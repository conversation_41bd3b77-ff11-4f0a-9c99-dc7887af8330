import json
import os
import datetime
import time 
import numpy as np
import pandas as pd

from utils.helpers import get_weekday


def daily_reports_get(account_id, adgroup_id, api_config, page, page_size, start_date, end_date):

    from api_utils.api_requests import get_daily_reports_adgroup

    return get_daily_reports_adgroup(
        account_id=account_id,
        adgroup_id=adgroup_id,
        api_config=api_config,
        page=page,
        page_size=page_size,
        start_date=start_date,
        end_date=end_date
    )


def hourly_reports_get(account_id, api_config, page, page_size, report_date, time_line = 'REQUEST_TIME'):
    """
    Get hourly reports for dynamic creative level.

    """
    from api_utils.api_requests import get_hourly_reports_dynamic_creative

    return get_hourly_reports_dynamic_creative(
        account_id=account_id,
        api_config=api_config,
        page=page,
        page_size=page_size,
        report_date=report_date,
        time_line=time_line
    )


def get_adgroups(account_ids, api_config, general_config):

    # access_tokens = [APIConfig.ACCESS_TOKEN] * len(account_ids)
    from api_utils.api_requests import get_adgroups_info

    print ('--- total %d accounts ...' % len(account_ids))

    out_dir = 'adgroups/'
    if os.path.exists(out_dir) == False:
        os.mkdir(out_dir)
    for account_id in account_ids:

        # if account_id != '********': continue

        print ('------------------------------------')
        # print ('account_id: ', account_id, ' access_token: ', access_token)
        print ('account_id: ', account_id,)
        # # get ads
        print ('adgroups/get ...')
        res = get_adgroups_info(account_id, api_config, 1, general_config.PAGE_SIZE_DEFAULT)
        if 'data' not in res.keys():
            print ('*** cannot get data ....')
            continue

        total_page = res['data']['page_info']['total_page']
        if total_page == 0:
            print ('*** page is 0 ....')
            continue
        for p in range(1, total_page + 1):
            res = get_adgroups_info(account_id, api_config, p, general_config.PAGE_SIZE_DEFAULT)
            try:
                if os.path.exists(out_dir + account_id) == False:
                    os.mkdir(out_dir + account_id)
                with open(out_dir + account_id + '/' + str(p) + '.json', 'w', encoding='utf8') as fp:
                    fp.write(json.dumps(res, indent=4, ensure_ascii=False))
            except Exception as e:
                print ('*** ', e)
        # break


def get_adgroup_report(account_ids=None):

    # 全量获取，每天一次
    from config.config import AccountConfig, APIConfig, APIConfig, PathConfig, GeneralConfig, AdGroupReportDataHeader
    
    if account_ids is None:
        account_ids = AccountConfig.ACCOUNT_IDS

    print ('--- start get adgroups for all accounts ...')
    get_adgroups(account_ids, APIConfig, GeneralConfig) # 获取全量广告

    print ('--- start get adgroup reports ...')
    adgroup_report_data = {}
    account_report_data = {}

    # Configuration values
    hist_range = GeneralConfig.HIST_RANGE

    for account_id in account_ids:
        # if account_id not in account_ids:
        #     continue
        account_last7_cost = 0
        account_last7_biz_follow_uv = 0
        account_last7_biz_reg_uv = 0
        account_last7_reservation_uv = 0
        account_last5_cost = 0
        account_last5_biz_follow_uv = 0
        account_last5_biz_reg_uv = 0
        account_last5_reservation_uv = 0
        account_last3_cost = 0
        account_last3_biz_follow_uv = 0
        account_last3_biz_reg_uv = 0
        account_last3_reservation_uv = 0
        account_total_reservation_uv = 0
        print ('--- account_id: ', account_id)
        if os.path.exists('adgroups/' + account_id) == False:
            continue
        for j_file in os.listdir('adgroups/' + account_id):
            data = json.load(open('adgroups/' + account_id + '/' + j_file, encoding='utf8'))
            if 'data' in data and 'list' in data['data']:
                if account_id not in adgroup_report_data:
                    adgroup_report_data[account_id] = {}
                    account_report_data[account_id] = {}
                data = data['data']['list']
                for d in data:
                    created_time = d['created_time']
                    created_time = datetime.datetime.fromtimestamp(created_time)
                    created_time = str(created_time).split(' ')[0]
                    first_day_begin_time = d['first_day_begin_time']
                    adgroup_id = d['adgroup_id']
                    adgroup_name = d['adgroup_name']
                    system_status = d['system_status']
                    today = str(datetime.datetime.now()).split(' ')[0]
                    if adgroup_id not in adgroup_report_data[account_id]:
                        adgroup_report_data[account_id][adgroup_id] = {}
                        adgroup_report_data[account_id][adgroup_id]['ds'] = today
                        adgroup_report_data[account_id][adgroup_id]['adgroup_name'] = adgroup_name # 广告名称
                        adgroup_report_data[account_id][adgroup_id]['created_time'] = created_time # 广告创建时间
                        adgroup_report_data[account_id][adgroup_id]['first_day_begin_time'] = first_day_begin_time # 广告开始投放第一天
                        adgroup_report_data[account_id][adgroup_id]['system_status'] = system_status # 广告状态
                        adgroup_report_data[account_id][adgroup_id]['report_data'] = {'ds':[], # 近期每天的投放数据
                                                                                        'cost':[],
                                                                                        'acquisition_cost':[],
                                                                                        'view_count':[],
                                                                                        'valid_click_count':[],
                                                                                        'biz_follow_uv':[], 
                                                                                        'biz_reg_uv':[], 
                                                                                        'reservation_uv':[]}
                        adgroup_report_data[account_id][adgroup_id]['is_ever_up'] = 0 # 是否曾经起量过
                        adgroup_report_data[account_id][adgroup_id]['last_up_date'] = '' # 是否曾经起量过
                        adgroup_report_data[account_id][adgroup_id]['last_up_cost'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_uv'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_uv'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_uv'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_cost'] = 0 # 最近一次起量成本
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_cost'] = 0 # 最近一次起量成本
                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = 0 # 最近一次起量成本
                        adgroup_report_data[account_id][adgroup_id]['life_long_cost'] = 0 # 生命周期总消耗
                        adgroup_report_data[account_id][adgroup_id]['life_long_biz_follow_uv'] = 0 # 生命周期总转化数
                        adgroup_report_data[account_id][adgroup_id]['life_long_biz_reg_uv'] = 0 # 生命周期总转化数
                        adgroup_report_data[account_id][adgroup_id]['life_long_reservation_uv'] = 0 # 生命周期总转化数
                        adgroup_report_data[account_id][adgroup_id]['life_long_days'] = 0 # 上线天数

                    
                    # past_14day = str(datetime.datetime.today() + datetime.timedelta(days = -hist_range)).split(' ')[0]
                    # past_5day = str(datetime.datetime.today() + datetime.timedelta(days = -5)).split(' ')[0]
                    # past_3day = str(datetime.datetime.today() + datetime.timedelta(days = -3)).split(' ')[0]
                    res = daily_reports_get(account_id, int(adgroup_id), APIConfig, 1, GeneralConfig.PAGE_SIZE_DEFAULT, created_time, today)
                    if 'data' not in res.keys():
                        print ('*** daily_reports_get cannot get data ....')
                        print (res)
                        exit()

                    adgroup_report_data[account_id][adgroup_id]['life_long_days'] = (datetime.datetime.strptime(today, "%Y-%m-%d") - datetime.datetime.strptime(created_time, "%Y-%m-%d")).days
                    print (adgroup_report_data[account_id][adgroup_id]['life_long_days'])


                    total_page = res['data']['page_info']['total_page']
                    if total_page == 0:
                        # print (res)
                        # print ('*** page is 0 ....')
                        continue

                    # print ('--- report date: ', report_date)
                    # print ('--- total %d pages ...' % total_page)
                    #adgroup_report_data[account_id][adgroup_id]['is_ever_up'] = 0 # 是否曾经起量过
                    #adgroup_report_data[account_id][adgroup_id]['last_up_date'] = '' # 是否曾经起量过
                    #adgroup_report_data[account_id][adgroup_id]['last_up_cost'] = 0 # 最近一次起量消耗
                    #adgroup_report_data[account_id][adgroup_id]['last_up_reservation_uv'] = 0 # 最近一次起量消耗
                    #adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = 0 # 最近一次起量成本
                    #adgroup_report_data[account_id][adgroup_id]['life_long_cost'] = 0 # 最近一次起量成本
                    #adgroup_report_data[account_id][adgroup_id]['life_long_days'] = 0 # 上线天数
                    for p in range(1, total_page + 1):
                        res = daily_reports_get(account_id, adgroup_id, APIConfig, p, GeneralConfig.PAGE_SIZE_DEFAULT, created_time, today)
                        if 'data' not in res.keys():
                            time.sleep(3)
                            res = daily_reports_get(account_id, adgroup_id, APIConfig, p, GeneralConfig.PAGE_SIZE_DEFAULT, created_time, today)
                        try:
                            data = res['data']['list']
                            # print (data[0])
                            # exit()
                            data = data[::-1] # 日期由大到小排序
                            day_count = 0
                            for d in data:
                                account_total_reservation_uv += d['reservation_uv']
                                if d['date'] == today:
                                    continue
                                if day_count == 14: # 统计满7天
                                    break
                                if get_weekday(d['date']) in  GeneralConfig.NO_AD_DAY_OF_WEEK and d['cost'] * 0.01 < 500:
                                    # 不在投放日
                                    continue
                                day_count += 1
                                adgroup_report_data[account_id][adgroup_id]['life_long_cost'] += d['cost'] * 0.01 # 自上线以来总消耗
                                adgroup_report_data[account_id][adgroup_id]['life_long_reservation_uv'] += d['reservation_uv']
                                if d['acquisition_cost'] * 0.01 > 200:
                                    adgroup_report_data[account_id][adgroup_id]['is_ever_up'] = 1
                                    adgroup_report_data[account_id][adgroup_id]['last_up_cost'] = d['cost'] * 0.01
                                    adgroup_report_data[account_id][adgroup_id]['last_up_date'] = d['date']
                                    adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_uv'] = d['biz_follow_uv']
                                    adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_uv'] = d['biz_reg_uv']
                                    adgroup_report_data[account_id][adgroup_id]['last_up_reservation_uv'] = d['reservation_uv']
                                    if d['biz_follow_uv']:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_cost'] = d['cost'] * 0.01/d['biz_follow_uv']
                                    else:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_cost'] = 0
                                    if d['biz_reg_uv']:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_cost'] = d['cost'] * 0.01/d['biz_reg_uv']
                                    else:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_cost'] = 0
                                    if d['reservation_uv']:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = d['cost'] * 0.01/d['reservation_uv']
                                    else:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = 0
                                # if d['date'] < past_14day:
                                #     continue
                                # if d['date'] >= past_3day:
                                if day_count <= 3:
                                    account_last3_cost += d['cost'] * 0.01
                                    account_last3_biz_follow_uv += d['biz_follow_uv']
                                    account_last3_biz_reg_uv += d['biz_reg_uv']
                                    account_last3_reservation_uv += d['reservation_uv']
                                # if d['date'] >= past_5day:
                                if day_count <= 5:
                                    account_last5_cost += d['cost'] * 0.01
                                    account_last5_biz_follow_uv += d['biz_follow_uv']
                                    account_last5_biz_reg_uv += d['biz_reg_uv']
                                    account_last5_reservation_uv += d['reservation_uv']
                                account_last7_cost += d['cost'] * 0.01
                                account_last7_biz_follow_uv += d['biz_follow_uv']
                                account_last7_biz_reg_uv += d['biz_reg_uv']
                                account_last7_reservation_uv += d['reservation_uv']
                                adgroup_report_data[account_id][adgroup_id]['report_data']['ds'].append(d['date'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['cost'].append(d['cost'] * 0.01)
                                adgroup_report_data[account_id][adgroup_id]['report_data']['acquisition_cost'].append(d['acquisition_cost'] * 0.01)
                                adgroup_report_data[account_id][adgroup_id]['report_data']['view_count'].append(d['view_count'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['valid_click_count'].append(d['valid_click_count'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['biz_follow_uv'].append(d['biz_follow_uv'])  
                                adgroup_report_data[account_id][adgroup_id]['report_data']['biz_reg_uv'].append(d['biz_reg_uv'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['reservation_uv'].append(d['reservation_uv'])       
                        except Exception as e:
                            print ('*** ', e) 
                            print (res)

                    # 去重周末不投放日期
                    if adgroup_report_data[account_id][adgroup_id]['life_long_days'] >= 5:
                        adgroup_report_data[account_id][adgroup_id]['is_new'] = 0
                    else:
                        today = datetime.datetime.today()
                        report_data = adgroup_report_data[account_id][adgroup_id]['report_data']
                        ds_list = report_data['ds']
                        cost_list = report_data['cost']
                        days_cnt = 0
                        for i in range(1, adgroup_report_data[account_id][adgroup_id]['life_long_days'] + 1):
                            ds = str(today + datetime.timedelta(-i)).split(' ')[0]
                            if ds in ds_list:
                                week_day = get_weekday(ds)
                                if week_day in GeneralConfig.NO_AD_DAY_OF_WEEK and cost_list[ds_list.index(ds)] < 500:
                                    continue
                            else:
                                week_day = get_weekday(ds)
                                if week_day in GeneralConfig.NO_AD_DAY_OF_WEEK:
                                    continue
                            days_cnt += 1
                        if days_cnt <= 2:
                            adgroup_report_data[account_id][adgroup_id]['is_new'] = 1
                        else:
                            adgroup_report_data[account_id][adgroup_id]['is_new'] = 0

                    adgroup_cost_sum = np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['cost'])
                    adgroup_biz_follow_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['biz_follow_uv']))
                    adgroup_biz_reg_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['biz_reg_uv']))
                    adgroup_reservation_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['reservation_uv']))
                    adgroup_report_data[account_id][adgroup_id]['total_cost'] = adgroup_cost_sum
                    adgroup_report_data[account_id][adgroup_id]['total_reservation_uv'] = adgroup_reservation_uv
                    if adgroup_biz_follow_uv:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_follow_cost'] = adgroup_cost_sum/adgroup_biz_follow_uv
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_follow_cost'] = 0
                    if adgroup_biz_reg_uv:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_reg_cost'] = adgroup_cost_sum/adgroup_biz_reg_uv
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_reg_cost'] = 0
                    if adgroup_reservation_uv:
                        adgroup_report_data[account_id][adgroup_id]['total_reservation_cost'] = adgroup_cost_sum/adgroup_reservation_uv
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_reservation_cost'] = 0
                   
                    adgroup_data_hist = pd.DataFrame(adgroup_report_data[account_id][adgroup_id]['report_data'])
                    # 前一天和前第三天表现
                    for delta_day in [1, 3, 5, 7, 14]:
                        past_1day = datetime.datetime.today() + datetime.timedelta(days = -delta_day)
                        past_1day = str(past_1day).split(' ')[0]
                        past_1data = adgroup_data_hist[adgroup_data_hist['ds']==past_1day]

                        adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day] = np.sum(past_1data['cost'])
                        adgroup_report_data[account_id][adgroup_id]['past_%dday_acquisition_cost' % delta_day] = np.sum(past_1data['acquisition_cost'])

                        past_1day_biz_follow_uv = np.sum(past_1data['biz_follow_uv'])
                        past_1day_biz_reg_uv = np.sum(past_1data['biz_reg_uv'])
                        past_1day_reservation_uv = np.sum(past_1data['reservation_uv'])

                        adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_follow_uv' % delta_day] = int(past_1day_biz_follow_uv)
                        adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_reg_uv' % delta_day] = int(past_1day_biz_reg_uv)
                        adgroup_report_data[account_id][adgroup_id]['past_%dday_reservation_uv' % delta_day] = int(past_1day_reservation_uv)

                        if adgroup_report_data[account_id][adgroup_id]['past_%dday_acquisition_cost' % delta_day] > 100:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_up' % delta_day] = 1
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_up' % delta_day] = 0
                        
                        if past_1day_biz_follow_uv:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_follow_cost' % delta_day] = \
                                adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day]/past_1day_biz_follow_uv
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_follow_cost' % delta_day] = 0
                        if past_1day_biz_reg_uv:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_reg_cost' % delta_day] = \
                                adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day]/past_1day_biz_reg_uv
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_reg_cost' % delta_day] = 0
                        if past_1day_reservation_uv:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_reservation_cost' % delta_day] = \
                                adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day]/past_1day_reservation_uv
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_reservation_cost' % delta_day] = 0

        account_report_data[account_id]['account_past_3day_cost'] = account_last3_cost
        account_report_data[account_id]['account_past_3day_biz_follow_uv'] = account_last3_biz_follow_uv
        if account_last3_biz_follow_uv:
            account_report_data[account_id]['account_past_3day_biz_follow_cost'] = account_last3_cost/account_last3_biz_follow_uv
        else:
            account_report_data[account_id]['account_past_3day_biz_follow_cost'] = account_last3_cost
        account_report_data[account_id]['account_past_3day_biz_reg_uv'] = account_last3_biz_reg_uv
        if account_last3_reservation_uv:
            account_report_data[account_id]['account_past_3day_biz_reg_cost'] = account_last3_cost/account_last3_biz_reg_uv
        else:
            account_report_data[account_id]['account_past_3day_biz_reg_cost'] = account_last3_cost
        account_report_data[account_id]['account_past_3day_reservation_uv'] = account_last3_reservation_uv
        if account_last3_reservation_uv:
            account_report_data[account_id]['account_past_3day_reservation_cost'] = account_last3_cost/account_last3_reservation_uv
        else:
            account_report_data[account_id]['account_past_3day_reservation_cost'] = account_last3_cost

        account_report_data[account_id]['account_past_5day_cost'] = account_last5_cost
        account_report_data[account_id]['account_past_5day_biz_follow_uv'] = account_last5_biz_follow_uv
        if account_last5_biz_follow_uv:
            account_report_data[account_id]['account_past_5day_biz_follow_cost'] = account_last5_cost/account_last5_biz_follow_uv
        else:
            account_report_data[account_id]['account_past_5day_biz_follow_cost'] = account_last5_cost
        account_report_data[account_id]['account_past_5day_biz_reg_uv'] = account_last5_biz_reg_uv
        if account_last5_reservation_uv:
            account_report_data[account_id]['account_past_5day_biz_reg_cost'] = account_last5_cost/account_last5_biz_reg_uv
        else:
            account_report_data[account_id]['account_past_5day_biz_reg_cost'] = account_last5_cost
        account_report_data[account_id]['account_past_5day_reservation_uv'] = account_last5_reservation_uv
        if account_last5_reservation_uv:
            account_report_data[account_id]['account_past_5day_reservation_cost'] = account_last5_cost/account_last5_reservation_uv
        else:
            account_report_data[account_id]['account_past_5day_reservation_cost'] = account_last5_cost

        account_report_data[account_id]['account_past_7day_cost'] = account_last7_cost
        account_report_data[account_id]['account_past_7day_biz_follow_uv'] = account_last3_biz_follow_uv
        if account_last3_biz_follow_uv:
            account_report_data[account_id]['account_past_7day_biz_follow_cost'] = account_last3_cost/account_last3_biz_follow_uv
        else:
            account_report_data[account_id]['account_past_7day_biz_follow_cost'] = account_last7_cost
        account_report_data[account_id]['account_past_7day_biz_reg_uv'] = account_last3_biz_reg_uv
        if account_last3_reservation_uv:
            account_report_data[account_id]['account_past_7day_biz_reg_cost'] = account_last3_cost/account_last3_biz_reg_uv
        else:
            account_report_data[account_id]['account_past_7day_biz_reg_cost'] = account_last7_cost
        account_report_data[account_id]['account_past_7day_reservation_uv'] = account_last7_reservation_uv
        if account_last7_reservation_uv:
            account_report_data[account_id]['account_past_7day_reservation_cost'] = account_last7_cost/account_last7_reservation_uv
        else:
            account_report_data[account_id]['account_past_7day_reservation_cost'] = account_last7_cost

        account_report_data[account_id]['account_total_reservation_uv'] = account_total_reservation_uv

    with open(PathConfig.ADGROUP_REPORT_PATH, 'w', encoding = 'utf8') as f:
        json.dump(adgroup_report_data, f, indent=4, ensure_ascii=False)

    with open(PathConfig.ACCOUNT_REPORT_PATH, 'w', encoding = 'utf8') as f:
        json.dump(account_report_data, f, indent=4, ensure_ascii=False)


def get_adcreative_hourly_report(account_config, api_config, general_config, time_line = 'REQUEST_TIME'):

    accounts = account_config.ACCOUNT_IDS
    token = api_config.ACCESS_TOKEN
    access_tokens = [token] * len(accounts)
    select_accounts = accounts

    now = str(datetime.datetime.now())
    today = now.split(' ')[0]
    this_year = int(today.split('-')[0])
    start_month = 12
    start_day = 1
    end_month = int(today.split('-')[1])
    end_day = int(today.split('-')[2])
    time_range_start = datetime.datetime(2024, start_month, start_day)
    time_range_end = datetime.datetime(this_year, end_month, end_day)
    time_range_days = (time_range_end - time_range_start).days
    print ('time_range_start: ', time_range_start)
    print ('time_range_end: ', time_range_end)
    print ('--- time_range_days: ', time_range_days)

    for i, (account_id, access_token) in enumerate(zip(select_accounts, access_tokens)):

        try:

            # if os.path.exists('data/hourly_reports_dynamic_creative_data/%s.csv' % account_id) == True:
            #     continue

            # if account_id not in select_accounts:
            #     continue
            
            print ('---------------- %d / %d --------------------' % (i, len(accounts)))
            print ('account_id: ', account_id, ' access_token: ', access_token)
            print ('adcreative hourly_reports/get %s ...' % time_line)

            ad_report_data = {'account_id':[],
                    'adgroup_id':[],
                    'dynamic_creative_id':[], 
                    'ds':[],
                    'hour':[],
                    'cost':[],
                    'acquisition_cost':[],
                    'view_count':[],
                    'valid_click_count':[],
                    'biz_follow_uv': [],
                    'biz_follow_cost': [],
                    'biz_reg_uv': [],
                    'reg_cost_pla': [],
                    'page_reservation_cost_with_people': [],
                    'reservation_uv':[]}

            # if i < 17:
            #     continue
            # if account_id != '********':
            #     continue
            has_hist = 0

            if os.path.exists('data_dynamic_creative_level') == False:
                os.mkdir('data_dynamic_creative_level')
            if os.path.exists('data_dynamic_creative_level/hourly_reports_%s/' % time_line) == False:
                os.mkdir('data_dynamic_creative_level/hourly_reports_%s/' % time_line)
            
            if os.path.exists('data_dynamic_creative_level/hourly_reports_%s/%s.csv' % (time_line, account_id)) == True:
                hist_data = pd.read_csv('data_dynamic_creative_level/hourly_reports_%s/%s.csv' % (time_line, account_id), encoding='utf8')
                if len(hist_data) > 0:
                    ds_list = list(set(hist_data['ds']))
                    ds_list.sort()
                    last_date = ds_list[-1].split('-')
                    start_date = datetime.datetime(int(last_date[0]), int(last_date[1]), int(last_date[2])) + datetime.timedelta(days=1)
                    start_date = str(start_date).split(' ')[0].split('-')
                    start_year = int(start_date[0])
                    start_month = int(start_date[1])
                    start_day = int(start_date[2])
                    time_range_start = datetime.datetime(start_year, start_month, start_day)
                    time_range_end = datetime.datetime(this_year, end_month, end_day)
                    time_range_days = (time_range_end - time_range_start).days
                    if time_range_days == 0:
                        if today not in ds_list:
                            time_range_days = 1
                        has_hist = 1
                    print ('数据拉取时间: ', start_date, start_month, start_day, ' --- ', time_range_end, end_month, end_day)

            print ('--- time_range_start: ', time_range_start, time_range_days)
 
            for i in range(time_range_days):

                report_date = time_range_start + datetime.timedelta(days=i)
                report_date = str(report_date).split(' ')[0]
                print ('--- report date: ', report_date)
                
                try:
                    res = hourly_reports_get(account_id, api_config, 1, general_config.PAGE_SIZE_LARGE, report_date)
                except:
                    time.sleep(1)
                    res = hourly_reports_get(account_id, api_config, 1, general_config.PAGE_SIZE_LARGE, report_date)
                
                if 'data' not in res.keys():
                    print ('*** cannot get data ....')
                    print (res)
                    break

                total_page = res['data']['page_info']['total_page']
                if total_page == 0:
                    #print ('*** page is 0 ....')
                    continue
                
                print ('--- %s total %d pages ...' % (report_date, total_page))

                for p in range(1, total_page + 1):
                    try:
                        res = hourly_reports_get(account_id, api_config, p, general_config.PAGE_SIZE_LARGE, report_date, time_line)
                    except:
                        time.sleep(1)
                        res = hourly_reports_get(account_id, api_config, p, general_config.PAGE_SIZE_LARGE, report_date, time_line)

                    if 'data' not in res.keys():
                        time.sleep(1)
                        res = hourly_reports_get(account_id, api_config, p, general_config.PAGE_SIZE_LARGE, report_date, time_line)
                    try:
                        data = res['data']['list']
                        # print (data[0])
                        # exit()
                        for d in data:
                            ad_report_data['account_id'].append(account_id)
                            ad_report_data['adgroup_id'].append(d['adgroup_id'])
                            ad_report_data['dynamic_creative_id'].append(d['dynamic_creative_id'])
                            ad_report_data['ds'].append(d['date'])
                            ad_report_data['hour'].append(d['hour'])
                            ad_report_data['cost'].append(d['cost'])
                            ad_report_data['acquisition_cost'].append(d['acquisition_cost'])
                            ad_report_data['view_count'].append(d['view_count'])
                            ad_report_data['valid_click_count'].append(d['valid_click_count'])
                            ad_report_data['reservation_uv'].append(d['reservation_uv'])
                            ad_report_data['biz_follow_uv'].append(d['biz_follow_uv'])
                            ad_report_data['biz_follow_cost'].append(d['biz_follow_cost'])
                            ad_report_data['biz_reg_uv'].append(d['biz_reg_uv'])
                            ad_report_data['reg_cost_pla'].append(d['reg_cost_pla'])
                            ad_report_data['page_reservation_cost_with_people'].append(d['page_reservation_cost_with_people'])
                            
                    except Exception as e:
                        print ('*** ', e) 
                time.sleep(1)
                
                
            ad_report_data = pd.DataFrame(ad_report_data)
            
            if len(ad_report_data) == 0:
                continue

            if has_hist:
                for col in hist_data.columns:
                    if 'Unnamed' in col:
                        hist_data = hist_data.drop([col], axis = 1)
                ad_report_data = pd.concat([hist_data, ad_report_data], axis = 0)

            # ad_report_data.to_csv('data_dynamic_creative_level/hourly_reports/%s.csv' % account_id)
            # if os.path.exists('data_dynamic_creative_level/hourly_reports' % (start_month, start_day, end_month, end_day)) == False:
            #     os.mkdir('data_dynamic_creative_level/hourly_reports-2024%02d%02d-2024%02d%02d' % (start_month, start_day, end_month, end_day))
            if os.path.exists('data_dynamic_creative_level/hourly_reports_%s' % time_line) == False:
                os.mkdir('data_dynamic_creative_level/hourly_reports_%s' % time_line)
            ad_report_data.to_csv('data_dynamic_creative_level/hourly_reports_%s/%s.csv' % (time_line, account_id))
            # exit()
        except Exception as e:
            print ('--- account ERROR: ', e)
            continue