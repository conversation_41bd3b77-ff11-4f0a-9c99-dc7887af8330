import datetime 
import json


def stop_ads_for_ndays(select_account_id, adgroup_status, account_config, string_format, days = 1):
    # for 人工控停
    
    print ('--- 所有广告改到第二天投放 ...') # 每周五晚暂停没有消耗广告
    res = {}
    # adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_ids = adgroup_status.keys()
        
        select_up_adgroup_ids = []
        active_num = 0
        for adgroup_id in adgroup_ids:
            # if adgroup_id not in adgroup_status:
            #     print ('--- %s adgroup status not found ...' % str(adgroup_id))
            #     continue
            
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in account_config.ACTIVE_STATUS + [account_config.NOT_IN_DELIVERY_STATUS]: #, 'ADGROUP_STATUS_ACCOUNT_BALANCE_NOT_ENOUGH']:
                continue

            active_num += 1
            msg = '当天停投'
            print (adgroup_id, msg)
            new_delivery_date = str(datetime.datetime.today() + datetime.timedelta(days = days)).split(' ')[0]
            # select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
            select_up_adgroup_ids.append([f'{string_format.UPDATE_DELIVERY_DATE}-%s' % new_delivery_date, account_id, adgroup_id, msg])
        print ('--- active num: ', active_num)
        res[account_id] = select_up_adgroup_ids

    print (res)

    return res


def check_if_restart_again(select_account_id, adgroup_status, control_config, account_config, target_config, path_config, string_format, scale_ratio = 1):
    # 配置
    follow_final_cost_goal = int(target_config.STEP_1_TARGET * scale_ratio)
    adgroup_follow_cost_limit = follow_final_cost_goal * control_config.STEP_1_COST_LIMIT_SCALE
    register_final_cost_goal = int(target_config.STEP_2_TARGET * scale_ratio)
    adgroup_register_cost_limit = register_final_cost_goal * control_config.STEP_2_COST_LIMIT_SCALE
    reservation_final_cost_goal = int(target_config.STEP_3_TARGET * scale_ratio) 
    adgroup_reservation_cost_limit = reservation_final_cost_goal * control_config.STEP_3_COST_LIMIT_SCALE

    print ('--- 目标关注成本： ', follow_final_cost_goal)
    print ('--- 目标注册成本： ', register_final_cost_goal)
    print ('--- 目标表单预约成本： ', reservation_final_cost_goal)

    print ('--- 关注成本限制： ', adgroup_follow_cost_limit)
    print ('--- 目标注册成本限制： ', adgroup_register_cost_limit)
    print ('--- 目标表单预约成本限制： ', adgroup_reservation_cost_limit)
    
    now = datetime.datetime.now()
    today = str(now).split(' ')[0]
    hour = int(str(now).split(' ')[1].split(':')[0])
    miniute = int(str(now).split(' ')[1].split(':')[1])
    
    print ('--- 检查是否当天重新投放 ...')
    print ('--- 当前时间：', today, hour, miniute)
    # 当天一件起量过的晚上6点之后判断, 当天没有起量过的, 每10分钟扫描
    res = {}
    adgroup_report_data = json.load(open(path_config.ADGROUP_REPORT_PATH, encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)

        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in [account_config.NOT_IN_DELIVERY_STATUS]:
                continue

            print ('*** %d 当前数据 ...' % int(adgroup_id))
            msg = '--- 当天关注数 %d, 当天消耗 %.2f 当天关注成本 %.2f > %d' % \
                        (data['today_biz_follow_uv'], data['today_cost'], data['today_biz_follow_cost'], adgroup_follow_cost_limit)
            print (msg)
            msg = '--- 当天注册数 %d, 当天消耗 %.2f 当天注册成本 %.2f > %d' % \
                        (data['today_biz_reg_uv'], data['today_cost'], data['today_biz_reg_cost'], adgroup_register_cost_limit)
            print (msg)
            msg = '--- 当天表单预约数 %d, 当天消耗 %.2f 当天表单预约成本 %.2f > %d' % \
                        (data['today_reservation_uv'], data['today_cost'], data['today_reservation_cost'], adgroup_reservation_cost_limit)
            print (msg)

            reservation_up_mark = 0
            register_up_mark = 0
            follow_up_mark = 0
            if data['today_reservation_uv'] > 0 and data['today_reservation_cost'] < adgroup_reservation_cost_limit \
                or data['today_reservation_uv'] == 0 and data['today_cost'] < adgroup_reservation_cost_limit:
                reservation_up_mark = 1
            if data['today_biz_reg_uv'] > 0 and data['today_biz_reg_cost'] < adgroup_register_cost_limit or \
                data['today_biz_reg_uv'] == 0 and data['today_cost'] < adgroup_register_cost_limit:
                register_up_mark = 1
            if data['today_biz_follow_uv'] > 0 and data['today_biz_follow_cost'] < adgroup_follow_cost_limit:
                follow_up_mark = 1

            print (follow_up_mark, register_up_mark, reservation_up_mark)

            if follow_up_mark and register_up_mark and reservation_up_mark:
                msg = '当天重启: 当天关注数 %d, 当天消耗 %.2f 当天关注成本 %.2f < %d' % \
                        (data['today_biz_follow_uv'], data['today_cost'], data['today_biz_follow_cost'], adgroup_follow_cost_limit)
                msg = '当天重启: 当天注册数 %d, 当天消耗 %.2f 当天注册成本 %.2f < %d' % \
                        (data['today_biz_reg_uv'], data['today_cost'], data['today_biz_reg_cost'], adgroup_register_cost_limit)
                msg = '当天重启: 当天表单预约数 %d, 当天消耗 %.2f 当天表单预约成本 %.2f < %d' % \
                        (data['today_reservation_uv'], data['today_cost'], data['today_reservation_cost'], adgroup_reservation_cost_limit)
                select_up_adgroup_ids.append([f'{string_format.UPDATE_DELIVERY_DATE}-%s' % today, account_id, adgroup_id, '三阶段成本全部达标(目标值2倍)...'])

        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res


def check_if_over_reg_reservation_rate(select_account_id, adgroup_status, path_config, account_config, data_header, string_format, scale_ratio = 1):
    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    hour = int(str(now).split(' ')[1].split(':')[0])
    miniute = int(str(now).split(' ')[1].split(':')[1])
    
    print ('--- 注册下单率 ...')
    print ('--- 当前时间：', day, hour, miniute)
    # 当天一件起量过的晚上6点之后判断, 当天没有起量过的, 每10分钟扫描
    res = {}
    adgroup_report_data = json.load(open(path_config.ADGROUP_REPORT_PATH, encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in account_config.ACTIVE_STATUS + [account_config.NOT_IN_DELIVERY_STATUS]:
                continue

            report_data = data['report_data']
            if len(report_data['ds']) == 0:
                continue

            total_reg_uv = 0
            total_reservation_uv = 0
            for reg, reserve in zip(report_data[data_header.JOIN_CHAR.join([data_header.STEP_2_STAT_NAME,  data_header.UV_POSTFIX])],
                                    report_data[data_header.JOIN_CHAR.join([data_header.STEP_3_STAT_NAME,  data_header.UV_POSTFIX])]):
                total_reg_uv += reg
                total_reservation_uv += reserve
            
            if total_reg_uv > 0:
                ratio = total_reservation_uv/total_reg_uv
            else:
                ratio = 0

            print ('*** %d 近7天数据 ...' % int(adgroup_id))
            msg = '--- 注册数 %d 表单数 %d ratio: %.2f' % \
                        (total_reg_uv, total_reservation_uv, ratio)
            print (msg)

            one_month = str(datetime.datetime.today() + datetime.timedelta(days = 30)).split(' ')[0]
            if ratio > 0 and ratio < 0.35:
                msg = '注册转化率太低 ' + str(ratio)
                print (msg)
                select_up_adgroup_ids.append([f'{string_format.UPDATE_DELIVERY_DATE}%s' % one_month, account_id, adgroup_id, msg])

        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res


def check_if_low_cost(select_account_id, adgroup_status, account_config, target_config, path_config, string_format, scale_ratio = 2):
    
    # 配置
    follow_final_cost_goal = int(target_config.STEP_1_TARGET * scale_ratio)
    # register_final_cost_goal = int(target_config.STEP_2_TARGET * scale_ratio)
    # reservation_final_cost_goal = int(target_config.STEP_3_TARGET * scale_ratio)
    
    print ('--- 低消 ...') # 每晚6点开始
    res = {}
    adgroup_report_data = json.load(open(path_config.ADGROUP_REPORT_PATH, encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            data = adgroup_data[adgroup_id]
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in account_config.ACTIVE_STATUS:
                continue

            # ds_list = data['report_data']['ds']
            # if len(ds_list) > 0: # 只作用于第一天
            #     continue
            tomorrow = str(datetime.datetime.today() + datetime.timedelta(days = 1)).split(' ')[0]
            if data['today_reservation_uv'] == 0 and data['today_cost'] < 100:
                msg = '低消: 当天转化数 =0, 当天总消耗 %.2f <100' % (data['today_cost'])
                print (msg)
                select_up_adgroup_ids.append([f'{string_format.UPDATE_DELIVERY_DATE}-%s' % tomorrow, account_id, adgroup_id, msg])
            else:
                if data['past_1hour_cost'] < follow_final_cost_goal: # 过去一个小时消耗低
                    msg = '低消: 前一个小时消耗 %.2f < %d' % (data['past_1hour_cost'], follow_final_cost_goal)
                    print (msg)
                    select_up_adgroup_ids.append([f'{string_format.UPDATE_DELIVERY_DATE}-%s' % tomorrow, account_id, adgroup_id, msg])

        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res
