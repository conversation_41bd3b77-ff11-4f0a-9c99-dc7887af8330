import datetime
import random
from utils.helpers import get_weekday


def get_account_create_time(account_config, create_config):
    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    week_day = get_weekday(day)
    create_time_list = []
    """if week_day == '星期日':
        hour_list = [6, 7]
    else:
        hour_list = [14, 15]
    """
    if week_day in create_config.SPECIAL_CREATE_HOUR_DAY_OF_WEEK:
        hour_list = create_config.CREATE_HOUR_FOR_SPECIAL_DAY_OF_WEEK
    else:
        hour_list = create_config.CREATE_HOUR_FOR_WEEKDAY
    for hour in hour_list:
        for minitue in range(0, 60, 5):
            create_time_list.append('%02d-%02d' % (hour, minitue))
    random.shuffle(create_time_list)
    account_create_time = {}
    cnt = 0
    for account_id in account_config.OLD_ACCOUNTS + account_config.MEDIAN_ACCOUNTS + account_config.NEW_ACCOUNTS:
        account_create_time[account_id] = create_time_list[cnt % len(create_time_list)]
        cnt += 1
    print(account_create_time)
    return account_create_time
