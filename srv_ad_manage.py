import os, sys
import json
import random
import requests
import time
import urllib
import numpy as np
import cv2
import datetime
import calendar
import pandas as pd
import tornado.ioloop
import tornado.template
import tornado.web
from tornado.concurrent import run_on_executor
from concurrent.futures import ThreadPoolExecutor
from concurrent.futures import ProcessPoolExecutor
import logging
from xgboost.sklearn import XGBClassifier

logging.getLogger("tornado.access").propagate = False
logging.getLogger("tornado.access").disabled = True

def url_to_image(url):
    resp = urllib.request.urlopen(url)
    image = np.asarray(bytearray(resp.read()), dtype = "uint8")
    image = cv2.imdecode(image, cv2.IMREAD_COLOR)
    return image

def get_weekday(date_string):
    weekday_dict = {
        0: "星期一",
        1: "星期二",
        2: "星期三",
        3: "星期四",
        4: "星期五",
        5: "星期六",
        6: "星期日"
    }
    date = calendar.weekday(int(date_string.split("-")[0]), int(date_string.split("-")[1]), int(date_string.split("-")[2]))
    return weekday_dict[date]

# -- 账号分层：
# 1. 老账户：********、
# 2. 中等账户：********（暂定）、********、********
# 3. 新账户：********、********、（********、********、********、********、********）      （********、********、********、********、********）
# 4. 光明专属：********、********
# 5. AI素材测试账号： ********、********
# 6. 100%回传账号：********、********
# 7.图片素材测试账号：额外申请2号

year_target = 210 #250 # 2025年业务目标； 2024年：220
follow_target = 350 # 关注成本
register_target = 800 # 注册成本
reservation_target = 6000 # 表单预约成本

# old_accounts = ['********']
# median_accounts = ['********', '********', '********']
# new_accounts = ['********', '********', '********',\
#                     '********', '********', '********', '********', '********',\
#                         '********', '********', '********', '********']


# old_accounts = ['********']
# median_accounts = ['********', '********', '********', '********', '********']
# new_accounts = ['********', '********', '********', '********', \
#                     '********', '********', '********', '********','********', '********']

# ******** 调整
old_accounts = []
median_accounts = []
new_accounts = ['********','********'] #['********', '********']

one_hundred_account = [] # 100% 回传账号
one_third_account = []

two_small_up_account = [] #['********']
two_ups_account = []
# 素材分类账号: ********(智云众), ********(优矩)

# morning_accounts = old_accounts + median_accounts + new_accounts
# afternoon_accounts = ['********', '********', '********', '********', '********']
afternoon_accounts = [] #['********']

control_exclude_accounts = [] #['********', '********','********']
create_exclude_accounts = []
# up_exclude_accounts = ['********', '********', '********', '********', '********', '********'] # ******** PID控价
up_exclude_accounts = [] #['********', '********'] # # ******** Update
over_budget_exclude_accounts = []
low_cost_exclude_accounts = []

account_ids = old_accounts + median_accounts + new_accounts

lines = open('get_data/全量非朋账号.txt', encoding='utf8').readlines()
feipeng_accounts = []
for line in lines:
    L = line.strip().split()
    if L[0] not in feipeng_accounts:
        feipeng_accounts.append(int(L[0]))

# stop_accounts = ['********', '********', '********', '********', '********', '********', '********', '********', \
#                  '********', '********', '********', '********']

# stop_accounts = ['********', '********', '********', '********'] + new_accounts
youju_accounts = []
stop_accounts = []
# stop_accounts = old_accounts + median_accounts + new_accounts

def get_account_up_time():
    up_time_list = []
    for hour in [8, 9]:
        for minitue in [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]:
            up_time_list.append('%02d-%02d' % (hour, minitue))
    random.shuffle(up_time_list)
    account_up_time = {}
    cnt = 0
    for account_id in old_accounts + median_accounts + new_accounts:
        if account_id in afternoon_accounts:
            continue
        account_up_time[account_id] = up_time_list[cnt]
        cnt += 1
    print (account_up_time)
    return account_up_time

def get_account_create_time():
    
    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    week_day = get_weekday(day)
    create_time_list = []
    if week_day == '星期日':
        hour_list = [6, 7]
    else:
        hour_list = [14, 15]
    for hour in hour_list:
        for minitue in [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]:
            create_time_list.append('%02d-%02d' % (hour, minitue))
    random.shuffle(create_time_list)
    account_create_time = {}
    cnt = 0
    for account_id in old_accounts + median_accounts + new_accounts:
        account_create_time[account_id] = create_time_list[cnt % len(create_time_list)]
        cnt += 1
    print (account_create_time)
    return account_create_time

# ZYZ_no_offer = ['********', '********', '********'] #智云众
# ZYZ_SKU = ['********']

# YJ_no_offer = ['********', '********', '********', '********'] #优矩
# YJ_SKU = ['********']

# account_ids = ZYZ_no_offer + ZYZ_SKU + YJ_no_offer + YJ_SKU
access_token = '66c1748bdca5e0016250f3cdd4cdaa4f' # 微业贷token

# 配置
hist_range = 7 # 历史数据观测时间
up_all_num_for_new = 1 # 冷启阶段新账户只起量一个
up_all_num_for_median = 2 # 中等账户起量两个
up_all_num_for_old = 3 # 度过冷启阶段账户可起量三个

def adgroups_get(account_id, access_token, page, page_size):

    interface = 'adgroups/get'
    # url = 'https://api.e.qq.com/v1.1/' + interface
    url = 'https://api.e.qq.com/v3.0/' + interface

    nonce = str(time.time()) + str(random.randint(0, 999999))
    # print ('nonce: ', nonce)

    common_parameters = {
            'access_token': access_token, 
            'timestamp': int(time.time()), 
            'nonce': nonce,
        }

    parameters = {
            "account_id": account_id,
            "page": page,
            "page_size": page_size,
            "is_deleted": False,
            'fields':["adgroup_id","adgroup_name",'created_time', 'last_modified_time', 'begin_date', 'system_status','first_day_begin_time', 'end_date', 'billing_event', 'bid_amount', 'total_budget', 'daily_budget', 'scene_spec', \
                'optimization_goal', 'targeting_translation', 'ad_count', 'bid_strategy', 'cold_start_audience', \
                'expand_enabled', 'smart_bid_type', 'smart_cost_cap', 'custom_adgroup_tag', 'flow_optimization_enabled', \
                'promoted_object_type', 'targeting_id', 'targeting', 'bid_strategy', 'cold_start_audience', 'is_deleted']
        }

    parameters.update(common_parameters)
    for k in parameters:
        if type(parameters[k]) is not str:
            parameters[k] = json.dumps(parameters[k])

    r = requests.get(url, params = parameters)

    return r.json()

def get_adgroups(account_ids):

    access_tokens = ['66c1748bdca5e0016250f3cdd4cdaa4f'] * len(account_ids)

    print ('--- total %d accounts ...' % len(account_ids))

    out_dir = 'adgroups/'
    if os.path.exists(out_dir) == False:
        os.mkdir(out_dir)
    for (account_id, access_token) in zip(account_ids, access_tokens):

        # if account_id != '24441417': continue

        print ('------------------------------------')
        print ('account_id: ', account_id, ' access_token: ', access_token)

        # # get ads
        print ('adgroups/get ...')
        res = adgroups_get(account_id, access_token, 1, 100)
        if 'data' not in res.keys():
            print ('*** cannot get data ....')
            continue

        # record = res['data']['list'][0]
        # for k in record.keys():
        #     print (k, ' '*(30-len(k)), record[k])
        # break

        # # continue

        total_page = res['data']['page_info']['total_page']
        if total_page == 0:
            print ('*** page is 0 ....')
            continue
        # print ('--- total %d pages ...' % total_page)
        for p in range(1, total_page + 1):
            res = adgroups_get(account_id, access_token, p, 100)
            try:
                if os.path.exists(out_dir + account_id) == False:
                    os.mkdir(out_dir + account_id)
                with open(out_dir + account_id + '/' + str(p) + '.json', 'w', encoding='utf8') as fp:
                    fp.write(json.dumps(res, indent=4, ensure_ascii=False))
            except Exception as e:
                print ('*** ', e)
        # break

def hourly_reports_get(account_id, access_token, page, page_size, report_date, time_line = 'REQUEST_TIME'):

    interface = 'hourly_reports/get'
    # url = 'https://api.e.qq.com/v1.1/' + interface
    url = 'https://api.e.qq.com/v3.0/' + interface

    common_parameters = {
            'access_token': access_token, 
            'timestamp': int(time.time()), 
            'nonce': str(time.time()) + str(random.randint(0, 999999)),
        }

    start_date = report_date.split('-') #time_range.split('-')[0].split('.')
    end_date = report_date.split('-') #time_range.split('-')[1].split('.')
    # print ('--- start date: ', start_date)
    # print ('--- end date: ', end_date)

    parameters = {
            "account_id": account_id,
            "level": "REPORT_LEVEL_DYNAMIC_CREATIVE", #"REPORT_LEVEL_DYNAMIC_CREATIVE", #"REPORT_LEVEL_ADGROUP",
            "date_range": 
            {
                "start_date": "%d-%02d-%02d" % (int(start_date[0]), int(start_date[1]), int(start_date[2])), #"2022-05-28",
                "end_date": "%d-%02d-%02d" % (int(end_date[0]), int(end_date[1]), int(end_date[2])) #"2022-08-18"
            },
            "group_by": 
            [
                "date", "adgroup_id","dynamic_creative_id","hour", 
         
            ],
            "time_line": time_line, #'REPORTING_TIME',
            "page": page,
            "page_size": page_size,
            "fields": ['adgroup_id', 'dynamic_creative_id', 'date','hour','view_count', 'valid_click_count', \
                       'cost', 'acquisition_cost', 'cpc', 'thousand_display_price', \
                       'reservation_uv', 'biz_follow_uv','biz_follow_cost', 'biz_reg_uv', \
                       'reg_cost_pla', 'page_reservation_cost_with_people']
        }

    parameters.update(common_parameters)
    for k in parameters:
        if type(parameters[k]) is not str:
            parameters[k] = json.dumps(parameters[k])

    r = requests.get(url, params = parameters)

    return r.json()

def get_adcreative_hourly_report(time_line = 'REQUEST_TIME'):

    # res = daily_reports_get('********', '042afe0294d41f0b11c2a91f437fbb10', 1, 100, '2024.03.01-2024.03.18')
    # lines = open('Q_公众号_accounts.txt', encoding='utf8').readlines()
    # accounts = [l.strip().split(' ')[0] for l in lines]
    # accounts = [********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********]
    
    accounts = old_accounts + median_accounts + new_accounts
    token = '66c1748bdca5e0016250f3cdd4cdaa4f' #'acf84e8507564a95034efd94b4d412e7'
    access_tokens = [token] * len(accounts)
    select_accounts = accounts

    now = str(datetime.datetime.now())
    today = now.split(' ')[0]
    this_year = int(today.split('-')[0])
    start_month = 12
    start_day = 1
    end_month = int(today.split('-')[1])
    end_day = int(today.split('-')[2])
    time_range_start = datetime.datetime(2024, start_month, start_day)
    time_range_end = datetime.datetime(this_year, end_month, end_day)
    time_range_days = (time_range_end - time_range_start).days
    print ('time_range_start: ', time_range_start)
    print ('time_range_end: ', time_range_end)
    print ('--- time_range_days: ', time_range_days)

    for i, (account_id, access_token) in enumerate(zip(select_accounts, access_tokens)):

        try:

            # if os.path.exists('data/hourly_reports_dynamic_creative_data/%s.csv' % account_id) == True:
            #     continue

            # if account_id not in select_accounts:
            #     continue
            
            print ('---------------- %d / %d --------------------' % (i, len(accounts)))
            print ('account_id: ', account_id, ' access_token: ', access_token)
            print ('adcreative hourly_reports/get %s ...' % time_line)

            ad_report_data = {'account_id':[],
                    'adgroup_id':[],
                    'dynamic_creative_id':[], 
                    'ds':[],
                    'hour':[],
                    'cost':[],
                    'acquisition_cost':[],
                    'view_count':[],
                    'valid_click_count':[],
                    'biz_follow_uv': [],
                    'biz_follow_cost': [],
                    'biz_reg_uv': [],
                    'reg_cost_pla': [],
                    'page_reservation_cost_with_people': [],
                    'reservation_uv':[]}

            # if i < 17:
            #     continue
            # if account_id != '********':
            #     continue
            has_hist = 0

            if os.path.exists('data_dynamic_creative_level') == False:
                os.mkdir('data_dynamic_creative_level')
            if os.path.exists('data_dynamic_creative_level/hourly_reports_%s/' % time_line) == False:
                os.mkdir('data_dynamic_creative_level/hourly_reports_%s/' % time_line)
            
            if os.path.exists('data_dynamic_creative_level/hourly_reports_%s/%s.csv' % (time_line, account_id)) == True:
                hist_data = pd.read_csv('data_dynamic_creative_level/hourly_reports_%s/%s.csv' % (time_line, account_id), encoding='utf8')
                if len(hist_data) > 0:
                    ds_list = list(set(hist_data['ds']))
                    ds_list.sort()
                    last_date = ds_list[-1].split('-')
                    start_date = datetime.datetime(int(last_date[0]), int(last_date[1]), int(last_date[2])) + datetime.timedelta(days=1)
                    start_date = str(start_date).split(' ')[0].split('-')
                    start_year = int(start_date[0])
                    start_month = int(start_date[1])
                    start_day = int(start_date[2])
                    time_range_start = datetime.datetime(start_year, start_month, start_day)
                    time_range_end = datetime.datetime(this_year, end_month, end_day)
                    time_range_days = (time_range_end - time_range_start).days
                    if time_range_days == 0:
                        if today not in ds_list:
                            time_range_days = 1
                        has_hist = 1
                    print ('数据拉取时间: ', start_date, start_month, start_day, ' --- ', time_range_end, end_month, end_day)

            print ('--- time_range_start: ', time_range_start, time_range_days)
 
            for i in range(time_range_days):

                report_date = time_range_start + datetime.timedelta(days=i)
                report_date = str(report_date).split(' ')[0]
                print ('--- report date: ', report_date)
                
                try:
                    res = hourly_reports_get(account_id, access_token, 1, 500, report_date)
                except:
                    time.sleep(1)
                    res = hourly_reports_get(account_id, access_token, 1, 500, report_date)
                
                if 'data' not in res.keys():
                    print ('*** cannot get data ....')
                    print (res)
                    break

                total_page = res['data']['page_info']['total_page']
                if total_page == 0:
                    #print ('*** page is 0 ....')
                    continue
                
                print ('--- %s total %d pages ...' % (report_date, total_page))

                for p in range(1, total_page + 1):
                    try:
                        res = hourly_reports_get(account_id, access_token, p, 500, report_date, time_line)
                    except:
                        time.sleep(1)
                        res = hourly_reports_get(account_id, access_token, p, 500, report_date, time_line)

                    if 'data' not in res.keys():
                        time.sleep(1)
                        res = hourly_reports_get(account_id, access_token, p, 500, report_date, time_line)
                    try:
                        data = res['data']['list']
                        # print (data[0])
                        # exit()
                        for d in data:
                            ad_report_data['account_id'].append(account_id)
                            ad_report_data['adgroup_id'].append(d['adgroup_id'])
                            ad_report_data['dynamic_creative_id'].append(d['dynamic_creative_id'])
                            ad_report_data['ds'].append(d['date'])
                            ad_report_data['hour'].append(d['hour'])
                            ad_report_data['cost'].append(d['cost'])
                            ad_report_data['acquisition_cost'].append(d['acquisition_cost'])
                            ad_report_data['view_count'].append(d['view_count'])
                            ad_report_data['valid_click_count'].append(d['valid_click_count'])
                            ad_report_data['reservation_uv'].append(d['reservation_uv'])
                            ad_report_data['biz_follow_uv'].append(d['biz_follow_uv'])
                            ad_report_data['biz_follow_cost'].append(d['biz_follow_cost'])
                            ad_report_data['biz_reg_uv'].append(d['biz_reg_uv'])
                            ad_report_data['reg_cost_pla'].append(d['reg_cost_pla'])
                            ad_report_data['page_reservation_cost_with_people'].append(d['page_reservation_cost_with_people'])
                            
                    except Exception as e:
                        print ('*** ', e) 
                time.sleep(1)
                
                
            ad_report_data = pd.DataFrame(ad_report_data)
            
            if len(ad_report_data) == 0:
                continue

            if has_hist:
                for col in hist_data.columns:
                    if 'Unnamed' in col:
                        hist_data = hist_data.drop([col], axis = 1)
                ad_report_data = pd.concat([hist_data, ad_report_data], axis = 0)

            # ad_report_data.to_csv('data_dynamic_creative_level/hourly_reports/%s.csv' % account_id)
            # if os.path.exists('data_dynamic_creative_level/hourly_reports' % (start_month, start_day, end_month, end_day)) == False:
            #     os.mkdir('data_dynamic_creative_level/hourly_reports-2024%02d%02d-2024%02d%02d' % (start_month, start_day, end_month, end_day))
            if os.path.exists('data_dynamic_creative_level/hourly_reports_%s' % time_line) == False:
                os.mkdir('data_dynamic_creative_level/hourly_reports_%s' % time_line)
            ad_report_data.to_csv('data_dynamic_creative_level/hourly_reports_%s/%s.csv' % (time_line, account_id))
            # exit()
        except Exception as e:
            print ('--- account ERROR: ', e)
            continue

def get_predict_data():

    get_adgroups(old_accounts + median_accounts + new_accounts)
    get_adcreative_hourly_report() # 每天获取小时报

    data_fields = ['cost', 'biz_follow_uv', 'biz_reg_uv', 'reservation_uv']
    data_time_range = ['cur_day', 'prev_hours_cur_day']
    for delta_day in [3,7,14,30]:
        data_time_range.append('past_%ddays' % delta_day)
    for delta_hour in [1,2,3,4,5]:
        data_time_range.append('last_%dhours' % delta_hour)
    operation_dict = {'account_id':[], 'adgroup_id':[], 'adgroup_create_time':[], \
                      'ds':[], 'hour':[], 'life_long_days':[]}
    for f in data_fields:
        for t in data_time_range:
            operation_dict[t + '_' + f] = []
            if f != 'cost':
                operation_dict[t + '_' + f.replace('_uv', '_cost')] = []
    print (operation_dict.keys())

    for account_id in old_accounts + median_accounts + new_accounts:
        # if account_id not in ['********']:
        #     continue
        print ('--- accound id: ', account_id)
        hourly_reports = pd.read_csv('data_dynamic_creative_level/hourly_reports_REQUEST_TIME/%s.csv' % account_id)
        hourly_reports['ds'] = hourly_reports['ds'].apply(lambda x: str(x))
        adgroup_ids = list(set(hourly_reports['adgroup_id']))

        if os.path.exists('adgroups/%s' % account_id) == False:
            continue

        adgroup_data = {}
        for j_file in os.listdir('adgroups/' + account_id):
            data = json.load(open('adgroups/' + account_id + '/' + j_file, encoding='utf8'))
            if 'data' in data and 'list' in data['data']:
                data = data['data']['list']
                for d in data:
                    if len(adgroup_data) == 0:
                        for col in d:
                            adgroup_data[col] = []
                    for col in d:
                        adgroup_data[col].append(d[col])

        adgroup_data = pd.DataFrame(adgroup_data)

        for adgroup_id in adgroup_ids:

            adgroup_info = adgroup_data[adgroup_data['adgroup_id'] == adgroup_id]
            if len(adgroup_info):

                adgroup_create_time = adgroup_info['created_time'].iloc[0]
                adgroup_create_time = datetime.datetime.fromtimestamp(adgroup_create_time)

                adgroup_report = hourly_reports[hourly_reports['adgroup_id'] == adgroup_id]
                adgroup_report = adgroup_report.sort_values(['ds', 'hour'])
                day = str(datetime.datetime.today()).split(' ')[0]
                for hour in range(6, 23):
                    
                    life_long_days = (datetime.datetime.today() - adgroup_create_time).days

                    # 过去N天统计
                    for delta_day in [3, 7, 14, 30]:
                        past_3day_date = str(datetime.datetime.strptime(day, "%Y-%m-%d") + datetime.timedelta(days = -delta_day)).split(' ')[0]
                        past_3days_data = adgroup_report[(adgroup_report['ds'] < day) & (adgroup_report['ds'] >= past_3day_date)]
                        past_3days_cost = np.sum(past_3days_data['cost']) * 0.01
                        past_3days_biz_follow_uv = np.sum(past_3days_data['biz_follow_uv'])
                        past_3days_biz_reg_uv = np.sum(past_3days_data['biz_reg_uv'])
                        past_3days_reservation_uv = np.sum(past_3days_data['reservation_uv'])

                        operation_dict['past_%ddays_cost' % delta_day].append(past_3days_cost)
                        operation_dict['past_%ddays_biz_follow_uv' % delta_day].append(past_3days_biz_follow_uv)
                        if past_3days_biz_follow_uv:
                            operation_dict['past_%ddays_biz_follow_cost' % delta_day].append(past_3days_cost/past_3days_biz_follow_uv)
                        else:
                            operation_dict['past_%ddays_biz_follow_cost' % delta_day].append(0)
                        operation_dict['past_%ddays_biz_reg_uv' % delta_day].append(past_3days_biz_reg_uv)
                        if past_3days_biz_reg_uv:
                            operation_dict['past_%ddays_biz_reg_cost' % delta_day].append(past_3days_cost/past_3days_biz_reg_uv)
                        else:
                            operation_dict['past_%ddays_biz_reg_cost' % delta_day].append(0)
                        operation_dict['past_%ddays_reservation_uv' % delta_day].append(past_3days_reservation_uv)
                        if past_3days_reservation_uv:
                            operation_dict['past_%ddays_reservation_cost' % delta_day].append(past_3days_cost/past_3days_reservation_uv)
                        else:
                            operation_dict['past_%ddays_reservation_cost' % delta_day].append(0)

                    cur_day_data = adgroup_report[adgroup_report['ds'] == day] # 当天数据
                    prev_hours_cur_day_data = cur_day_data[cur_day_data['hour'] < hour] # 截止操作动作小时之前的当天数据

                    # print ('-------------------------')
                    # print ('--- date: ', create_time)
                    # print ('--- operation: ', operation_action, operation_log)

                    operation_dict['account_id'].append(account_id)
                    operation_dict['adgroup_id'].append(adgroup_id)
                    operation_dict['adgroup_create_time'].append(adgroup_create_time)
                    operation_dict['life_long_days'].append(life_long_days)
                    operation_dict['ds'].append(day)
                    operation_dict['hour'].append(hour)

                    # 当天操作前数据统计
                    prev_hours_cur_day_cost = np.sum(prev_hours_cur_day_data['cost']) * 0.01
                    prev_hours_cur_day_biz_follow_uv = np.sum(prev_hours_cur_day_data['biz_follow_uv'])
                    prev_hours_cur_day_biz_reg_uv = np.sum(prev_hours_cur_day_data['biz_reg_uv'])
                    prev_hours_cur_day_reservation_uv = np.sum(prev_hours_cur_day_data['reservation_uv'])
                    operation_dict['prev_hours_cur_day_cost'].append(prev_hours_cur_day_cost)
                    operation_dict['prev_hours_cur_day_biz_follow_uv'].append(prev_hours_cur_day_biz_follow_uv)
                    if prev_hours_cur_day_biz_follow_uv:
                        operation_dict['prev_hours_cur_day_biz_follow_cost'].append(prev_hours_cur_day_cost/prev_hours_cur_day_biz_follow_uv)
                    else:
                        operation_dict['prev_hours_cur_day_biz_follow_cost'].append(0)
                    operation_dict['prev_hours_cur_day_biz_reg_uv'].append(prev_hours_cur_day_biz_reg_uv)
                    if prev_hours_cur_day_biz_reg_uv:
                        operation_dict['prev_hours_cur_day_biz_reg_cost'].append(prev_hours_cur_day_cost/prev_hours_cur_day_biz_reg_uv)
                    else:
                        operation_dict['prev_hours_cur_day_biz_reg_cost'].append(0)
                    operation_dict['prev_hours_cur_day_reservation_uv'].append(prev_hours_cur_day_reservation_uv)
                    if prev_hours_cur_day_reservation_uv:
                        operation_dict['prev_hours_cur_day_reservation_cost'].append(prev_hours_cur_day_cost/prev_hours_cur_day_reservation_uv)
                    else:
                        operation_dict['prev_hours_cur_day_reservation_cost'].append(0)

                    # 当天操作前分小时数据统计
                    for delta_hour in [1, 2, 3, 4, 5]:
                        last_hour_data = prev_hours_cur_day_data[(prev_hours_cur_day_data['hour'] < hour) & (prev_hours_cur_day_data['hour'] >= hour - delta_hour)]
                        last_hour_cost = np.sum(last_hour_data['cost']) * 0.01
                        last_hour_biz_follow_uv = np.sum(last_hour_data['biz_follow_uv'])
                        last_hour_biz_reg_uv = np.sum(last_hour_data['biz_reg_uv'])
                        last_hour_reservation_uv = np.sum(last_hour_data['reservation_uv'])

                        operation_dict['last_%dhours_cost' % delta_hour].append(last_hour_cost)
                        operation_dict['last_%dhours_biz_follow_uv' % delta_hour].append(last_hour_biz_follow_uv)
                        if last_hour_biz_follow_uv:
                            operation_dict['last_%dhours_biz_follow_cost' % delta_hour].append(last_hour_cost/last_hour_biz_follow_uv)
                        else:
                            operation_dict['last_%dhours_biz_follow_cost' % delta_hour].append(0)
                        operation_dict['last_%dhours_biz_reg_uv' % delta_hour].append(last_hour_biz_reg_uv)
                        if last_hour_biz_reg_uv:
                            operation_dict['last_%dhours_biz_reg_cost' % delta_hour].append(last_hour_cost/last_hour_biz_reg_uv)
                        else:
                            operation_dict['last_%dhours_biz_reg_cost' % delta_hour].append(0)
                        operation_dict['last_%dhours_reservation_uv' % delta_hour].append(last_hour_reservation_uv)
                        if last_hour_reservation_uv:
                            operation_dict['last_%dhours_reservation_cost' % delta_hour].append(last_hour_cost/last_hour_reservation_uv)
                        else:
                            operation_dict['last_%dhours_reservation_cost' % delta_hour].append(0)

                    # 当天数据统计
                    cur_day_cost = np.sum(cur_day_data['cost']) * 0.01
                    cur_day_biz_follow_uv = np.sum(cur_day_data['biz_follow_uv'])
                    cur_day_biz_reg_uv = np.sum(cur_day_data['biz_reg_uv'])
                    cur_day_reservation_uv = np.sum(cur_day_data['reservation_uv'])
                    operation_dict['cur_day_cost'].append(cur_day_cost)
                    operation_dict['cur_day_biz_follow_uv'].append(cur_day_biz_follow_uv)
                    if cur_day_biz_follow_uv:
                        operation_dict['cur_day_biz_follow_cost'].append(cur_day_cost/cur_day_biz_follow_uv)
                    else:
                        operation_dict['cur_day_biz_follow_cost'].append(0)
                    operation_dict['cur_day_biz_reg_uv'].append(cur_day_biz_reg_uv)
                    if cur_day_biz_reg_uv:
                        operation_dict['cur_day_biz_reg_cost'].append(cur_day_cost/cur_day_biz_reg_uv)
                    else:
                        operation_dict['cur_day_biz_reg_cost'].append(0)
                    operation_dict['cur_day_reservation_uv'].append(cur_day_reservation_uv)
                    if cur_day_reservation_uv:
                        operation_dict['cur_day_reservation_cost'].append(cur_day_cost/cur_day_reservation_uv)
                    else:
                        operation_dict['cur_day_reservation_cost'].append(0)

    operation_dict = pd.DataFrame(operation_dict)
    
    return operation_dict


def daily_reports_get(account_id, adgroup_id, access_token, page, page_size, start_date, end_date):

    interface = 'daily_reports/get'
    # url = 'https://api.e.qq.com/v1.1/' + interface
    url = 'https://api.e.qq.com/v3.0/' + interface

    nonce = str(time.time()) + str(random.randint(0, 999999))
    # print ('nonce: ', nonce, int(time.time()))

    common_parameters = {
            'access_token': access_token, 
            'timestamp': int(time.time()), 
            'nonce': nonce,
        }

    parameters = {
            "account_id": account_id,
            "level": 'REPORT_LEVEL_ADGROUP', #REPORT_LEVEL_DYNAMIC_CREATIVE', #'REPORT_LEVEL_MATERIAL_VIDEO', "REPORT_LEVEL_MATERIAL_IMAGE", #"REPORT_LEVEL_AD",
            "date_range": 
            {
                "start_date": start_date,
                "end_date": end_date
            },
            "filtering":
            [
                {
                    "field": "adgroup_id",
                    "operator": "EQUALS",
                    "values":
                    [
                        str(adgroup_id)
                    ]
                }
            ],
            "group_by": 
            [

                'date', 'adgroup_id' #, #'video_id' # "image_id" #'date', 
            ],
            "time_line": 'REQUEST_TIME', #'REPORTING_TIME',
            "page": page,
            "page_size": page_size,
            'fields': ['adgroup_id', 'date', 'view_count', 'valid_click_count', \
                'ctr', 'cost', 'acquisition_cost', 'biz_follow_uv', 'biz_reg_uv', 'reservation_uv']
        }

    parameters.update(common_parameters)
    for k in parameters:
        if type(parameters[k]) is not str:
            parameters[k] = json.dumps(parameters[k])

    r = requests.get(url, params = parameters)

    return r.json()

def hourly_reports_get_old(account_id, adgroup_id, access_token, page, page_size):

    interface = 'hourly_reports/get'
    # url = 'https://api.e.qq.com/v1.1/' + interface
    url = 'https://api.e.qq.com/v3.0/' + interface

    nonce = str(time.time()) + str(random.randint(0, 999999))
    # print ('nonce: ', nonce, int(time.time()))

    common_parameters = {
            'access_token': access_token, 
            'timestamp': int(time.time()), 
            'nonce': nonce,
        }
    
    today = datetime.datetime.today()
    today = str(today).split(' ')[0]

    parameters = {
            "account_id": account_id,
            "level": 'REPORT_LEVEL_ADGROUP', #REPORT_LEVEL_DYNAMIC_CREATIVE', #'REPORT_LEVEL_MATERIAL_VIDEO', "REPORT_LEVEL_MATERIAL_IMAGE", #"REPORT_LEVEL_AD",
            "date_range": 
            {
                "start_date": today,
                "end_date": today
            },
            "filtering":
            [
                {
                    "field": "adgroup_id",
                    "operator": "EQUALS",
                    "values":
                    [
                        str(adgroup_id)
                    ]
                }
            ],
            "group_by": 
            [

                'hour', 'date' #, #'video_id' # "image_id" #'date', 
            ],
            "time_line": 'REQUEST_TIME', #'REPORTING_TIME',
            "page": page,
            "page_size": page_size,
            'fields': ['date', 'hour', 'view_count', 'valid_click_count', \
                'ctr', 'cost', 'acquisition_cost', 'biz_follow_uv', 'biz_reg_uv', 'reservation_uv']
        }

    parameters.update(common_parameters)
    for k in parameters:
        if type(parameters[k]) is not str:
            parameters[k] = json.dumps(parameters[k])

    r = requests.get(url, params = parameters)

    return r.json()

def get_adgroup_report_old(account_ids):

    # 全量获取, 每天一次

    print ('--- start get adgroups for all accounts ...')
    get_adgroups(account_ids) # 获取全量广告

    print ('--- start get adgroup reports ...')
    adgroup_report_data = {}

    for account_id in account_ids:
        # if account_id not in account_ids:
        #     continue
        account_last7_cost = 0
        account_last7_biz_follow_uv = 0
        account_last7_biz_reg_uv = 0
        account_last7_reservation_uv = 0
        account_last3_cost = 0
        account_last3_biz_follow_uv = 0
        account_last3_biz_reg_uv = 0
        account_last3_reservation_uv = 0
        account_total_reservation_uv = 0
        print ('--- account_id: ', account_id)
        if os.path.exists('adgroups/' + account_id) == False:
            continue
        for j_file in os.listdir('adgroups/' + account_id):
            data = json.load(open('adgroups/' + account_id + '/' + j_file, encoding='utf8'))
            if 'data' in data and 'list' in data['data']:
                if account_id not in adgroup_report_data:
                    adgroup_report_data[account_id] = {}
                data = data['data']['list']
                for d in data:
                    created_time = d['created_time']
                    created_time = datetime.datetime.fromtimestamp(created_time)
                    created_time = str(created_time).split(' ')[0]
                    first_day_begin_time = d['first_day_begin_time']
                    adgroup_id = d['adgroup_id']
                    adgroup_name = d['adgroup_name']
                    system_status = d['system_status']
                    today = str(datetime.datetime.now()).split(' ')[0]
                    if adgroup_id not in adgroup_report_data[account_id]:
                        adgroup_report_data[account_id][adgroup_id] = {}
                        adgroup_report_data[account_id][adgroup_id]['ds'] = today
                        adgroup_report_data[account_id][adgroup_id]['adgroup_name'] = adgroup_name # 广告名称
                        adgroup_report_data[account_id][adgroup_id]['created_time'] = created_time # 广告创建时间
                        adgroup_report_data[account_id][adgroup_id]['first_day_begin_time'] = first_day_begin_time # 广告开始投放第一天
                        adgroup_report_data[account_id][adgroup_id]['system_status'] = system_status # 广告状态
                        adgroup_report_data[account_id][adgroup_id]['report_data'] = {'ds':[], # 近期每天的投放数据
                                                                                        'cost':[],
                                                                                        'acquisition_cost':[],
                                                                                        'view_count':[],
                                                                                        'valid_click_count':[],
                                                                                        'biz_follow_uv':[], 
                                                                                        'biz_reg_uv':[], 
                                                                                        'reservation_uv':[]}
                        adgroup_report_data[account_id][adgroup_id]['is_ever_up'] = 0 # 是否曾经起量过
                        adgroup_report_data[account_id][adgroup_id]['last_up_date'] = '' # 是否曾经起量过
                        adgroup_report_data[account_id][adgroup_id]['last_up_cost'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_uv'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_uv'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_uv'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_cost'] = 0 # 最近一次起量成本
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_cost'] = 0 # 最近一次起量成本
                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = 0 # 最近一次起量成本
                        adgroup_report_data[account_id][adgroup_id]['life_long_cost'] = 0 # 生命周期总消耗
                        adgroup_report_data[account_id][adgroup_id]['life_long_biz_follow_uv'] = 0 # 生命周期总转化数
                        adgroup_report_data[account_id][adgroup_id]['life_long_biz_reg_uv'] = 0 # 生命周期总转化数
                        adgroup_report_data[account_id][adgroup_id]['life_long_reservation_uv'] = 0 # 生命周期总转化数
                        adgroup_report_data[account_id][adgroup_id]['life_long_days'] = 0 # 上线天数

                    
                    past_14day = str(datetime.datetime.today() + datetime.timedelta(days = -hist_range)).split(' ')[0]
                    past_3day = str(datetime.datetime.today() + datetime.timedelta(days = -3)).split(' ')[0]
                    res = daily_reports_get(account_id, int(adgroup_id), access_token, 1, 100, created_time, today)
                    if 'data' not in res.keys():
                        print ('*** daily_reports_get cannot get data ....')
                        print (res)
                        exit()

                    adgroup_report_data[account_id][adgroup_id]['life_long_days'] = (datetime.datetime.strptime(today, "%Y-%m-%d") - datetime.datetime.strptime(created_time, "%Y-%m-%d")).days

                    total_page = res['data']['page_info']['total_page']
                    if total_page == 0:
                        # print (res)
                        # print ('*** page is 0 ....')
                        continue

                    # print ('--- report date: ', report_date)
                    # print ('--- total %d pages ...' % total_page)
                    #adgroup_report_data[account_id][adgroup_id]['is_ever_up'] = 0 # 是否曾经起量过
                    #adgroup_report_data[account_id][adgroup_id]['last_up_date'] = '' # 是否曾经起量过
                    #adgroup_report_data[account_id][adgroup_id]['last_up_cost'] = 0 # 最近一次起量消耗
                    #adgroup_report_data[account_id][adgroup_id]['last_up_reservation_uv'] = 0 # 最近一次起量消耗
                    #adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = 0 # 最近一次起量成本
                    #adgroup_report_data[account_id][adgroup_id]['life_long_cost'] = 0 # 最近一次起量成本
                    #adgroup_report_data[account_id][adgroup_id]['life_long_days'] = 0 # 上线天数
                    for p in range(1, total_page + 1):
                        res = daily_reports_get(account_id, adgroup_id, access_token, p, 100, created_time, today)
                        if 'data' not in res.keys():
                            time.sleep(3)
                            res = daily_reports_get(account_id, adgroup_id, access_token, p, 100, created_time, today)
                        try:
                            data = res['data']['list']
                            # print (data[0])
                            # exit()
                            
                            for d in data:
                                account_total_reservation_uv += d['reservation_uv']
                                if d['date'] == today:
                                    continue
                                adgroup_report_data[account_id][adgroup_id]['life_long_cost'] += d['cost'] * 0.01 # 自上线以来总消耗
                                adgroup_report_data[account_id][adgroup_id]['life_long_reservation_uv'] += d['reservation_uv']
                                if d['acquisition_cost'] * 0.01 > 200:
                                    adgroup_report_data[account_id][adgroup_id]['is_ever_up'] = 1
                                    adgroup_report_data[account_id][adgroup_id]['last_up_cost'] = d['cost'] * 0.01
                                    adgroup_report_data[account_id][adgroup_id]['last_up_date'] = d['date']
                                    adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_uv'] = d['biz_follow_uv']
                                    adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_uv'] = d['biz_reg_uv']
                                    adgroup_report_data[account_id][adgroup_id]['last_up_reservation_uv'] = d['reservation_uv']
                                    if d['biz_follow_uv']:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_cost'] = d['cost'] * 0.01/d['biz_follow_uv']
                                    else:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_cost'] = 0
                                    if d['biz_reg_uv']:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_cost'] = d['cost'] * 0.01/d['biz_reg_uv']
                                    else:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_cost'] = 0
                                    if d['reservation_uv']:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = d['cost'] * 0.01/d['reservation_uv']
                                    else:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = 0
                                if d['date'] < past_14day:
                                    continue
                                if d['date'] >= past_3day:
                                    account_last3_cost += d['cost'] * 0.01
                                    account_last3_biz_follow_uv += d['biz_follow_uv']
                                    account_last3_biz_reg_uv += d['biz_reg_uv']
                                    account_last3_reservation_uv += d['reservation_uv']
                                account_last7_cost += d['cost'] * 0.01
                                account_last7_biz_follow_uv += d['biz_follow_uv']
                                account_last7_biz_reg_uv += d['biz_reg_uv']
                                account_last7_reservation_uv += d['reservation_uv']
                                adgroup_report_data[account_id][adgroup_id]['report_data']['ds'].append(d['date'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['cost'].append(d['cost'] * 0.01)
                                adgroup_report_data[account_id][adgroup_id]['report_data']['acquisition_cost'].append(d['acquisition_cost'] * 0.01)
                                adgroup_report_data[account_id][adgroup_id]['report_data']['view_count'].append(d['view_count'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['valid_click_count'].append(d['valid_click_count'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['biz_follow_uv'].append(d['biz_follow_uv'])  
                                adgroup_report_data[account_id][adgroup_id]['report_data']['biz_reg_uv'].append(d['biz_reg_uv'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['reservation_uv'].append(d['reservation_uv'])       
                        except Exception as e:
                            print ('*** ', e) 
                            print (res)
                    adgroup_cost_sum = np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['cost'])
                    adgroup_biz_follow_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['biz_follow_uv']))
                    adgroup_biz_reg_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['biz_reg_uv']))
                    adgroup_reservation_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['reservation_uv']))
                    adgroup_report_data[account_id][adgroup_id]['total_cost'] = adgroup_cost_sum
                    adgroup_report_data[account_id][adgroup_id]['total_reservation_uv'] = adgroup_reservation_uv
                    if adgroup_biz_follow_uv:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_follow_cost'] = adgroup_cost_sum/adgroup_biz_follow_uv
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_follow_cost'] = 0
                    if adgroup_biz_reg_uv:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_reg_cost'] = adgroup_cost_sum/adgroup_biz_reg_uv
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_reg_cost'] = 0
                    if adgroup_reservation_uv:
                        adgroup_report_data[account_id][adgroup_id]['total_reservation_cost'] = adgroup_cost_sum/adgroup_reservation_uv
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_reservation_cost'] = 0
                   
                    adgroup_data_hist = pd.DataFrame(adgroup_report_data[account_id][adgroup_id]['report_data'])
                    # 前一天和前第三天表现
                    for delta_day in [1, 3]:
                        past_1day = datetime.datetime.today() + datetime.timedelta(days = -delta_day)
                        past_1day = str(past_1day).split(' ')[0]
                        past_1data = adgroup_data_hist[adgroup_data_hist['ds']==past_1day]

                        adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day] = np.sum(past_1data['cost'])
                        adgroup_report_data[account_id][adgroup_id]['past_%dday_acquisition_cost' % delta_day] = np.sum(past_1data['acquisition_cost'])

                        past_1day_biz_follow_uv = np.sum(past_1data['biz_follow_uv'])
                        past_1day_biz_reg_uv = np.sum(past_1data['biz_reg_uv'])
                        past_1day_reservation_uv = np.sum(past_1data['reservation_uv'])

                        adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_follow_uv' % delta_day] = int(past_1day_biz_follow_uv)
                        adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_reg_uv' % delta_day] = int(past_1day_biz_reg_uv)
                        adgroup_report_data[account_id][adgroup_id]['past_%dday_reservation_uv' % delta_day] = int(past_1day_reservation_uv)

                        if adgroup_report_data[account_id][adgroup_id]['past_%dday_acquisition_cost' % delta_day] > 100:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_up' % delta_day] = 1
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_up' % delta_day] = 0
                        
                        if past_1day_biz_follow_uv:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_follow_cost' % delta_day] = \
                                adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day]/past_1day_biz_follow_uv
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_follow_cost' % delta_day] = 0
                        if past_1day_biz_reg_uv:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_reg_cost' % delta_day] = \
                                adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day]/past_1day_biz_reg_uv
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_reg_cost' % delta_day] = 0
                        if past_1day_reservation_uv:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_reservation_cost' % delta_day] = \
                                adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day]/past_1day_reservation_uv
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_reservation_cost' % delta_day] = 0

        for adgroup_id in adgroup_report_data[account_id].keys():
            adgroup_report_data[account_id][adgroup_id]['account_past_3day_cost'] = account_last3_cost
            adgroup_report_data[account_id][adgroup_id]['account_past_3day_biz_follow_uv'] = account_last3_biz_follow_uv
            if account_last3_biz_follow_uv:
                adgroup_report_data[account_id][adgroup_id]['account_past_3day_biz_follow_cost'] = account_last3_cost/account_last3_biz_follow_uv
            else:
                adgroup_report_data[account_id][adgroup_id]['account_past_3day_biz_follow_cost'] = 0
            adgroup_report_data[account_id][adgroup_id]['account_past_3day_biz_reg_uv'] = account_last3_biz_reg_uv
            if account_last3_reservation_uv:
                adgroup_report_data[account_id][adgroup_id]['account_past_3day_biz_reg_cost'] = account_last3_cost/account_last3_biz_reg_uv
            else:
                adgroup_report_data[account_id][adgroup_id]['account_past_3day_biz_reg_cost'] = 0
            adgroup_report_data[account_id][adgroup_id]['account_past_3day_reservation_uv'] = account_last3_reservation_uv
            if account_last3_reservation_uv:
                adgroup_report_data[account_id][adgroup_id]['account_past_3day_reservation_cost'] = account_last3_cost/account_last3_reservation_uv
            else:
                adgroup_report_data[account_id][adgroup_id]['account_past_3day_reservation_cost'] = 0

            adgroup_report_data[account_id][adgroup_id]['account_past_7day_cost'] = account_last7_cost
            adgroup_report_data[account_id][adgroup_id]['account_past_7day_biz_follow_uv'] = account_last3_biz_follow_uv
            if account_last3_biz_follow_uv:
                adgroup_report_data[account_id][adgroup_id]['account_past_7day_biz_follow_cost'] = account_last3_cost/account_last3_biz_follow_uv
            else:
                adgroup_report_data[account_id][adgroup_id]['account_past_7day_biz_follow_cost'] = 0
            adgroup_report_data[account_id][adgroup_id]['account_past_7day_biz_reg_uv'] = account_last3_biz_reg_uv
            if account_last3_reservation_uv:
                adgroup_report_data[account_id][adgroup_id]['account_past_7day_biz_reg_cost'] = account_last3_cost/account_last3_biz_reg_uv
            else:
                adgroup_report_data[account_id][adgroup_id]['account_past_7day_biz_reg_cost'] = 0
            adgroup_report_data[account_id][adgroup_id]['account_past_7day_reservation_uv'] = account_last7_reservation_uv
            if account_last7_reservation_uv:
                adgroup_report_data[account_id][adgroup_id]['account_past_7day_reservation_cost'] = account_last7_cost/account_last7_reservation_uv
            else:
                adgroup_report_data[account_id][adgroup_id]['account_past_7day_reservation_cost'] = 0

            adgroup_report_data[account_id][adgroup_id]['account_total_reservation_uv'] = account_total_reservation_uv

    with open('adgroup_report_data.json', 'w', encoding = 'utf8') as f:
        json.dump(adgroup_report_data, f, indent=4, ensure_ascii=False)

def get_adgroup_report(account_ids):

    # 全量获取，每天一次

    print ('--- start get adgroups for all accounts ...')
    get_adgroups(account_ids) # 获取全量广告

    print ('--- start get adgroup reports ...')
    adgroup_report_data = {}
    account_report_data = {}

    for account_id in account_ids:
        # if account_id not in account_ids:
        #     continue

        account_last14_cost = 0
        account_last14_biz_follow_uv = 0
        account_last14_biz_reg_uv = 0
        account_last14_reservation_uv = 0
        account_last7_cost = 0
        account_last7_biz_follow_uv = 0
        account_last7_biz_reg_uv = 0
        account_last7_reservation_uv = 0
        account_last5_cost = 0
        account_last5_biz_follow_uv = 0
        account_last5_biz_reg_uv = 0
        account_last5_reservation_uv = 0
        account_last3_cost = 0
        account_last3_biz_follow_uv = 0
        account_last3_biz_reg_uv = 0
        account_last3_reservation_uv = 0
        account_total_reservation_uv = 0
        print ('--- account_id: ', account_id)
        if os.path.exists('adgroups/' + account_id) == False:
            continue
        for j_file in os.listdir('adgroups/' + account_id):
            data = json.load(open('adgroups/' + account_id + '/' + j_file, encoding='utf8'))
            if 'data' in data and 'list' in data['data']:
                if account_id not in adgroup_report_data:
                    adgroup_report_data[account_id] = {}
                    account_report_data[account_id] = {}
                data = data['data']['list']
                for d in data:
                    created_time = d['created_time']
                    created_time = datetime.datetime.fromtimestamp(created_time)
                    created_time = str(created_time).split(' ')[0]
                    first_day_begin_time = d['first_day_begin_time']
                    adgroup_id = d['adgroup_id']
                    adgroup_name = d['adgroup_name']
                    system_status = d['system_status']
                    today = str(datetime.datetime.now()).split(' ')[0]
                    if adgroup_id not in adgroup_report_data[account_id]:
                        adgroup_report_data[account_id][adgroup_id] = {}
                        adgroup_report_data[account_id][adgroup_id]['ds'] = today
                        adgroup_report_data[account_id][adgroup_id]['adgroup_name'] = adgroup_name # 广告名称
                        adgroup_report_data[account_id][adgroup_id]['created_time'] = created_time # 广告创建时间
                        adgroup_report_data[account_id][adgroup_id]['first_day_begin_time'] = first_day_begin_time # 广告开始投放第一天
                        adgroup_report_data[account_id][adgroup_id]['system_status'] = system_status # 广告状态
                        adgroup_report_data[account_id][adgroup_id]['report_data'] = {'ds':[], # 近期每天的投放数据
                                                                                        'cost':[],
                                                                                        'acquisition_cost':[],
                                                                                        'view_count':[],
                                                                                        'valid_click_count':[],
                                                                                        'biz_follow_uv':[], 
                                                                                        'biz_reg_uv':[], 
                                                                                        'reservation_uv':[]}
                        adgroup_report_data[account_id][adgroup_id]['is_ever_up'] = 0 # 是否曾经起量过
                        adgroup_report_data[account_id][adgroup_id]['last_up_date'] = '' # 是否曾经起量过
                        adgroup_report_data[account_id][adgroup_id]['last_up_cost'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_uv'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_uv'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_uv'] = 0 # 最近一次起量消耗
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_cost'] = 0 # 最近一次起量成本
                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_cost'] = 0 # 最近一次起量成本
                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = 0 # 最近一次起量成本
                        adgroup_report_data[account_id][adgroup_id]['life_long_cost'] = 0 # 生命周期总消耗
                        adgroup_report_data[account_id][adgroup_id]['life_long_biz_follow_uv'] = 0 # 生命周期总转化数
                        adgroup_report_data[account_id][adgroup_id]['life_long_biz_reg_uv'] = 0 # 生命周期总转化数
                        adgroup_report_data[account_id][adgroup_id]['life_long_reservation_uv'] = 0 # 生命周期总转化数
                        adgroup_report_data[account_id][adgroup_id]['life_long_days'] = 0 # 上线天数

                    
                    past_14day = str(datetime.datetime.today() + datetime.timedelta(days = -hist_range)).split(' ')[0]
                    past_5day = str(datetime.datetime.today() + datetime.timedelta(days = -5)).split(' ')[0]
                    past_3day = str(datetime.datetime.today() + datetime.timedelta(days = -3)).split(' ')[0]
                    res = daily_reports_get(account_id, int(adgroup_id), access_token, 1, 100, created_time, today)
                    if 'data' not in res.keys():
                        print ('*** daily_reports_get cannot get data ....')
                        print (res)
                        exit()

                    adgroup_report_data[account_id][adgroup_id]['life_long_days'] = (datetime.datetime.strptime(today, "%Y-%m-%d") - datetime.datetime.strptime(created_time, "%Y-%m-%d")).days
                    
                    total_page = res['data']['page_info']['total_page']
                    if total_page == 0:
                        # print (res)
                        # print ('*** page is 0 ....')
                        continue

                    # print ('--- report date: ', report_date)
                    # print ('--- total %d pages ...' % total_page)
                    #adgroup_report_data[account_id][adgroup_id]['is_ever_up'] = 0 # 是否曾经起量过
                    #adgroup_report_data[account_id][adgroup_id]['last_up_date'] = '' # 是否曾经起量过
                    #adgroup_report_data[account_id][adgroup_id]['last_up_cost'] = 0 # 最近一次起量消耗
                    #adgroup_report_data[account_id][adgroup_id]['last_up_reservation_uv'] = 0 # 最近一次起量消耗
                    #adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = 0 # 最近一次起量成本
                    #adgroup_report_data[account_id][adgroup_id]['life_long_cost'] = 0 # 最近一次起量成本
                    #adgroup_report_data[account_id][adgroup_id]['life_long_days'] = 0 # 上线天数
                    for p in range(1, total_page + 1):
                        res = daily_reports_get(account_id, adgroup_id, access_token, p, 100, created_time, today)
                        if 'data' not in res.keys():
                            time.sleep(3)
                            res = daily_reports_get(account_id, adgroup_id, access_token, p, 100, created_time, today)
                        try:
                            data = res['data']['list']
                            # print (data[0])
                            # exit()
                            data = data[::-1] # 日期由大到小排序
                            day_count = 0
                            for d in data:
                                account_total_reservation_uv += d['reservation_uv']
                                if d['date'] == today:
                                    continue
                                if day_count == 14: # 统计满14天
                                    break
                                if get_weekday(d['date']) in ['星期六',  '星期日'] and d['cost'] * 0.01 < 500:
                                    # 不在投放日
                                    continue
                                day_count += 1
                                adgroup_report_data[account_id][adgroup_id]['life_long_cost'] += d['cost'] * 0.01 # 自上线以来总消耗
                                adgroup_report_data[account_id][adgroup_id]['life_long_reservation_uv'] += d['reservation_uv']
                                if d['acquisition_cost'] * 0.01 > 200:
                                    adgroup_report_data[account_id][adgroup_id]['is_ever_up'] = 1
                                    adgroup_report_data[account_id][adgroup_id]['last_up_cost'] = d['cost'] * 0.01
                                    adgroup_report_data[account_id][adgroup_id]['last_up_date'] = d['date']
                                    adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_uv'] = d['biz_follow_uv']
                                    adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_uv'] = d['biz_reg_uv']
                                    adgroup_report_data[account_id][adgroup_id]['last_up_reservation_uv'] = d['reservation_uv']
                                    if d['biz_follow_uv']:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_cost'] = d['cost'] * 0.01/d['biz_follow_uv']
                                    else:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_follow_cost'] = 0
                                    if d['biz_reg_uv']:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_cost'] = d['cost'] * 0.01/d['biz_reg_uv']
                                    else:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_biz_reg_cost'] = 0
                                    if d['reservation_uv']:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = d['cost'] * 0.01/d['reservation_uv']
                                    else:
                                        adgroup_report_data[account_id][adgroup_id]['last_up_reservation_cost'] = 0
                                # if d['date'] < past_14day:
                                #     continue
                                # if d['date'] >= past_3day:
                                if day_count <= 3:
                                    account_last3_cost += d['cost'] * 0.01
                                    account_last3_biz_follow_uv += d['biz_follow_uv']
                                    account_last3_biz_reg_uv += d['biz_reg_uv']
                                    account_last3_reservation_uv += d['reservation_uv']
                                # if d['date'] >= past_5day:
                                if day_count <= 5:
                                    account_last5_cost += d['cost'] * 0.01
                                    account_last5_biz_follow_uv += d['biz_follow_uv']
                                    account_last5_biz_reg_uv += d['biz_reg_uv']
                                    account_last5_reservation_uv += d['reservation_uv']
                                if day_count <= 7:
                                    account_last7_cost += d['cost'] * 0.01
                                    account_last7_biz_follow_uv += d['biz_follow_uv']
                                    account_last7_biz_reg_uv += d['biz_reg_uv']
                                    account_last7_reservation_uv += d['reservation_uv']
                                account_last14_cost += d['cost'] * 0.01
                                account_last14_biz_follow_uv += d['biz_follow_uv']
                                account_last14_biz_reg_uv += d['biz_reg_uv']
                                account_last14_reservation_uv += d['reservation_uv']

                                adgroup_report_data[account_id][adgroup_id]['report_data']['ds'].append(d['date'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['cost'].append(d['cost'] * 0.01)
                                adgroup_report_data[account_id][adgroup_id]['report_data']['acquisition_cost'].append(d['acquisition_cost'] * 0.01)
                                adgroup_report_data[account_id][adgroup_id]['report_data']['view_count'].append(d['view_count'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['valid_click_count'].append(d['valid_click_count'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['biz_follow_uv'].append(d['biz_follow_uv'])  
                                adgroup_report_data[account_id][adgroup_id]['report_data']['biz_reg_uv'].append(d['biz_reg_uv'])
                                adgroup_report_data[account_id][adgroup_id]['report_data']['reservation_uv'].append(d['reservation_uv'])       
                        except Exception as e:
                            print ('*** ', e) 
                            print (res)

                    # 去重周末不投放日期
                    if adgroup_report_data[account_id][adgroup_id]['life_long_days'] >= 5:
                        adgroup_report_data[account_id][adgroup_id]['is_new'] = 0
                    else:
                        today = datetime.datetime.today()
                        report_data = adgroup_report_data[account_id][adgroup_id]['report_data']
                        ds_list = report_data['ds']
                        cost_list = report_data['cost']
                        days_cnt = 0
                        for i in range(1, adgroup_report_data[account_id][adgroup_id]['life_long_days'] + 1):
                            ds = str(today + datetime.timedelta(-i)).split(' ')[0]
                            if ds in ds_list:
                                week_day = get_weekday(ds)
                                if week_day in ['星期六', '星期日'] and cost_list[ds_list.index(ds)] < 500:
                                    continue
                            else:
                                week_day = get_weekday(ds)
                                if week_day in ['星期六', '星期日']:
                                    continue
                            days_cnt += 1
                        if days_cnt <= 2:
                            adgroup_report_data[account_id][adgroup_id]['is_new'] = 1
                        else:
                            adgroup_report_data[account_id][adgroup_id]['is_new'] = 0

                    adgroup_cost_sum = np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['cost'])
                    adgroup_biz_follow_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['biz_follow_uv']))
                    adgroup_biz_reg_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['biz_reg_uv']))
                    adgroup_reservation_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['reservation_uv']))
                    adgroup_report_data[account_id][adgroup_id]['total_cost'] = adgroup_cost_sum
                    adgroup_report_data[account_id][adgroup_id]['total_reservation_uv'] = adgroup_reservation_uv
                    if adgroup_biz_follow_uv:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_follow_cost'] = adgroup_cost_sum/adgroup_biz_follow_uv
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_follow_cost'] = 0
                    if adgroup_biz_reg_uv:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_reg_cost'] = adgroup_cost_sum/adgroup_biz_reg_uv
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_reg_cost'] = 0
                    if adgroup_reservation_uv:
                        adgroup_report_data[account_id][adgroup_id]['total_reservation_cost'] = adgroup_cost_sum/adgroup_reservation_uv
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_reservation_cost'] = 0
                   
                    adgroup_data_hist = pd.DataFrame(adgroup_report_data[account_id][adgroup_id]['report_data'])
                    # 前一天和前第三天表现
                    for delta_day in [1, 3, 5, 7, 14]:
                        # past_1day = datetime.datetime.today() + datetime.timedelta(days = -delta_day)
                        # past_1day = str(past_1day).split(' ')[0]
                        # past_1data = adgroup_data_hist[adgroup_data_hist['ds']==past_1day]
                        past_1data = adgroup_data_hist.iloc[:delta_day]

                        adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day] = np.sum(past_1data['cost'])
                        adgroup_report_data[account_id][adgroup_id]['past_%dday_acquisition_cost' % delta_day] = np.sum(past_1data['acquisition_cost'])

                        past_1day_biz_follow_uv = np.sum(past_1data['biz_follow_uv'])
                        past_1day_biz_reg_uv = np.sum(past_1data['biz_reg_uv'])
                        past_1day_reservation_uv = np.sum(past_1data['reservation_uv'])

                        adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_follow_uv' % delta_day] = int(past_1day_biz_follow_uv)
                        adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_reg_uv' % delta_day] = int(past_1day_biz_reg_uv)
                        adgroup_report_data[account_id][adgroup_id]['past_%dday_reservation_uv' % delta_day] = int(past_1day_reservation_uv)

                        if adgroup_report_data[account_id][adgroup_id]['past_%dday_acquisition_cost' % delta_day] > 100:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_up' % delta_day] = 1
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_up' % delta_day] = 0
                        
                        if past_1day_biz_follow_uv:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_follow_cost' % delta_day] = \
                                adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day]/past_1day_biz_follow_uv
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_follow_cost' % delta_day] = 0
                        if past_1day_biz_reg_uv:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_reg_cost' % delta_day] = \
                                adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day]/past_1day_biz_reg_uv
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_biz_reg_cost' % delta_day] = 0
                        if past_1day_reservation_uv:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_reservation_cost' % delta_day] = \
                                adgroup_report_data[account_id][adgroup_id]['past_%dday_cost' % delta_day]/past_1day_reservation_uv
                        else:
                            adgroup_report_data[account_id][adgroup_id]['past_%dday_reservation_cost' % delta_day] = 0

        account_report_data[account_id]['account_past_3day_cost'] = account_last3_cost
        account_report_data[account_id]['account_past_3day_biz_follow_uv'] = account_last3_biz_follow_uv
        if account_last3_biz_follow_uv:
            account_report_data[account_id]['account_past_3day_biz_follow_cost'] = account_last3_cost/account_last3_biz_follow_uv
        else:
            account_report_data[account_id]['account_past_3day_biz_follow_cost'] = account_last3_cost
        account_report_data[account_id]['account_past_3day_biz_reg_uv'] = account_last3_biz_reg_uv
        if account_last3_biz_reg_uv:
            account_report_data[account_id]['account_past_3day_biz_reg_cost'] = account_last3_cost/account_last3_biz_reg_uv
        else:
            account_report_data[account_id]['account_past_3day_biz_reg_cost'] = account_last3_cost
        account_report_data[account_id]['account_past_3day_reservation_uv'] = account_last3_reservation_uv
        if account_last3_reservation_uv:
            account_report_data[account_id]['account_past_3day_reservation_cost'] = account_last3_cost/account_last3_reservation_uv
        else:
            account_report_data[account_id]['account_past_3day_reservation_cost'] = account_last3_cost

        account_report_data[account_id]['account_past_5day_cost'] = account_last5_cost
        account_report_data[account_id]['account_past_5day_biz_follow_uv'] = account_last5_biz_follow_uv
        if account_last5_biz_follow_uv:
            account_report_data[account_id]['account_past_5day_biz_follow_cost'] = account_last5_cost/account_last5_biz_follow_uv
        else:
            account_report_data[account_id]['account_past_5day_biz_follow_cost'] = account_last5_cost
        account_report_data[account_id]['account_past_5day_biz_reg_uv'] = account_last5_biz_reg_uv
        if account_last5_biz_reg_uv:
            account_report_data[account_id]['account_past_5day_biz_reg_cost'] = account_last5_cost/account_last5_biz_reg_uv
        else:
            account_report_data[account_id]['account_past_5day_biz_reg_cost'] = account_last5_cost
        account_report_data[account_id]['account_past_5day_reservation_uv'] = account_last5_reservation_uv
        if account_last5_reservation_uv:
            account_report_data[account_id]['account_past_5day_reservation_cost'] = account_last5_cost/account_last5_reservation_uv
        else:
            account_report_data[account_id]['account_past_5day_reservation_cost'] = account_last5_cost

        account_report_data[account_id]['account_past_7day_cost'] = account_last7_cost
        account_report_data[account_id]['account_past_7day_biz_follow_uv'] = account_last7_biz_follow_uv
        if account_last7_biz_follow_uv:
            account_report_data[account_id]['account_past_7day_biz_follow_cost'] = account_last7_cost/account_last7_biz_follow_uv
        else:
            account_report_data[account_id]['account_past_7day_biz_follow_cost'] = account_last7_cost
        account_report_data[account_id]['account_past_7day_biz_reg_uv'] = account_last7_biz_reg_uv
        if account_last7_biz_reg_uv:
            account_report_data[account_id]['account_past_7day_biz_reg_cost'] = account_last7_cost/account_last7_biz_reg_uv
        else:
            account_report_data[account_id]['account_past_7day_biz_reg_cost'] = account_last7_cost
        account_report_data[account_id]['account_past_7day_reservation_uv'] = account_last7_reservation_uv
        if account_last7_reservation_uv:
            account_report_data[account_id]['account_past_7day_reservation_cost'] = account_last7_cost/account_last7_reservation_uv
        else:
            account_report_data[account_id]['account_past_7day_reservation_cost'] = account_last7_cost

        account_report_data[account_id]['account_past_14day_cost'] = account_last14_cost
        account_report_data[account_id]['account_past_14day_biz_follow_uv'] = account_last14_biz_follow_uv
        if account_last14_biz_follow_uv:
            account_report_data[account_id]['account_past_14day_biz_follow_cost'] = account_last14_cost/account_last14_biz_follow_uv
        else:
            account_report_data[account_id]['account_past_14day_biz_follow_cost'] = account_last14_cost
        account_report_data[account_id]['account_past_14day_biz_reg_uv'] = account_last14_biz_reg_uv
        if account_last14_biz_reg_uv:
            account_report_data[account_id]['account_past_14day_biz_reg_cost'] = account_last14_cost/account_last14_biz_reg_uv
        else:
            account_report_data[account_id]['account_past_14day_biz_reg_cost'] = account_last14_cost
        account_report_data[account_id]['account_past_14day_reservation_uv'] = account_last14_reservation_uv
        if account_last14_reservation_uv:
            account_report_data[account_id]['account_past_14day_reservation_cost'] = account_last14_cost/account_last14_reservation_uv
        else:
            account_report_data[account_id]['account_past_14day_reservation_cost'] = account_last14_cost

        account_report_data[account_id]['account_total_reservation_uv'] = account_total_reservation_uv

    with open('adgroup_report_data.json', 'w', encoding = 'utf8') as f:
        json.dump(adgroup_report_data, f, indent=4, ensure_ascii=False)

    with open('account_report_data.json', 'w', encoding = 'utf8') as f:
        json.dump(account_report_data, f, indent=4, ensure_ascii=False)

def update_adgroup_system_status(select_account_id):
    get_adgroups([select_account_id])
    print ('--- start update adgroup system status ...')
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        for j_file in os.listdir('adgroups/' + account_id):
            data = json.load(open('adgroups/' + account_id + '/' + j_file, encoding='utf8'))
            if 'data' in data and 'list' in data['data']:
                data = data['data']['list']
                for d in data:
                    adgroup_id = d['adgroup_id']
                    system_status = d['system_status']
                    if adgroup_id in adgroup_report_data[account_id].keys():
                        adgroup_report_data[account_id][adgroup_id]['system_status'] = system_status
    with open('adgroup_report_data.json', 'w', encoding = 'utf8') as f:
        json.dump(adgroup_report_data, f, indent=4, ensure_ascii=False)
    
def update_adgroup_today_report_data(select_account_id):
    # 每小时更新当天数据
    print ('--- start update adgroup today report ...')
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    if select_account_id not in adgroup_report_data:
        print ('--- 账号暂没有数据...')
        return -1
    else:
        now = datetime.datetime.now()
        day = str(now).split(' ')[0]
        hour = int(str(now).split(' ')[1].split(':')[0])
        for account_id in [select_account_id]:
            for adgroup_id in adgroup_report_data[account_id].keys():
                res = hourly_reports_get_old(account_id, adgroup_id, access_token, 1, 100)
                if 'data' in res and 'list' in res['data']:
                    data = res['data']['list']
                    cost = []
                    acquisition_cost = []
                    biz_follow_uv = []
                    biz_reg_uv = []
                    reservation_uv = []
                    hour_list = []
                    for d in data:
                        cost.append(d['cost'] * 0.01)
                        hour_list.append(d['hour'])
                        biz_follow_uv.append(d['biz_follow_uv'])
                        biz_reg_uv.append(d['biz_reg_uv'])
                        reservation_uv.append(d['reservation_uv'])
                        acquisition_cost.append(d['acquisition_cost'] * 0.01)

                    # 判断增长趋势
                    up_trend = 0
                    print ('--- today hour cost: ', cost, hour_list)
                    if len(cost) > 3:
                        if hour != hour_list[-1]:
                            delta_1 = cost[-1] > cost[-2]
                            delta_2 = cost[-2] > cost[-3]
                        else:
                            delta_1 = cost[-2] > cost[-3]
                            delta_2 = cost[-3] > cost[-4]
                        print (delta_1, delta_2, delta_1 > delta_2)
                        if delta_1 > 0 and delta_2 > 0 and delta_1 > delta_2: 
                            print ('--- 消耗上升趋势: delta_1 %d delta_2 %d')
                            up_trend = 1
                    adgroup_report_data[account_id][adgroup_id]['up_trend'] = up_trend
                    
                    today_cost = np.sum(cost)
                    today_acquisition_cost = np.sum(acquisition_cost)
                    today_biz_follow_uv = int(np.sum(biz_follow_uv)) 
                    today_biz_reg_uv = int(np.sum(biz_reg_uv)) 
                    today_reservation_uv = int(np.sum(reservation_uv)) 
                    if today_biz_follow_uv:
                        today_biz_follow_cost = today_cost/today_biz_follow_uv
                    else:
                        today_biz_follow_cost = 0
                    if today_biz_reg_uv:
                        today_biz_reg_cost = today_cost/today_biz_reg_uv
                    else:
                        today_biz_reg_cost = 0
                    if today_reservation_uv:
                        today_reservation_cost = today_cost/today_reservation_uv
                    else:
                        today_reservation_cost = 0
                    if today_acquisition_cost > 100:
                        adgroup_report_data[account_id][adgroup_id]['today_is_up'] = 1
                    else:
                        adgroup_report_data[account_id][adgroup_id]['today_is_up'] = 0

                    if len(cost)>1:
                        past_1hour_cost = cost[-2]
                        past_1hour_biz_follow_uv = biz_follow_uv[-2]
                        past_1hour_biz_reg_uv = biz_reg_uv[-2]
                        past_1hour_reservation_uv = reservation_uv[-2]
                        adgroup_report_data[account_id][adgroup_id]['past_1hour_cost'] = past_1hour_cost
                        adgroup_report_data[account_id][adgroup_id]['past_1hour_biz_follow_uv'] = past_1hour_biz_follow_uv
                        adgroup_report_data[account_id][adgroup_id]['past_1hour_biz_reg_uv'] = past_1hour_biz_reg_uv
                        adgroup_report_data[account_id][adgroup_id]['past_1hour_reservation_uv'] = past_1hour_reservation_uv
                    else:
                        adgroup_report_data[account_id][adgroup_id]['past_1hour_cost'] = 0
                        adgroup_report_data[account_id][adgroup_id]['past_1hour_biz_follow_uv'] = 0
                        adgroup_report_data[account_id][adgroup_id]['past_1hour_biz_reg_uv'] = 0
                        adgroup_report_data[account_id][adgroup_id]['past_1hour_reservation_uv'] = 0

                    adgroup_report_data[account_id][adgroup_id]['today_cost'] = today_cost
                    adgroup_report_data[account_id][adgroup_id]['today_biz_follow_uv'] = today_biz_follow_uv
                    adgroup_report_data[account_id][adgroup_id]['today_biz_follow_cost'] = today_biz_follow_cost
                    adgroup_report_data[account_id][adgroup_id]['today_biz_reg_uv'] = today_biz_reg_uv
                    adgroup_report_data[account_id][adgroup_id]['today_biz_reg_cost'] = today_biz_reg_cost
                    adgroup_report_data[account_id][adgroup_id]['today_reservation_uv'] = today_reservation_uv
                    adgroup_report_data[account_id][adgroup_id]['today_reservation_cost'] = today_reservation_cost
                    adgroup_report_data[account_id][adgroup_id]['today_acquisition_cost'] = today_acquisition_cost

                    adgroup_cost_sum = np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['cost'])
                    adgroup_biz_follow_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['biz_follow_uv']))     
                    adgroup_biz_reg_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['biz_reg_uv']))     
                    adgroup_reservation_uv = int(np.sum(adgroup_report_data[account_id][adgroup_id]['report_data']['reservation_uv']))      

                    adgroup_report_data[account_id][adgroup_id]['total_cost'] = adgroup_cost_sum + today_cost
                    adgroup_report_data[account_id][adgroup_id]['total_biz_follow_uv'] = adgroup_biz_follow_uv + today_biz_follow_uv
                    adgroup_report_data[account_id][adgroup_id]['total_biz_reg_uv'] = adgroup_biz_reg_uv + today_biz_reg_uv
                    adgroup_report_data[account_id][adgroup_id]['total_reservation_uv'] = adgroup_reservation_uv + today_reservation_uv

                    if adgroup_report_data[account_id][adgroup_id]['total_biz_follow_uv']:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_follow_cost'] = adgroup_report_data[account_id][adgroup_id]['total_cost']/adgroup_report_data[account_id][adgroup_id]['total_biz_follow_uv']
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_follow_cost'] = 0
                    if adgroup_report_data[account_id][adgroup_id]['total_biz_reg_uv']:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_reg_cost'] = adgroup_report_data[account_id][adgroup_id]['total_cost']/adgroup_report_data[account_id][adgroup_id]['total_biz_reg_uv']
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_biz_reg_cost'] = 0
                    if adgroup_report_data[account_id][adgroup_id]['total_reservation_uv']:
                        adgroup_report_data[account_id][adgroup_id]['total_reservation_cost'] = adgroup_report_data[account_id][adgroup_id]['total_cost']/adgroup_report_data[account_id][adgroup_id]['total_reservation_uv']
                    else:
                        adgroup_report_data[account_id][adgroup_id]['total_reservation_cost'] = 0


        with open('adgroup_report_data.json', 'w', encoding = 'utf8') as f:
            json.dump(adgroup_report_data, f, indent=4, ensure_ascii=False)

        return 1

def get_up_adgroup_ids_for_all(select_account_id, adgroup_status):
    print ('--- 基于历史数据, 寻找可以一键起量的广告 ...')
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        for adgroup_id in adgroup_ids:
            data = adgroup_data[adgroup_id]
            account_past_7day_reservation_cost = data['account_past_7day_reservation_cost']
            account_total_reservation_uv = data['account_total_reservation_uv']
            break

        if account_total_reservation_uv >= 30: # 判断新老账号
            up_num = up_all_num_for_old
            print ('--- 老账户: 总转化数 %d' % account_total_reservation_uv)
            if account_past_7day_reservation_cost > 600: # 老账号成本太高, 不起量
                print ('--- 账户成本超过 %.2f 600...不起量' % account_past_7day_reservation_cost)
                res[account_id] = []
                continue
        else:
            print ('--- 新账户: 总转化数 %d' % account_total_reservation_uv)
            up_num = up_all_num_for_new

        print ('--- 最大一键起量数:', up_num)
        print ('--- 最大从未起量广告的一键起量数:', up_new_adgroup_num)
        
        select_up_adgroup_ids = []
        never_up_adgroup_ids = []
        active_num = 0
        candidates = {'adgroup_id':[], 'created_time':[],'reservation_uv':[], 'cost':[], 'reservation_cost':[],'msg':[]}
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
                print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                continue
            active_num += 1 
            ds_list = data['report_data']['ds']
            if len(ds_list) == 0: # 没有历史数据
                continue
        
            # if data['total_cost'] < 100 or (data['past_1day_up'] == 1 and data['past_1day_reservation_cost'] > final_cost_goal * 2):
            if data['total_cost'] < 100:
                mark = 0
            elif data['is_ever_up'] == 1 and \
                    ((data['last_up_reservation_uv'] == 0) or \
                     (data['last_up_reservation_uv'] >0 and data['last_up_reservation_cost'] > final_cost_goal * 2)):
                    mark = 0
                    msg = '不进行一键起量： 最近一次起量转化数 %d, 转化消耗 %.2f, 转化成本 %d >%d' % \
                        (data['last_up_reservation_uv'], data['last_up_cost'], data['last_up_reservation_cost'], final_cost_goal * 2)
                    print (adgroup_id, msg)
            elif data['past_1day_reservation_uv'] > 2 and data['total_reservation_cost'] <= final_cost_goal * 1.2:
                mark = 1
                msg = '开启一键起量：1: 前一天转化数 %d >2, 总转化成本 %.2f <=520' % (data['past_1day_reservation_uv'], data['total_reservation_cost'])
                print (adgroup_id, msg)
            elif data['past_1day_reservation_uv'] > 0 and (account_past_7day_reservation_cost > 0 and account_past_7day_reservation_cost <= final_cost_goal):
                mark = 1
                msg = '开启一键起量：2: 前一天转化数 %d >0, 账户总转化成本 %.2f <=440' % (data['past_1day_reservation_uv'], account_past_7day_reservation_cost)
                print (adgroup_id, msg)
            elif data['total_reservation_uv'] >= 1 and data['total_reservation_cost'] <= adgroup_reservation_cost_limit:
                # if data['is_ever_up'] == 1 and data['past_1day_reservation_cost'] <= final_cost_goal * 2: # adgroup_reservation_cost_limit * 1.2
                mark = 1
                msg = '开启一键起量： 3: 总转化数 %d >=1, 总转化成本 %.2f <=%d' % \
                        (data['total_reservation_uv'], data['total_reservation_cost'], adgroup_reservation_cost_limit)
                print (adgroup_id, msg)
            else:
                mark = 0
            if mark == 1:
                candidates['adgroup_id'].append(adgroup_id)
                candidates['created_time'].append(data['created_time'])
                candidates['reservation_uv'].append(data['total_reservation_uv'])
                candidates['cost'].append(data['total_cost'])
                candidates['reservation_cost'].append(data['total_reservation_cost'])
                candidates['msg'].append(msg)
            else:
                if data['is_ever_up'] == 0:
                    never_up_adgroup_ids.append(adgroup_id)
        print ('--- active num: ', active_num)

        candidates = pd.DataFrame(candidates)
        candidates['reservation_uv_norm'] = candidates['reservation_uv']/np.sum(candidates['reservation_uv'])
        candidates['score'] = candidates['reservation_uv_norm'] / candidates['reservation_cost'] * -1
        candidates = candidates.sort_values('score')
        print (candidates)
        for i in range(min(up_num, len(candidates))):
            select_up_adgroup_ids.append(['startAutoAcquisition',account_id, candidates['adgroup_id'].iloc[i], candidates['msg'].iloc[i], 300000])
        if len(select_up_adgroup_ids) < up_num:
            candidates = {'adgroup_id':[], 'created_time':[],'reservation_uv':[], 'cost':[], 'reservation_cost':[], 'msg':[]}
            print ('--- 补充从来没有起量广告')
            select_new_adgroup_id =[]
            for adgroup_id in never_up_adgroup_ids:
                data = adgroup_data[adgroup_id]
                candidates['adgroup_id'].append(adgroup_id)
                candidates['created_time'].append(data['created_time'])
                candidates['reservation_uv'].append(data['total_reservation_uv'])
                candidates['cost'].append(data['total_cost'])
                candidates['reservation_cost'].append(data['total_reservation_cost'])
                candidates['msg'].append('从未起量')
            candidates = pd.DataFrame(candidates)
            temp = candidates[(candidates['reservation_uv']>0) & (candidates['reservation_uv']<adgroup_reservation_cost_limit)]
            if len(temp):
                temp = temp.sort_values(['reservation_uv'], ascending=False)
                print (temp)
                for i in range(min(up_new_adgroup_num, len(temp))):
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], '开启一键起量：从未起量, 转化数>0', 300000])
                    print (temp['adgroup_id'].iloc[i], ' 4: 总转化数大于0')
                    select_new_adgroup_id.append(temp['adgroup_id'].iloc[i])
                    if len(select_up_adgroup_ids) == up_num or len(select_new_adgroup_id) == up_new_adgroup_num:
                        break
            if len(select_up_adgroup_ids) < up_num and len(select_new_adgroup_id) < up_new_adgroup_num:
                candidates['cost'] = candidates['cost'] * -1
                # candidates = candidates.sort_values(['created_time', 'cost'])
                candidates = candidates.sort_values(['created_time'], ascending=False)
                print (candidates)
                for i in range(min(up_new_adgroup_num, len(candidates))):
                    if candidates['adgroup_id'].iloc[i] in select_up_adgroup_ids:
                        continue
                    if candidates['cost'].iloc[i] > adgroup_reservation_cost_limit: # 整体成本超标
                        continue
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, candidates['adgroup_id'].iloc[i], '开启一键起量：从未起量, 转化数=0', 300000])
                    select_new_adgroup_id.append(candidates['adgroup_id'].iloc[i])
                    print (candidates['adgroup_id'].iloc[i], ' 5: 总转化数等于0')
                    if len(select_up_adgroup_ids) == up_num or len(select_new_adgroup_id) == up_new_adgroup_num:
                        break
        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        
        res[account_id] = select_up_adgroup_ids
        # return select_up_adgroup_ids
    return res

def check_if_meet_up_condition(adgroup_id, adgroup_status, adgroup_data):

    if adgroup_id not in adgroup_status:
        # print ('--- %s adgroup status not found ...' % str(adgroup_id))
        return 0
    # if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
    #     print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
    #     return 0
    system_status = adgroup_status[adgroup_id]['systemStatus']
    if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
        return 0
    data = adgroup_data[adgroup_id]
    adgroup_name = data['adgroup_name']
    if adgroup_name[-2:] == 'ZD':
        print ('--- 最大转化成本广告, 不起量...')
        return 0
    ds_list = data['report_data']['ds']
    if len(ds_list) == 0: # 没有历史数据
        return 0
    
    return 1

def check_account_over_budget(account_id, adgroup_iadgroup_data, adgroup_status, final_cost_goal):
    account_last_1day_cost = 0
    account_last_1day_reservation_uv = 0
    for adgroup_id in adgroup_ids:
        data = adgroup_data[adgroup_id]
        # account_past_7day_reservation_cost = data['account_past_7day_reservation_cost']
        # account_past_3day_reservation_cost = data['account_past_3day_reservation_cost']
        # account_past_3day_cost = data['account_past_3day_cost']
        account_total_reservation_uv = data['account_total_reservation_uv']
        if len(data['report_data']['ds']) > 0:
            account_last_1day_cost += data['report_data']['cost'][-1]
            account_last_1day_reservation_uv += data['report_data']['reservation_uv'][-1]

    if account_last_1day_reservation_uv > 0:
            account_last_1day_reservation_cost = account_last_1day_cost/account_last_1day_reservation_uv
    else:
        account_last_1day_reservation_cost = 0
    
    if account_last_1day_reservation_uv == 0 and account_last_1day_cost > final_cost_goal * 2:
        print ('--- 账号前一天总成本超标,  转化数 %d 消耗 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost))
        return 0, 0, 0
    if account_last_1day_reservation_uv > 0 and account_last_1day_cost/account_last_1day_reservation_uv > final_cost_goal * 2:
        print ('--- 账号前一天总成本超标,  转化数 %d 转化成本 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost/account_last_1day_reservation_uv))
        return 0, 0, 0
    
    up_new_adgroup_num = 1 # 新广告起量数量
    if account_id in old_accounts:
        # up_num = up_all_num_for_old
        if account_last_1day_reservation_uv > 0:
            if account_last_1day_reservation_cost <= final_cost_goal:
                print ('--- 账户: 前一天转化成本 %.2f <= %.2f, 起量4个' % (account_last_1day_reservation_cost, final_cost_goal))
                up_num = 4
            if account_last_1day_reservation_cost <= final_cost_goal * 1.2:
                print ('--- 账户: 前一天转化成本 %.2f <= %.2f, 起量3个' % (account_last_1day_reservation_cost, final_cost_goal * 1.2))
                up_num = 3
            elif account_last_1day_reservation_cost <= final_cost_goal * 1.6:
                print ('--- 账户: 前一天转化成本 %.2f <=  %.2f, 起量2个' % (account_last_1day_reservation_cost, final_cost_goal * 1.6))
                up_num = 2
            else:
                print ('--- 账户: 前一天转化成本 %.2f >  %.2f, 起量1个' % (account_last_1day_reservation_cost, final_cost_goal * 1.6))
                up_num = 1
                up_new_adgroup_num = 0
        else:
            if account_last_1day_cost <= final_cost_goal * 1.2:
                print ('--- 账户: 前一天无转化, 消耗 %.2f <= %.2f, 起量3个' % (account_last_1day_cost, final_cost_goal * 1.2))
                up_num = 3
            elif account_last_1day_cost <= final_cost_goal * 1.6:
                print ('--- 账户: 前一天无转化, 消耗 %.2f <= %.2f, 起量2个' % (account_last_1day_cost, final_cost_goal * 1.6))
                up_num = 2
            else:
                print ('--- 账户: 前一天无转化, 消耗 %.2f > %.2f, 起量1个' % (account_last_1day_cost, final_cost_goal * 1.6))
                up_num = 1
                up_new_adgroup_num = 0
        print ('--- 老账户: 总转化数 %d' % account_total_reservation_uv)
    elif account_id in median_accounts:
        # up_num = up_all_num_for_median
        if account_last_1day_reservation_uv > 0:
            if account_last_1day_reservation_cost <= final_cost_goal * 1.4:
                print ('--- 账户: 前一天转化成本 %.2f <= %.2f, 起量2个' % (account_last_1day_reservation_cost, final_cost_goal * 1.4))
                up_num = 2
            else:
                print ('--- 账户: 前一天转化成本 %.2f > %.2f, 起量1个' % (account_last_1day_reservation_cost, final_cost_goal * 1.4))
                up_num = 1
                up_new_adgroup_num = 0
        else:
            if account_last_1day_cost <= final_cost_goal * 1.4:
                print ('--- 账户: 前一天无转化, 消耗 %.2f <= %.2f, 起量2个' % (account_last_1day_cost, final_cost_goal * 1.4))
                up_num = 2
            else:
                print ('--- 账户: 前一天无转化, 消耗 %.2f > %.2f, 起量1个' % (account_last_1day_cost, final_cost_goal * 1.4))
                up_num = 1
                up_new_adgroup_num = 0
        print ('--- 中等账户: 总转化数 %d' % account_total_reservation_uv)
    else:
        print ('--- 新账户: 总转化数 %d' % account_total_reservation_uv)
        up_num = up_all_num_for_new

    up_num_till_now = 0
    for adgroup_id in adgroup_ids:
        if adgroup_id not in adgroup_status:
            print ('--- %s adgroup status not found ...' % str(adgroup_id))
            continue
        if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
            up_num_till_now += 1
    if up_num_till_now > 0:
        if up_num > 2:
            up_num = 2
            print ('--- 已经有提前起量广告, 起量广告数设置减少为2...')

    return 1, up_num, up_new_adgroup_num

def rank_up_ads(data, final_cost_goal, adgroup_reservation_cost_limit, week_day):
    
    # rank
    # 1: 前一天自己跑量好的广告
    # 2: 前一天起量好的广告
    # 3: 总成本好的广告
    # 4: 没有起过量的广告
    
    if check_not_up_ads(data, final_cost_goal, adgroup_reservation_cost_limit, week_day) == 0:
        rank = 999
        msg = '不符合起量约束...'
        up_budget = 0
        reservation_uv = 0
        resevation_cost = 0
    elif data['past_1day_up'] == 0 and data['past_1day_cost'] >= final_cost_goal * 0.5 and \
        data['past_1day_reservation_uv'] >= 1 and data['past_1day_reservation_cost'] <= adgroup_reservation_cost_limit:
        rank = 1
        msg = '开启一键起量： 前一天没有起量,  前一天转化数 %d >=1, 前一天转化成本 %.2f <=%d' % \
                        (data['past_1day_reservation_uv'], data['past_1day_reservation_cost'], adgroup_reservation_cost_limit)
        if data['past_1day_reservation_uv'] >=2:
            up_budget = 400000
        else:
            up_budget = 300000
        reservation_uv = data['past_1day_reservation_uv']
        resevation_cost = data['past_1day_reservation_cost']
        
    elif week_day == '星期一' and data['past_3day_up']==1 and data['past_3day_reservation_uv'] > 0 \
                              and data['past_3day_reservation_cost'] < adgroup_reservation_cost_limit: # 查看星期五起量效果
        print ('--- 星期一, 查看星期五起量效果')
        rank = 2
        msg = '开启一键起量： 前星期五起量,  前星期五转化数 %d >前星期五转化成本 %.2f <=%d' % \
            (data['past_3day_reservation_uv'], data['past_3day_reservation_cost'], adgroup_reservation_cost_limit)
        if data['past_3day_reservation_cost'] <= final_cost_goal:
            up_budget = 400000
        else:
            up_budget = 300000
        reservation_uv = data['past_3day_reservation_uv']
        resevation_cost = data['past_3day_reservation_cost']

    elif week_day != '星期一' and data['past_1day_up']==1 and data['past_1day_reservation_uv'] > 0 \
                              and data['past_1day_reservation_cost'] < adgroup_reservation_cost_limit: # 查看前一天起量效果
        print ('--- 非星期一, 查看前一天起量效果')
        rank = 2
        msg = '开启一键起量： 前一天起量,  前一天转化数 %d >=0, 前一天转化成本 %.2f <=%d' % \
            (data['past_1day_reservation_uv'], data['past_1day_reservation_cost'], adgroup_reservation_cost_limit)
        if data['past_1day_reservation_cost'] <= final_cost_goal:
            up_budget = 400000
        else:
            up_budget = 300000
        reservation_uv = data['past_1day_reservation_uv']
        resevation_cost = data['past_1day_reservation_cost']

    elif ((week_day != '星期一' and data['past_1day_reservation_uv'] > 2) or \
            (week_day == '星期一' and data['past_3day_reservation_uv'] > 2)) \
            and data['total_reservation_cost'] <= final_cost_goal * 1.2:
        rank = 3
        up_budget = 300000
        if week_day == '星期一':
            reservation_uv = data['past_3day_reservation_uv']
            resevation_cost = data['past_3day_reservation_cost']
        else:
            reservation_uv = data['past_1day_reservation_uv']
            resevation_cost = data['past_1day_reservation_cost']
        msg = '开启一键起量: 前一天转化数 %d >2, 总转化成本 %.2f <=%.2f' % (reservation_uv, data['total_reservation_cost'], final_cost_goal * 1.2)

    elif ((week_day != '星期一' and data['past_1day_reservation_uv'] > 0) or \
            (week_day == '星期一' and data['past_3day_reservation_uv'] > 0)) \
                and (data['account_past_7day_reservation_uv'] > 0 and data['account_past_7day_reservation_cost'] <= final_cost_goal):
        rank = 3
        up_budget = 300000
        msg = '开启一键起量： 前一天转化数 %d >0, 账户总转化成本 %.2f <=440' % (data['past_1day_reservation_uv'], data['account_past_7day_reservation_cost'])
        if week_day == '星期一':
            reservation_uv = data['past_3day_reservation_uv']
            resevation_cost = data['past_3day_reservation_cost']
        else:
            reservation_uv = data['past_1day_reservation_uv']
            resevation_cost = data['past_1day_reservation_cost']
    elif data['total_reservation_uv'] >= 1 and data['total_reservation_cost'] <= adgroup_reservation_cost_limit:
        rank = 3
        up_budget = 300000
        msg = '开启一键起量： 总转化数 %d >=1, 总转化成本 %.2f <=%d' % \
                (data['total_reservation_uv'], data['total_reservation_cost'], adgroup_reservation_cost_limit)
        reservation_uv = data['total_reservation_uv']
        resevation_cost = data['total_reservation_cost']
    elif data['is_ever_up'] == 0 and data['life_long_days'] <= 3:
        rank = 4
        up_budget = 300000
        msg = '开启一键起量： 没有起量的广告'
        reservation_uv = data['total_reservation_uv']
        resevation_cost = data['total_reservation_cost']
    else:
        rank = 999
        msg = '不符合起量要求...'
        up_budget = 0
        reservation_uv = 0
        resevation_cost = 0

    return rank, up_budget, msg, reservation_uv, resevation_cost

def check_not_up_ads(data, final_cost_goal, adgroup_reservation_cost_limit, week_day):
    if data['is_ever_up'] == 0 and data['life_long_reservation_uv'] < 1 and data['life_long_days'] > 3: # 没有起过量, 转化数<1, 且上线时间超过3天, 已经过保障期
        print ('没有起过量, 总转化数 %d<1, 且上线天数 %d超过3天' % (data['life_long_reservation_uv'], data['life_long_days']))
        return 0
    if data['total_reservation_cost'] > adgroup_reservation_cost_limit: # 整体成本超标
        print ('超本: 成本 %.2f > %.2f' % (data['total_reservation_cost'], adgroup_reservation_cost_limit))
        return 0
    if data['past_1day_up'] == 1 and data['past_1day_reservation_uv'] == 0: #前一天起量没有转化
        print ('前一天起量没有转化')
        return 0
    if data['past_1day_up'] == 1 and data['past_1day_reservation_cost'] > adgroup_reservation_cost_limit: #前一天起量超本
        print ('前一天起量超本: 前一天成本 %.2f > %.2f' % (data['past_1day_reservation_cost'], adgroup_reservation_cost_limit))
        return 0
    if week_day == '星期一':
        if data['past_3day_up'] == 1 and data['past_3day_reservation_uv'] == 0: #前一天起量没有转化
            print ('前星期五起量没有转化')
            return 0
        if data['past_3day_up'] == 1 and data['past_3day_reservation_cost'] > adgroup_reservation_cost_limit: #前一天起量没有转化
            print ('前星期五起量超本: 前星期五天成本 %.2f > %.2f' % (data['past_3day_reservation_cost'], adgroup_reservation_cost_limit))
            return 0
    if data['is_ever_up'] == 1 and \
            (str(datetime.datetime.now() + datetime.timedelta(days=-7)).split(' ')[0] <= data['last_up_date']) and \
            data['total_cost'] < final_cost_goal * 0.5:
        msg = '不进行一键起量: -- 近7天起过量但是低消 近7天总消耗 %.2f < %.2f' % (data['total_cost'], final_cost_goal * 0.5)
        print (msg)
        return 0
    if data['is_ever_up'] == 1 and \
            (str(datetime.datetime.now() + datetime.timedelta(days=-14)).split(' ')[0] <= data['last_up_date']) and \
            ((data['last_up_reservation_uv'] == 0) or \
                (data['last_up_reservation_uv'] > 0 and data['last_up_reservation_cost'] > final_cost_goal * 2)):
        msg = '不进行一键起量： 近14天 最近一次起量转化数 %d, 转化消耗 %.2f, 转化成本 %d >%d' % \
            (data['last_up_reservation_uv'], data['last_up_cost'], data['last_up_reservation_cost'], final_cost_goal * 2)
        print (msg)
        return 0
    return 1
    
def get_up_adgroup_ids(select_account_id, adgroup_status, scale_ratio = 2, add_new = True): # 偏保守
    
    # 配置
    final_cost_goal = int(year_target * scale_ratio) # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2 # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3
    up_new_adgroup_num = 1 # 最多选择起量的从未起量广告数
    
    print ('---- 保守起量')
    print ('--- 基于历史数据, 寻找可以一键起量的广告 ...')
    print ('--- final_cost_goal: ', final_cost_goal)
    print ('--- adgroup_reservation_cost_limit: ', adgroup_reservation_cost_limit)
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    week_day = get_weekday(day)
    print ('--- week_day: ', week_day)
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()

        account_check, total_up_num, up_new_adgroup_num = check_account_over_budget(account_id, adgroup_iadgroup_data, adgroup_status, final_cost_goal)

        if account_check == 0: # 检查账号是否前一天超本
            res[account_id] = []
            continue

        print ('--- 最大一键起量数:', total_up_num)
        print ('--- 最大从未起量广告的一键起量数:', up_new_adgroup_num)
        
        select_up_adgroup_ids = []
        candidates = {'adgroup_id':[], 'created_time':[], 'rank':[], 'up_budget':[], 'reservation_uv':[], 'reservation_cost':[],'msg':[]}
        for adgroup_id in adgroup_ids:

            if check_if_meet_up_condition(adgroup_id, adgroup_status, adgroup_data) == 0: # 是否满足起量前提
                continue

            data = adgroup_data[adgroup_id]
            rank, up_budget, msg, reservation_uv, resevation_cost = rank_up_ads(data, final_cost_goal, adgroup_reservation_cost_limit, week_day)
            print ('***', adgroup_id, rank, up_budget, msg, reservation_uv, resevation_cost)

            candidates['adgroup_id'].append(adgroup_id)
            candidates['created_time'].append(data['created_time'])
            candidates['reservation_uv'].append(reservation_uv)
            candidates['reservation_cost'].append(resevation_cost)
            candidates['up_budget'].append(up_budget)
            candidates['rank'].append(rank)
            candidates['msg'].append(msg)

        candidates = pd.DataFrame(candidates)

        candidates['reservation_uv_norm'] = candidates['reservation_uv']/np.sum(candidates['reservation_uv'])
        candidates['score'] = candidates['reservation_uv_norm'] / candidates['reservation_cost'] * -1
        candidates = candidates.sort_values(['rank','score'])

        print (candidates[['adgroup_id', 'created_time', 'rank', 'score']])

        # rank
        # 1: 前一天自己跑量好的广告
        # 2: 前一天起量好的广告
        # 3: 总成本好的广告
        # 4: 没有起过量的广告

        if account_id in old_accounts + median_accounts:
            rank_list = [1, 2, 3]
        else:
            rank_list = [1, 2]

        while True:
            for r in rank_list:
                temp = candidates[candidates['rank'] == r]
                if len(temp):
                    for i in range(len(temp)):
                        select_up_adgroup_ids.append(['startAutoAcquisition', account_id, temp['adgroup_id'].iloc[i], \
                                                temp['msg'].iloc[i], temp['up_budget'].iloc[i]])
                        break # 每个rank每次添加一个
                    if len(select_up_adgroup_ids) >= total_up_num - up_new_adgroup_num:
                        break
            if len(select_up_adgroup_ids) == 0 or len(select_up_adgroup_ids) >= total_up_num - up_new_adgroup_num:
                break

        new_count = 0
        if len(select_up_adgroup_ids) < total_up_num and add_new:
            print ('--- 添加新广告起量 ...')
            temp = candidates[candidates['rank'] == 4]
            temp = temp.sort_values(['created_time'], ascending=False)
            if len(temp):
                for i in range(len(temp)):
                    select_up_adgroup_ids.append(['startAutoAcquisition', account_id, temp['adgroup_id'].iloc[i], \
                                            temp['msg'].iloc[i], temp['up_budget'].iloc[i]])
                    new_count += 1
                    if len(select_up_adgroup_ids) >= total_up_num or new_count == up_new_adgroup_num:
                        break

        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        
        res[account_id] = select_up_adgroup_ids

    return res

def get_up_adgroup_ids_for_all_morning(select_account_id, adgroup_status, account_status, scale_ratio = 1): # 方案1

    print ('--------------------------------------')
    print ('--- 上午起量')
    
    # 配置
    follow_final_cost_goal = int(account_status['past2WeeksFollowCost'] * scale_ratio)
    register_final_cost_goal = int(account_status['past2WeeksRegCost'] * scale_ratio)
    reservation_final_cost_goal = int(account_status['past2WeeksReservationCost'] * scale_ratio)

    
    adgroup_follow_cost_limit = follow_final_cost_goal * 1.2
    adgroup_register_cost_limit = register_final_cost_goal * 1.2
    adgroup_reservation_cost_limit = reservation_final_cost_goal * 1.2

    print ('--- 目标关注成本： ', follow_final_cost_goal)
    print ('--- 目标注册成本： ', register_final_cost_goal)
    print ('--- 目标表单预约成本： ', reservation_final_cost_goal)

    print ('--- 关注成本限制： ', adgroup_follow_cost_limit)
    print ('--- 目标注册成本限制： ', adgroup_register_cost_limit)
    print ('--- 目标表单预约成本限制： ', adgroup_reservation_cost_limit)
    
    
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)

        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        # account_last_1day_cost = 0
        # account_last_1day_reservation_uv = 0
        # for adgroup_id in adgroup_ids:
        #     data = adgroup_data[adgroup_id]
        #     account_past_7day_reservation_cost = data['account_past_7day_reservation_cost']
        #     account_past_3day_reservation_cost = data['account_past_3day_reservation_cost']
        #     account_past_3day_cost = data['account_past_3day_cost']
        #     account_total_reservation_uv = data['account_total_reservation_uv']
        #     if len(data['report_data']['ds']) > 0:
        #         account_last_1day_cost += data['report_data']['cost'][-1]
        #         account_last_1day_reservation_uv += data['report_data']['reservation_uv'][-1]
        
        # if account_last_1day_reservation_uv == 0 and account_last_1day_cost > final_cost_goal * 2:
        #     print ('--- 账号前一天总成本超标,  转化数 %d 消耗 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost))
        #     res[account_id] = []
        #     continue
        # if account_last_1day_reservation_uv > 0 and account_last_1day_cost/account_last_1day_reservation_uv > final_cost_goal * 2:
        #     print ('--- 账号前一天总成本超标,  转化数 %d 转化成本 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost/account_last_1day_reservation_uv))
        #     res[account_id] = []
        #     continue
        # if account_id in old_accounts:
        #     past_3day_cost_limit = final_cost_goal * 1.3
        # elif account_id in median_accounts:
        #     past_3day_cost_limit = final_cost_goal * 1.4
        # else:
        #     past_3day_cost_limit = final_cost_goal * 1.5
        # if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
        #     mark = 1
        #     if account_id in new_accounts:
        #         if random.random() < 0.5: # 新账户一定概率提高限制
        #             print ('--- 提高新账号近3天成本限制...')
        #             past_3day_cost_limit = final_cost_goal * 2
        #             if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
        #                 mark = 1
        #             else:
        #                 mark = 0
        #     if mark == 1:
        #         print ('--- 账号过去3天总成本超标,  总消耗 %.2f 转化成本 %.2f' % (account_past_3day_cost, account_past_3day_reservation_cost))
        #         # res[account_id] = []
        #         # continue


        # if account_total_reservation_uv >= 30: # 判断新老账号
        #     up_num = up_all_num_for_old
        #     print ('--- 老账户: 总转化数 %d' % account_total_reservation_uv)
        #     if account_past_7day_reservation_cost > 600: # 老账号成本太高, 不起量
        #         print ('--- 账户成本超过 %.2f 600...不起量' % account_past_7day_reservation_cost)
        #         res[account_id] = []
        #         continue
        # else:
        #     print ('--- 新账户: 总转化数 %d' % account_total_reservation_uv)
        #     up_num = up_all_num_for_new
        up_num = 1 # 所有账号只起量一个广告
        up_new_adgroup_num = 1 # 起量新广告数

        print ('--- 最大一键起量数:', up_num)
        print ('--- 最大从未起量广告的一键起量数:', up_new_adgroup_num)
        
        select_up_adgroup_ids = []
        final_select_adgroup_ids = []
        not_select_up_adgroup_ids = []
        active_num = 0
        candidates = {'adgroup_id':[], 'rank':[], 'created_time':[], 'today_cost':[], 'today_biz_follow_uv':[], 'today_biz_reg_uv':[], 'today_reservation_uv':[], 'msg':[]}
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
                print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['autoAcquisitionStatus'] == 'AUTO_ACQUISTION_STATUS_PENDING':
                print ('--- %s 正在起量中, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                print ('--- 不在投放状态, 不起量...')
                continue
            adgroup_name = data['adgroup_name']
            if adgroup_name[-2:] == 'ZD':
                print ('--- 最大转化成本广告, 不起量...')
                continue
            active_num += 1 
            ds_list = data['report_data']['ds']
            # if len(ds_list) == 0: # 没有历史数据
            #     continue
        
            rank = 0
            if data['today_cost'] >= register_final_cost_goal: # 消耗达到一个注册的成本
                if data['today_reservation_uv'] >= 1 and data['today_biz_reg_uv'] >= 1:
                    rank = 1
                    msg = '开启一键起量： 当天消耗 %.2f >= %.2f 目前表单数 %d >= 1, 并且当天注册数 %d >=1' % \
                        (data['today_cost'], register_final_cost_goal, data['today_reservation_uv'], data['today_biz_reg_uv'])
                    print (adgroup_id, msg)
                # elif data['today_biz_reg_uv'] >= 1 and data['today_biz_reg_cost'] <= adgroup_register_cost_limit:
                #     rank = 2
                #     msg = '开启一键起量： 当天目前注册数 %d >= 1, 且当天注册成本 %.2f <= %.2f' % \
                #         (data['today_biz_reg_uv'], data['today_biz_reg_cost'], adgroup_register_cost_limit)
                #     print (adgroup_id, msg)
            #         elif data['today_cost'] >= 700 and data['up_trend'] == 1:
            #             rank = 3
            #             msg = '开启一键起量： 当前消耗 %.2f > 700, 且成上升趋势' % \
            #                     (data['today_cost'])
            #             print (adgroup_id, msg)
            #         elif data['life_long_days'] <= 3 and data['is_ever_up'] == 0:
            #             rank = 4
            #             msg = '上线3天内新广告, 并且没有起过量'
            #             print (adgroup_id, msg)
            #         else:
            #             rank = 0 # 待选
            #     else:
            #         rank = 0

            if rank != 0:
                candidates['adgroup_id'].append(adgroup_id)
                candidates['rank'].append(rank)
                candidates['created_time'].append(data['created_time'])
                candidates['today_cost'].append(data['today_cost'])
                candidates['today_biz_follow_uv'].append(data['today_biz_follow_uv'])
                candidates['today_biz_reg_uv'].append(data['today_biz_reg_uv'])
                candidates['today_reservation_uv'].append(data['today_reservation_uv'])
                candidates['msg'].append(msg)

        print ('--- active num: ', active_num)

        candidates = pd.DataFrame(candidates)
        print (candidates)

        if account_id in old_accounts:
            up_budget = 2000000
        elif account_id in median_accounts:
            up_budget = 5000000
        else:
            up_budget = 3000000
        print ('--- 起量预算: ', up_budget)

    
        # 已经有表单
        temp = candidates[candidates['rank'] == 1]
        if len(temp):
            print ('--- 有表单广告 ...')
            temp = temp.sort_values('today_reservation_uv', ascending=False)
            print (temp)
            for i in range(len(temp)):
                select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i], up_budget])
                print (temp['adgroup_id'].iloc[i])
                final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                if len(select_up_adgroup_ids) == up_num:
                    break

        if len(select_up_adgroup_ids) < up_num:
            temp = candidates[candidates['rank'] == 2]
            if len(temp):
                print ('--- 有注册广告 ...')
                temp = temp.sort_values(['today_biz_reg_uv', 'today_biz_follow_uv'], ascending=False)
                print (temp)
                for i in range(len(temp)):
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i], up_budget])
                    print (temp['adgroup_id'].iloc[i])
                    final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                    break # 仅加入一个
                    # if len(select_up_adgroup_ids) == up_num:
                    #     break

        if len(select_up_adgroup_ids) < up_num:
            temp = candidates[candidates['rank'] == 3]
            if len(temp):
                print ('--- 有消耗上升趋势广告 ...')
                temp = temp.sort_values('today_cost', ascending=False)
                print (temp)
                for i in range(len(temp)):
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i], up_budget])
                    print (temp['adgroup_id'].iloc[i])
                    final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                    break # 仅加入一个
                    # if len(select_up_adgroup_ids) == up_num:
                    #     break

        # select_new_adgroup_id = []
        # if len(select_up_adgroup_ids) < up_num:
        #     temp = candidates[candidates['rank'] == 4]
        #     if len(temp):
        #         print ('--- 新创建广告, 且没有起过量 ...')
        #         temp = temp.sort_values(['created_time'], ascending=False)
        #         print (temp)
        #         for i in range(len(temp)):
        #             select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], \
        #                                         '开启一键起量：最新广告, 创建时间%s' % temp['created_time'].iloc[i], up_budget])
        #             select_new_adgroup_id.append(candidates['adgroup_id'].iloc[i])
        #             print (candidates['adgroup_id'].iloc[i], '开启一键起量：最新广告, 创建时间%s' % candidates['created_time'].iloc[i])
        #             if len(select_up_adgroup_ids) == up_num or len(select_new_adgroup_id) == up_new_adgroup_num:
        #                 break
        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        
        res[account_id] = select_up_adgroup_ids
        # return select_up_adgroup_ids
    return res

def get_up_adgroup_ids_for_all_afternoon(select_account_id, adgroup_status, account_status, scale_ratio = 1): # 方案1

    print ('--------------------------------------')
    print ('--- 下午起量')
    
    # 配置
    follow_final_cost_goal = int(account_status['past2WeeksFollowCost'] * scale_ratio)
    register_final_cost_goal = int(account_status['past2WeeksRegCost'] * scale_ratio)
    reservation_final_cost_goal = int(account_status['past2WeeksReservationCost'] * scale_ratio)

    
    adgroup_follow_cost_limit = follow_final_cost_goal * 1.2
    adgroup_register_cost_limit = register_final_cost_goal * 1.2
    adgroup_reservation_cost_limit = reservation_final_cost_goal * 1.2

    print ('--- 目标关注成本： ', follow_final_cost_goal)
    print ('--- 目标注册成本： ', register_final_cost_goal)
    print ('--- 目标表单预约成本： ', reservation_final_cost_goal)

    print ('--- 关注成本限制： ', adgroup_follow_cost_limit)
    print ('--- 目标注册成本限制： ', adgroup_register_cost_limit)
    print ('--- 目标表单预约成本限制： ', adgroup_reservation_cost_limit)
    
    
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:

        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        # account_last_1day_cost = 0
        # account_last_1day_reservation_uv = 0
        # for adgroup_id in adgroup_ids:
        #     data = adgroup_data[adgroup_id]
        #     account_past_7day_reservation_cost = data['account_past_7day_reservation_cost']
        #     account_past_3day_reservation_cost = data['account_past_3day_reservation_cost']
        #     account_past_3day_cost = data['account_past_3day_cost']
        #     account_total_reservation_uv = data['account_total_reservation_uv']
        #     if len(data['report_data']['ds']) > 0:
        #         account_last_1day_cost += data['report_data']['cost'][-1]
        #         account_last_1day_reservation_uv += data['report_data']['reservation_uv'][-1]
        
        # if account_last_1day_reservation_uv == 0 and account_last_1day_cost > final_cost_goal * 2:
        #     print ('--- 账号前一天总成本超标,  转化数 %d 消耗 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost))
        #     res[account_id] = []
        #     continue
        # if account_last_1day_reservation_uv > 0 and account_last_1day_cost/account_last_1day_reservation_uv > final_cost_goal * 2:
        #     print ('--- 账号前一天总成本超标,  转化数 %d 转化成本 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost/account_last_1day_reservation_uv))
        #     res[account_id] = []
        #     continue
        # if account_id in old_accounts:
        #     past_3day_cost_limit = final_cost_goal * 1.3
        # elif account_id in median_accounts:
        #     past_3day_cost_limit = final_cost_goal * 1.4
        # else:
        #     past_3day_cost_limit = final_cost_goal * 1.5
        # if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
        #     mark = 1
        #     if account_id in new_accounts:
        #         if random.random() < 0.5: # 新账户一定概率提高限制
        #             print ('--- 提高新账号近3天成本限制...')
        #             past_3day_cost_limit = final_cost_goal * 2
        #             if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
        #                 mark = 1
        #             else:
        #                 mark = 0
        #     if mark == 1:
        #         print ('--- 账号过去3天总成本超标,  总消耗 %.2f 转化成本 %.2f' % (account_past_3day_cost, account_past_3day_reservation_cost))
        #         # res[account_id] = []
        #         # continue


        # if account_total_reservation_uv >= 30: # 判断新老账号
        #     up_num = up_all_num_for_old
        #     print ('--- 老账户: 总转化数 %d' % account_total_reservation_uv)
        #     if account_past_7day_reservation_cost > 600: # 老账号成本太高, 不起量
        #         print ('--- 账户成本超过 %.2f 600...不起量' % account_past_7day_reservation_cost)
        #         res[account_id] = []
        #         continue
        # else:
        #     print ('--- 新账户: 总转化数 %d' % account_total_reservation_uv)
        #     up_num = up_all_num_for_new
        up_num = 1 # 所有账号只起量一个广告

        print ('--- 下午最大一键起量数:', up_num)
        
        select_up_adgroup_ids = []
        final_select_adgroup_ids = []
        not_select_up_adgroup_ids = []
        active_num = 0
        candidates = {'adgroup_id':[], 'rank':[], 'created_time':[], 'past_1day_reservation_uv':[], 'today_reservation_uv':[], 'today_biz_reg_uv':[], 'msg':[]}
        for adgroup_id in adgroup_ids:

            if adgroup_id not in adgroup_status:
                print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
                print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['autoAcquisitionStatus'] == 'AUTO_ACQUISTION_STATUS_PENDING':
                print ('--- %s 正在起量中, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                print ('--- 不在投放状态, 不起量...')
                continue
            adgroup_name = data['adgroup_name']
            if adgroup_name[-2:] == 'ZD':
                print ('--- 最大转化成本广告, 不起量...')
                continue
            active_num += 1 
            ds_list = data['report_data']['ds']
            # if len(ds_list) == 0: # 没有历史数据
            #     continue
        
            if data['today_reservation_uv'] >= 1 and data['today_reservation_cost'] <= adgroup_reservation_cost_limit:
                rank = 1
                msg = '开启一键起量： 当天表单数 %d >= 1 表单成本 %.2f <= %.2f' % \
                    (data['today_reservation_uv'], data['today_reservation_cost'], adgroup_reservation_cost_limit)
                print (adgroup_id, msg)
            # elif 'past_3day_reservation_uv' in data.keys() and data['past_3day_reservation_uv'] >= 1 \
            #         and data['past_3day_reservation_cost'] <= adgroup_reservation_cost_limit and \
            #             data['today_biz_reg_uv'] >= 1:
            #     rank = 2
            #     msg = '开启一键起量： 前三天表单数 %d >= 1 前三天表单成本 %.2f <= %.2f 当天注册数 %d >= 1' % \
            #         (data['past_3day_reservation_uv'], data['past_3day_reservation_cost'], \
            #             adgroup_reservation_cost_limit, data['today_biz_reg_uv'])
            #     print (adgroup_id, msg)
            # elif data['today_biz_reg_uv'] >= 1 and data['today_biz_reg_cost'] <= adgroup_register_cost_limit:
            #         rank = 3
            #         msg = '开启一键起量： 当天目前注册数 %d >= 1, 且当天注册成本 %.2f <= %.2f' % \
            #             (data['today_biz_reg_uv'], data['today_biz_reg_cost'], adgroup_register_cost_limit)
            else:
                rank = 0 # 待选

            if rank != 0:
                candidates['adgroup_id'].append(adgroup_id)
                candidates['rank'].append(rank)
                candidates['created_time'].append(data['created_time'])
                if 'past_1day_reservation_uv' in data.keys():
                    candidates['past_1day_reservation_uv'].append(data['past_1day_reservation_uv'])
                else:
                    candidates['past_1day_reservation_uv'].append(0)
                candidates['today_reservation_uv'].append(data['today_reservation_uv'])
                candidates['today_biz_reg_uv'].append(data['today_biz_reg_uv'])
                candidates['msg'].append(msg)
           
        print ('--- active num: ', active_num)

        candidates = pd.DataFrame(candidates)
        print (candidates)

        if account_id in old_accounts:
            up_budget = 2000000
        elif account_id in median_accounts:
            up_budget = 5000000
        else:
            up_budget = 3000000
        print ('--- 起量预算: ', up_budget)

        # 已经有表单
        temp = candidates[candidates['rank'] == 1]
        if len(temp):
            print ('--- 有表单广告 ...')
            temp = temp.sort_values('today_reservation_uv', ascending=False)
            print (temp)
            for i in range(len(temp)):
                select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i], up_budget])
                print (temp['adgroup_id'].iloc[i])
                final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                if len(select_up_adgroup_ids) == up_num:
                    break

        if len(select_up_adgroup_ids) < up_num:
            temp = candidates[candidates['rank'] == 2]
            if len(temp):
                print ('--- 前一天有表单广告 ...')
                temp = temp.sort_values('past_1day_reservation_uv', ascending=False)
                print (temp)
                for i in range(len(temp)):
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i], up_budget])
                    print (temp['adgroup_id'].iloc[i])
                    final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                    if len(select_up_adgroup_ids) == up_num:
                        break

        if len(select_up_adgroup_ids) < up_num:
            temp = candidates[candidates['rank'] == 3]
            if len(temp):
                print ('--- 有注册广告 ...')
                temp = temp.sort_values('today_biz_reg_uv', ascending=False)
                print (temp)
                for i in range(len(temp)):
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i], up_budget])
                    print (temp['adgroup_id'].iloc[i])
                    final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                    if len(select_up_adgroup_ids) == up_num:
                        break

        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        
        res[account_id] = select_up_adgroup_ids
        # return select_up_adgroup_ids
    return res

def get_up_adgroup_ids_for_all_1(select_account_id, adgroup_status, scale_ratio = 2): # 偏保守
    
    # 配置
    final_cost_goal = int(year_target * scale_ratio) # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2 # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3
    up_new_adgroup_num = 1 # 最多选择起量的从未起量广告数
    
    print ('---- 保守起量')
    print ('--- 基于历史数据, 寻找可以一键起量的广告 ...')
    print ('--- final_cost_goal: ', final_cost_goal)
    print ('--- adgroup_reservation_cost_limit: ', adgroup_reservation_cost_limit)
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)

        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        account_last_1day_cost = 0
        account_last_1day_reservation_uv = 0
        for adgroup_id in adgroup_ids:
            data = adgroup_data[adgroup_id]
            account_past_7day_reservation_cost = data['account_past_7day_reservation_cost']
            account_past_3day_reservation_cost = data['account_past_3day_reservation_cost']
            account_past_3day_cost = data['account_past_3day_cost']
            account_total_reservation_uv = data['account_total_reservation_uv']
            if len(data['report_data']['ds']) > 0:
                account_last_1day_cost += data['report_data']['cost'][-1]
                account_last_1day_reservation_uv += data['report_data']['reservation_uv'][-1]
        
        if account_last_1day_reservation_uv == 0 and account_last_1day_cost > final_cost_goal * 2:
            print ('--- 账号前一天总成本超标,  转化数 %d 消耗 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost))
            res[account_id] = []
            continue
        if account_last_1day_reservation_uv > 0 and account_last_1day_cost/account_last_1day_reservation_uv > final_cost_goal * 2:
            print ('--- 账号前一天总成本超标,  转化数 %d 转化成本 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost/account_last_1day_reservation_uv))
            res[account_id] = []
            continue
        if account_id in old_accounts:
            past_3day_cost_limit = final_cost_goal * 1.3
        elif account_id in median_accounts:
            past_3day_cost_limit = final_cost_goal * 1.4
        else:
            past_3day_cost_limit = final_cost_goal * 1.5
        if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
            mark = 1
            if account_id in new_accounts:
                if random.random() < 0.5: # 新账户一定概率提高限制
                    print ('--- 提高新账号近3天成本限制...')
                    past_3day_cost_limit = final_cost_goal * 2
                    if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
                        mark = 1
                    else:
                        mark = 0
            if mark == 1:
                print ('--- 账号过去3天总成本超标,  总消耗 %.2f 转化成本 %.2f' % (account_past_3day_cost, account_past_3day_reservation_cost))
                # res[account_id] = []
                # continue


        # if account_total_reservation_uv >= 30: # 判断新老账号
        #     up_num = up_all_num_for_old
        #     print ('--- 老账户: 总转化数 %d' % account_total_reservation_uv)
        #     if account_past_7day_reservation_cost > 600: # 老账号成本太高, 不起量
        #         print ('--- 账户成本超过 %.2f 600...不起量' % account_past_7day_reservation_cost)
        #         res[account_id] = []
        #         continue
        # else:
        #     print ('--- 新账户: 总转化数 %d' % account_total_reservation_uv)
        #     up_num = up_all_num_for_new
        up_num = up_all_num_for_new # 所有账号只起量一个广告

        if '1306' in account_id:
            up_num = 4
            up_new_adgroup_num = 4

        print ('--- 最大一键起量数:', up_num)
        print ('--- 最大从未起量广告的一键起量数:', up_new_adgroup_num)
        
        select_up_adgroup_ids = []
        final_select_adgroup_ids = []
        not_select_up_adgroup_ids = []
        active_num = 0
        candidates = {'adgroup_id':[], 'created_time':[],'past_1day_reservation_uv':[], 'past_1day_cost':[], 'past_1day_reservation_cost':[],'msg':[]}
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
                print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                continue
            adgroup_name = data['adgroup_name']
            if adgroup_name[-2:] == 'ZD':
                print ('--- 最大转化成本广告, 不起量...')
                continue
            active_num += 1 
            ds_list = data['report_data']['ds']
            if len(ds_list) == 0: # 没有历史数据
                continue
        
            if data['is_ever_up'] == 1 and \
                    ((data['last_up_reservation_uv'] == 0) or \
                     (data['last_up_reservation_uv'] > 0 and data['last_up_reservation_cost'] > final_cost_goal * 2)):
                mark = -1
                msg = '不进行一键起量： 最近一次起量转化数 %d, 转化消耗 %.2f, 转化成本 %d >%d' % \
                    (data['last_up_reservation_uv'], data['last_up_cost'], data['last_up_reservation_cost'], final_cost_goal * 2)
                print (adgroup_id, msg)
            elif data['past_1day_up'] == 0 and data['past_1day_cost'] >= final_cost_goal * 0.5 and \
                data['past_1day_reservation_uv'] >= 1 and data['past_1day_reservation_cost'] <= adgroup_reservation_cost_limit:
                # if data['is_ever_up'] == 1 and data['past_1day_reservation_cost'] <= final_cost_goal * 2: # adgroup_reservation_cost_limit * 1.2
                mark = 1
                msg = '开启一键起量： 前一天没有起量,  前一天转化数 %d >=1, 前一天转化成本 %.2f <=%d' % \
                        (data['past_1day_reservation_uv'], data['past_1day_reservation_cost'], adgroup_reservation_cost_limit)
                print (adgroup_id, msg)
            else:
                mark = 0 # 待选

            if mark == 1:
                candidates['adgroup_id'].append(adgroup_id)
                candidates['created_time'].append(data['created_time'])
                candidates['past_1day_reservation_uv'].append(data['past_1day_reservation_uv'])
                candidates['past_1day_cost'].append(data['past_1day_cost'])
                candidates['past_1day_reservation_cost'].append(data['past_1day_reservation_cost'])
                candidates['msg'].append(msg)
            elif mark == 0:
                not_select_up_adgroup_ids.append(adgroup_id)

        print ('--- active num: ', active_num)

        candidates = pd.DataFrame(candidates)
        candidates = candidates.sort_values('past_1day_reservation_uv', ascending=False)
        print (candidates)
        for i in range(len(candidates)):
            print ('--- 补充前一天自己跑量好的广告')
            if candidates['past_1day_reservation_uv'].iloc[i] >=2:
                up_budget = 400000
            else:
                up_budget = 300000
            select_up_adgroup_ids.append(['startAutoAcquisition',account_id, candidates['adgroup_id'].iloc[i], candidates['msg'].iloc[i], up_budget])
            print (candidates['adgroup_id'].iloc[i])
            final_select_adgroup_ids.append(candidates['adgroup_id'].iloc[i])
            if len(select_up_adgroup_ids) == up_num:
                break

        if len(select_up_adgroup_ids) < up_num:
            candidates = {'adgroup_id':[], 'created_time':[], 'life_long_days':[], 'total_reservation_cost':[], \
                          'past_1day_up':[], 'past_1day_reservation_uv':[],'past_1day_reservation_cost':[], \
                          'past_3day_up':[], 'past_3day_reservation_uv':[],'past_3day_reservation_cost':[], \
                          'msg':[]}
            for adgroup_id in not_select_up_adgroup_ids:
                data = adgroup_data[adgroup_id]
                candidates['adgroup_id'].append(adgroup_id)
                candidates['created_time'].append(data['created_time'])
                candidates['life_long_days'].append(data['life_long_days'])
                candidates['total_reservation_cost'].append(data['total_reservation_cost'])
                candidates['past_1day_up'].append(data['past_1day_up'])
                candidates['past_3day_up'].append(data['past_3day_up'])
                candidates['past_1day_reservation_uv'].append(data['past_1day_reservation_uv'])
                candidates['past_3day_reservation_uv'].append(data['past_3day_reservation_uv'])
                candidates['past_1day_reservation_cost'].append(data['past_1day_reservation_cost'])
                candidates['past_3day_reservation_cost'].append(data['past_3day_reservation_cost'])
                candidates['msg'].append('从未起量')
            candidates = pd.DataFrame(candidates)
            today = str(datetime.datetime.now()).split(' ')[0]
            week_day = get_weekday(today)
            if week_day == '星期一': # 查看星期五起量效果
               print ('--- 星期一, 查看星期五起量效果')
               temp = candidates[(candidates['past_3day_up']==1) & (candidates['past_3day_reservation_uv'] > 0) \
                              & (candidates['past_3day_reservation_cost'] < adgroup_reservation_cost_limit)]
            else:
               temp = candidates[(candidates['past_1day_up']==1) & (candidates['past_1day_reservation_uv'] > 0) \
                              & (candidates['past_1day_reservation_cost'] < adgroup_reservation_cost_limit)]
            if len(temp):
                if week_day == '星期一': # 查看星期五起量效果
                    temp = temp.sort_values(['past_3day_reservation_uv'], ascending=False)
                else:
                    temp = temp.sort_values(['past_1day_reservation_uv'], ascending=False)
                print ('--- 补充前一天起量好的广告')
                print (temp)
                for i in range(len(temp)):
                    if week_day == '星期一': # 查看星期五起量效果
                        msg = '开启一键起量： 前星期五起量,  前星期五转化数 %d >前星期五转化成本 %.2f <=%d' % \
                            (temp['past_3day_reservation_uv'].iloc[i], temp['past_3day_reservation_cost'].iloc[i], adgroup_reservation_cost_limit)
                        if temp['past_3day_reservation_cost'].iloc[i]/temp['past_3day_reservation_uv'].iloc[i] <= final_cost_goal:
                            up_budget = 400000
                        else:
                            up_budget = 300000
                    else:
                        msg = '开启一键起量： 前一天起量,  前一天转化数 %d >=0, 前一天转化成本 %.2f <=%d' % \
                            (temp['past_1day_reservation_uv'].iloc[i], temp['past_1day_reservation_cost'].iloc[i], adgroup_reservation_cost_limit)
                        if temp['past_1day_reservation_cost'].iloc[i]/temp['past_1day_reservation_uv'].iloc[i] <= final_cost_goal:
                            up_budget = 400000
                        else:
                            up_budget = 300000
                    
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], msg, up_budget])
                    final_select_adgroup_ids.append(candidates['adgroup_id'].iloc[i])
                    print (temp['adgroup_id'].iloc[i], msg)
                    if len(select_up_adgroup_ids) == up_num:
                        break
            if len(select_up_adgroup_ids) < up_num:
                # candidates['cost'] = candidates['cost'] * -1
                # candidates = candidates.sort_values(['created_time', 'cost'])
                select_new_adgroup_id =[]
                candidates = candidates.sort_values(['created_time'], ascending=False)
                print ('--- 按照创建时间排序')
                print (candidates)
                for i in range(len(candidates)):
                    if candidates['adgroup_id'].iloc[i] in final_select_adgroup_ids:
                        print (candidates['adgroup_id'].iloc[i], '已选择')
                        continue
                    if candidates['life_long_days'].iloc[i] > 3:
                        print (candidates['adgroup_id'].iloc[i], '上线超过3天')
                        continue
                    if candidates['total_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: # 整体成本超标
                        print (candidates['adgroup_id'].iloc[i], '超本')
                        continue
                    if candidates['past_1day_up'].iloc[i] == 1 and candidates['past_1day_reservation_uv'].iloc[i] == 0: #前一天起量没有转化
                        print (candidates['adgroup_id'].iloc[i], '前一天起量没有转化')
                        continue
                    if candidates['past_1day_up'].iloc[i] == 1 and candidates['past_1day_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: #前一天起量超本
                        print (candidates['adgroup_id'].iloc[i], '前一天起量超本')
                        continue
                    if week_day == '星期一':
                        if candidates['past_3day_up'].iloc[i] == 1 and candidates['past_3day_reservation_uv'].iloc[i] == 0: #前一天起量没有转化
                            print (candidates['adgroup_id'].iloc[i], '前星期五起量没有转化')
                            continue
                        if candidates['past_3day_up'].iloc[i] == 1 and candidates['past_3day_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: #前一天起量没有转化
                            print (candidates['adgroup_id'].iloc[i], '前星期五起量超本')
                            continue
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, candidates['adgroup_id'].iloc[i], \
                                                  '开启一键起量：最新广告, 创建时间%s' % candidates['created_time'].iloc[i], 300000])
                    select_new_adgroup_id.append(candidates['adgroup_id'].iloc[i])
                    print (candidates['adgroup_id'].iloc[i], '开启一键起量：最新广告, 创建时间%s' % candidates['created_time'].iloc[i])
                    if len(select_up_adgroup_ids) == up_num or len(select_new_adgroup_id) == up_new_adgroup_num:
                        break
        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        
        res[account_id] = select_up_adgroup_ids
        # return select_up_adgroup_ids
    return res

def get_up_adgroup_ids_for_all_2(select_account_id, adgroup_status, scale_ratio = 2): # 偏激进
    
    # 配置
    final_cost_goal = int(year_target * scale_ratio) # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2 # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3
    up_new_adgroup_num = 1 # 最多选择起量的从未起量广告数
    
    print ('--- 基于历史数据, 寻找可以一键起量的广告 ...')
    print ('--- final_cost_goal: ', final_cost_goal)
    print ('--- adgroup_reservation_cost_limit: ', adgroup_reservation_cost_limit)
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        account_last_1day_cost = 0
        account_last_1day_reservation_uv = 0
        for adgroup_id in adgroup_ids:
            data = adgroup_data[adgroup_id]
            account_past_7day_reservation_cost = data['account_past_7day_reservation_cost']
            account_past_3day_reservation_cost = data['account_past_3day_reservation_cost']
            account_past_3day_cost = data['account_past_3day_cost']
            account_total_reservation_uv = data['account_total_reservation_uv']
            if len(data['report_data']['ds']) > 0:
                account_last_1day_cost += data['report_data']['cost'][-1]
                account_last_1day_reservation_uv += data['report_data']['reservation_uv'][-1]

        if account_last_1day_reservation_uv > 0:
            account_last_1day_reservation_cost = account_last_1day_cost/account_last_1day_reservation_uv
        else:
            account_last_1day_reservation_cost = 0
        
        if account_last_1day_reservation_uv == 0 and account_last_1day_cost > final_cost_goal * 2:
            print ('--- 账号前一天总成本超标,  转化数 %d 消耗 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost))
            res[account_id] = []
            continue
        if account_last_1day_reservation_uv > 0 and account_last_1day_cost/account_last_1day_reservation_uv > final_cost_goal * 2:
            print ('--- 账号前一天总成本超标,  转化数 %d 转化成本 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost/account_last_1day_reservation_uv))
            res[account_id] = []
            continue
        if account_id in old_accounts:
            past_3day_cost_limit = final_cost_goal * 1.3
        elif account_id in median_accounts:
            past_3day_cost_limit = final_cost_goal * 1.4
        else:
            past_3day_cost_limit = final_cost_goal * 1.5
        if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
            mark = 1
            if account_id in new_accounts:
                if random.random() < 0.5: # 新账户一定概率提高限制
                    print ('--- 提高新账号近3天成本限制...')
                    past_3day_cost_limit = final_cost_goal * 2
                    if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
                        mark = 1
                    else:
                        mark = 0
            if mark == 1:
                print ('--- 账号过去3天总成本超标,  总消耗 %.2f 转化成本 %.2f' % (account_past_3day_cost, account_past_3day_reservation_cost))
                # res[account_id] = []
                # continue

        if account_id in old_accounts:
            # up_num = up_all_num_for_old
            if account_last_1day_reservation_uv > 0:
                if account_last_1day_reservation_cost <= final_cost_goal:
                    print ('--- 账户: 前一天转化成本 %.2f <= %.2f, 起量4个' % (account_last_1day_reservation_cost, final_cost_goal))
                    up_num = 4
                elif account_last_1day_reservation_cost <= final_cost_goal * 1.2:
                    print ('--- 账户: 前一天转化成本 %.2f <= %.2f, 起量3个' % (account_last_1day_reservation_cost, final_cost_goal * 1.2))
                    up_num = 3
                elif account_last_1day_reservation_cost <= final_cost_goal * 1.6:
                    print ('--- 账户: 前一天转化成本 %.2f <= %.2f, 起量2个' % (account_last_1day_reservation_cost, final_cost_goal * 1.6))
                    up_num = 2
                else:
                    print ('--- 账户: 前一天转化成本 %.2f > %.2f, 起量1个' % (account_last_1day_reservation_cost, final_cost_goal * 1.6))
                    up_num = 1
                    up_new_adgroup_num = 0
            else:
                if account_last_1day_cost <= final_cost_goal * 1.2:
                    print ('--- 账户: 前一天无转化, 消耗 %.2f <= %.2f, 起量3个' % (account_last_1day_cost, final_cost_goal * 1.2))
                    up_num = 3
                elif account_last_1day_cost <= final_cost_goal * 1.6:
                    print ('--- 账户: 前一天无转化, 消耗 %.2f <= %.2f, 起量2个' % (account_last_1day_cost, final_cost_goal * 1.6))
                    up_num = 2
                else:
                    print ('--- 账户: 前一天无转化, 消耗 %.2f > %.2f, 起量1个' % (account_last_1day_cost, final_cost_goal * 1.6))
                    up_num = 1
                    up_new_adgroup_num = 0
            print ('--- 老账户: 总转化数 %d' % account_total_reservation_uv)
        elif account_id in median_accounts:
            # up_num = up_all_num_for_median
            if account_last_1day_reservation_uv > 0:
                if account_last_1day_reservation_cost <= final_cost_goal * 1.4:
                    print ('--- 账户: 前一天转化成本 %.2f <= %.2f, 起量2个' % (account_last_1day_reservation_cost, final_cost_goal * 1.4))
                    up_num = 2
                else:
                    print ('--- 账户: 前一天转化成本 %.2f > %.2f, 起量1个' % (account_last_1day_reservation_cost, final_cost_goal * 1.4))
                    up_num = 1
                    up_new_adgroup_num = 0
            else:
                if account_last_1day_cost <= final_cost_goal * 1.4:
                    print ('--- 账户: 前一天无转化, 消耗 %.2f <= %.2f, 起量2个' % (account_last_1day_cost, final_cost_goal * 1.4))
                    up_num = 2
                else:
                    print ('--- 账户: 前一天无转化, 消耗 %.2f > %.2f, 起量1个' % (account_last_1day_cost, final_cost_goal * 1.4))
                    up_num = 1
                    up_new_adgroup_num = 0
            print ('--- 中等账户: 总转化数 %d' % account_total_reservation_uv)
        else:
            print ('--- 新账户: 总转化数 %d' % account_total_reservation_uv)
            up_num = up_all_num_for_new

        up_num_till_now = 0
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
                up_num_till_now += 1
        if up_num_till_now > 0:
            if up_num > 2:
                up_num = 2
                print ('--- 已经有提前起量广告, 起量广告数设置减少为2...')

        print ('--- 最大一键起量数:', up_num)
        print ('--- 最大从未起量广告的一键起量数:', up_new_adgroup_num)
        
        select_up_adgroup_ids = []
        final_select_adgroup_ids = []
        never_up_adgroup_ids = []
        active_num = 0
        candidates = {'adgroup_id':[], 'created_time':[],\
                      'past_1day_up':[],'past_1day_cost':[], 'past_1day_reservation_uv':[],'past_1day_reservation_cost':[], \
                        'reservation_uv':[], 'cost':[], 'reservation_cost':[],\
                            'level':[], 'up_budget':[], 'msg':[]}
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
                print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            adgroup_name = data['adgroup_name']
            if adgroup_name[-2:] == 'ZD':
                print ('--- 最大转化成本广告, 不起量...')
                continue
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                continue
            active_num += 1 
            ds_list = data['report_data']['ds']
            if len(ds_list) == 0: # 没有历史数据
                continue
       
            today = str(datetime.datetime.now()).split(' ')[0]
            week_day = get_weekday(today) 
            # if data['total_cost'] < 100 or (data['past_1day_up'] == 1 and data['past_1day_reservation_cost'] > final_cost_goal * 2):
            level = -1
            if data['is_ever_up'] == 1 and \
                    (str(datetime.datetime.now() + datetime.timedelta(days=-7)).split(' ')[0] <= data['last_up_date']) and \
                    data['total_cost'] < final_cost_goal * 0.5:
                mark = 0
                msg = '不进行一键起量: -- 近7天起过量但是低消 近7天总消耗 %.2f < %.2f' % (data['total_cost'], final_cost_goal * 0.5)
                print (adgroup_id, msg)
            elif data['past_1day_up'] == 1 and data['past_1day_reservation_uv'] == 0:
                mark = 0
                msg = '不进行一键起量: 前一天起量, 没有转化'
                print (adgroup_id, msg)
            elif data['is_ever_up'] == 1 and \
                    (str(datetime.datetime.now() + datetime.timedelta(days=-14)).split(' ')[0] <= data['last_up_date']) and \
                    ((data['last_up_reservation_uv'] == 0) or \
                     (data['last_up_reservation_uv'] > 0 and data['last_up_reservation_cost'] > final_cost_goal * 2)):
                mark = 0
                msg = '不进行一键起量： 近14天 最近一次起量转化数 %d, 转化消耗 %.2f, 转化成本 %d >%d' % \
                    (data['last_up_reservation_uv'], data['last_up_cost'], data['last_up_reservation_cost'], final_cost_goal * 2)
                print (adgroup_id, msg)
            # elif data['total_reservation_cost'] > adgroup_reservation_cost_limit:
            #     mark = 0
            #     msg = '不进行一键起量： 总转化成本 %d >%d' % \
            #         (data['total_reservation_cost'], adgroup_reservation_cost_limit)
            #     print (adgroup_id, msg)

            elif data['past_1day_up'] == 0 and data['past_1day_cost'] >= final_cost_goal * 0.5 and \
                    data['past_1day_reservation_uv'] >= 1 and data['past_1day_reservation_cost'] <= adgroup_reservation_cost_limit:
                mark = 1
                level = 1
                if data['past_1day_reservation_uv'] >= 2:
                    up_budget = 500000
                else:
                    up_budget = 300000
                msg = '开启一键起量： 前一天没有起量,  前一天转化数 %d >=1, 前一天转化成本 %.2f <=%d' % \
                        (data['past_1day_reservation_uv'], data['past_1day_reservation_cost'], adgroup_reservation_cost_limit)
                print (adgroup_id, msg)
            elif week_day == '星期一' and data['past_3day_up'] == 1 and data['past_3day_reservation_uv'] > 0 and \
                data['past_3day_reservation_cost'] <= adgroup_reservation_cost_limit:
                mark = 1
                level = 2
                if data['past_3day_reservation_cost'] <= final_cost_goal:
                    up_budget = 400000
                else:
                    up_budget = 300000
                msg = '开启一键起量：上周五起量 转化数 %d >0, 转化成本 %.2f <=%.2f' % (data['past_3day_reservation_uv'], data['past_3day_reservation_cost'], adgroup_reservation_cost_limit)
                print (adgroup_id, msg)
            elif week_day != '星期一' and data['past_1day_up'] == 1 and data['past_1day_reservation_uv'] > 0 and \
                data['past_1day_reservation_cost'] <= adgroup_reservation_cost_limit:
                mark = 1
                level = 2
                if data['past_1day_reservation_cost'] <= final_cost_goal:
                    up_budget = 400000
                else:
                    up_budget = 300000
                msg = '开启一键起量：前一天起量 转化数 %d >0, 转化成本 %.2f <=%.2f' % (data['past_1day_reservation_uv'], data['past_1day_reservation_cost'], adgroup_reservation_cost_limit)
                print (adgroup_id, msg)
            elif ((week_day != '星期一' and data['past_1day_reservation_uv'] > 2) or \
                    (week_day == '星期一' and data['past_3day_reservation_uv'] > 2)) \
                   and data['total_reservation_cost'] <= final_cost_goal * 1.2:
                mark = 1
                level = 3
                up_budget = 300000
                if week_day == '星期一':
                    past_reservation_uv = data['past_3day_reservation_uv']
                else:
                    past_reservation_uv = data['past_1day_reservation_uv']
                msg = '开启一键起量：1: 前一天转化数 %d >2, 总转化成本 %.2f <=%.2f' % (past_reservation_uv, data['total_reservation_cost'], final_cost_goal * 1.2)
                print (adgroup_id, msg)
            elif ((week_day != '星期一' and data['past_1day_reservation_uv'] > 0) or \
                    (week_day == '星期一' and data['past_3day_reservation_uv'] > 0)) \
                        and (data['account_past_7day_reservation_uv'] > 0 and data['account_past_7day_reservation_cost'] <= final_cost_goal):
                mark = 1
                level = 4
                up_budget = 300000
                msg = '开启一键起量：2: 前一天转化数 %d >0, 账户总转化成本 %.2f <=440' % (data['past_1day_reservation_uv'], data['account_past_7day_reservation_cost'])
                print (adgroup_id, msg)
            elif data['total_reservation_uv'] >= 1 and data['total_reservation_cost'] <= adgroup_reservation_cost_limit:
                # if data['is_ever_up'] == 1 and data['past_1day_reservation_cost'] <= final_cost_goal * 2: # adgroup_reservation_cost_limit * 1.2
                mark = 1
                level = 5
                up_budget = 300000
                msg = '开启一键起量： 3: 总转化数 %d >=1, 总转化成本 %.2f <=%d' % \
                        (data['total_reservation_uv'], data['total_reservation_cost'], adgroup_reservation_cost_limit)
                print (adgroup_id, msg)
            else:
                mark = 0
            if mark == 1:
                candidates['adgroup_id'].append(adgroup_id)
                candidates['past_1day_up'].append(data['past_1day_up'])
                candidates['past_1day_cost'].append(data['past_1day_cost'])
                candidates['past_1day_reservation_uv'].append(data['past_1day_reservation_uv'])
                candidates['past_1day_reservation_cost'].append(data['past_1day_reservation_cost'])
                candidates['created_time'].append(data['created_time'])
                candidates['reservation_uv'].append(data['total_reservation_uv'])
                candidates['cost'].append(data['total_cost'])
                candidates['reservation_cost'].append(data['total_reservation_cost'])
                candidates['up_budget'].append(up_budget)
                candidates['level'].append(level)
                candidates['msg'].append(msg)
            else:
                if data['is_ever_up'] == 0:
                    if data['life_long_days'] > 3:
                        msg = '不进行一键起量： 没有起过量, 上线时间 %d 超过3天...' % data['life_long_days']
                        print (adgroup_id, msg)
                    else:
                        never_up_adgroup_ids.append(adgroup_id)
        print ('--- active num: ', active_num)

        candidates = pd.DataFrame(candidates)
        candidates = candidates.sort_values(['level'])
        print ('----- all good ad_groups ...')
        print (candidates)

        # 自己跑量广告
        temp = candidates[(candidates['past_1day_up'] == 0) & (candidates['past_1day_reservation_uv'] > 0) & \
                          (candidates['past_1day_cost'] >= final_cost_goal * 0.5) & \
                          (candidates['past_1day_reservation_cost'] <= adgroup_reservation_cost_limit)] # 前一天没有起量
        if len(temp):
            temp = temp.sort_values(['past_1day_reservation_uv'], ascending=False)
            print ('--- 前一天自己跑量广告')
            print (temp[['adgroup_id', 'past_1day_reservation_uv']])
            for i in range(len(temp)):
                select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i], int(temp['up_budget'].iloc[i])])
                final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                break # 加入最多一个
                # if len(select_up_adgroup_ids) == up_num - up_new_adgroup_num:
                #     break
        
        # 前一天起量广告
        if len(select_up_adgroup_ids) < up_num - up_new_adgroup_num:
            temp = candidates[(candidates['past_1day_up'] == 1) & (candidates['past_1day_reservation_uv'] > 0) & \
                              (candidates['past_1day_reservation_cost'] < adgroup_reservation_cost_limit)] # 前一天没有起量
            if len(temp):
                temp = temp.sort_values(['past_1day_reservation_uv'], ascending=False)
                print ('--- 前一天跑量广告')
                print (temp[['adgroup_id', 'past_1day_reservation_uv']])
                for i in range(len(temp)):
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i], int(temp['up_budget'].iloc[i])])
                    final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                    # break # 加入最多一个
                    if len(select_up_adgroup_ids) >= up_num - up_new_adgroup_num:
                        break

        # 剩余好广告
        if len(select_up_adgroup_ids) < up_num - up_new_adgroup_num:
            temp = candidates[~candidates['adgroup_id'].isin([info[2] for info in select_up_adgroup_ids])] # 前一天没有起量
            temp['reservation_uv_norm'] = temp['reservation_uv']/np.sum(temp['reservation_uv'])
            temp['score'] = temp['reservation_uv_norm'] / temp['reservation_cost'] * -1
            temp = temp.sort_values(['level','score'])
            print ('--- 成本好的广告')
            print (temp)
            for i in range(len(temp)):
                if temp['adgroup_id'].iloc[i] in select_up_adgroup_ids:
                    continue
                if temp['reservation_cost'].iloc[i] > adgroup_reservation_cost_limit:
                    print (temp['adgroup_id'].iloc[i], '超本...')
                    continue
                select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i], int(temp['up_budget'].iloc[i])])
                final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                if len(select_up_adgroup_ids) >= up_num - up_new_adgroup_num:
                    break

        if len(select_up_adgroup_ids) < up_num:
            candidates = {'adgroup_id':[], 'created_time':[],'reservation_uv':[], 'cost':[], 'reservation_cost':[], 'msg':[]}
            print ('--- 补充其他广告')
            select_new_adgroup_id =[]
            for adgroup_id in never_up_adgroup_ids:
                data = adgroup_data[adgroup_id]
                candidates['adgroup_id'].append(adgroup_id)
                candidates['created_time'].append(data['created_time'])
                candidates['reservation_uv'].append(data['total_reservation_uv'])
                candidates['cost'].append(data['total_cost'])
                candidates['reservation_cost'].append(data['total_reservation_cost'])
                candidates['msg'].append('从未起量')
            candidates = pd.DataFrame(candidates)
            # temp = candidates[(candidates['reservation_uv']>0) & (candidates['reservation_uv_cost']<adgroup_reservation_cost_limit)]
            # if len(temp):
            #     temp = temp.sort_values(['reservation_uv'], ascending=False)
            #     print ('--- 有转化广告, 且成本满足')
            #     print (temp)
            #     for i in range(len(temp)):
            #         select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], '开启一键起量：从未起量, 转化数>0', 300000])
            #         print (temp['adgroup_id'].iloc[i], ' 4: 总转化数大于0')
            #         select_new_adgroup_id.append(temp['adgroup_id'].iloc[i])
            #         if len(select_up_adgroup_ids) == up_num or len(select_new_adgroup_id) == up_new_adgroup_num:
            #             break
            if len(select_up_adgroup_ids) < up_num and len(select_new_adgroup_id) < up_new_adgroup_num:
                # candidates['cost'] = candidates['cost'] * -1
                # candidates = candidates.sort_values(['created_time', 'cost'])
                candidates = candidates.sort_values(['created_time'], ascending=False)
                print ('--- 没有转化广告, 按照创建时间排序')
                print (candidates)
                for i in range(len(candidates)):
                    if candidates['adgroup_id'].iloc[i] in final_select_adgroup_ids:
                        print (candidates['adgroup_id'].iloc[i], '已选择')
                        continue
                    if candidates['reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: # 整体成本超标
                        print (candidates['adgroup_id'].iloc[i], candidates['reservation_cost'].iloc[i], '成本超标')
                        continue
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, candidates['adgroup_id'].iloc[i], '开启一键起量：从未起量, 转化数=0', 300000])
                    select_new_adgroup_id.append(candidates['adgroup_id'].iloc[i])
                    print (candidates['adgroup_id'].iloc[i], ' 5: 新广告')
                    if len(select_up_adgroup_ids) >= up_num or len(select_new_adgroup_id) == up_new_adgroup_num:
                        break
        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        
        res[account_id] = select_up_adgroup_ids
        # return select_up_adgroup_ids
    return res

def get_up_adgroup_ids_for_all_3(select_account_id, adgroup_status, scale_ratio = 2): # 新账号, 小量起多个
    
    # 配置
    final_cost_goal = int(year_target * scale_ratio) # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2 # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3
    up_new_adgroup_num = 1 # 最多选择起量的从未起量广告数
    
    print ('---- 保守起量')
    print ('--- 基于历史数据, 寻找可以一键起量的广告 ...')
    print ('--- final_cost_goal: ', final_cost_goal)
    print ('--- adgroup_reservation_cost_limit: ', adgroup_reservation_cost_limit)
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        account_last_1day_cost = 0
        account_last_1day_reservation_uv = 0
        for adgroup_id in adgroup_ids:
            data = adgroup_data[adgroup_id]
            account_past_7day_reservation_cost = data['account_past_7day_reservation_cost']
            account_past_3day_reservation_cost = data['account_past_3day_reservation_cost']
            account_past_3day_cost = data['account_past_3day_cost']
            account_total_reservation_uv = data['account_total_reservation_uv']
            if len(data['report_data']['ds']) > 0:
                account_last_1day_cost += data['report_data']['cost'][-1]
                account_last_1day_reservation_uv += data['report_data']['reservation_uv'][-1]
        
        if account_last_1day_reservation_uv == 0 and account_last_1day_cost > final_cost_goal * 2:
            print ('--- 账号前一天总成本超标,  转化数 %d 消耗 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost))
            res[account_id] = []
            continue
        if account_last_1day_reservation_uv > 0 and account_last_1day_cost/account_last_1day_reservation_uv > final_cost_goal * 2:
            print ('--- 账号前一天总成本超标,  转化数 %d 转化成本 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost/account_last_1day_reservation_uv))
            res[account_id] = []
            continue
        if account_id in old_accounts:
            past_3day_cost_limit = final_cost_goal * 1.3
        elif account_id in median_accounts:
            past_3day_cost_limit = final_cost_goal * 1.4
        else:
            past_3day_cost_limit = final_cost_goal * 1.5
        if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
            mark = 1
            if account_id in new_accounts:
                if random.random() < 0.5: # 新账户一定概率提高限制
                    print ('--- 提高新账号近3天成本限制...')
                    past_3day_cost_limit = final_cost_goal * 2
                    if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
                        mark = 1
                    else:
                        mark = 0
            if mark == 1:
                print ('--- 账号过去3天总成本超标,  总消耗 %.2f 转化成本 %.2f' % (account_past_3day_cost, account_past_3day_reservation_cost))
                # res[account_id] = []
                # continue

        # if account_total_reservation_uv >= 30: # 判断新老账号
        #     up_num = up_all_num_for_old
        #     print ('--- 老账户: 总转化数 %d' % account_total_reservation_uv)
        #     if account_past_7day_reservation_cost > 600: # 老账号成本太高, 不起量
        #         print ('--- 账户成本超过 %.2f 600...不起量' % account_past_7day_reservation_cost)
        #         res[account_id] = []
        #         continue
        # else:
        #     print ('--- 新账户: 总转化数 %d' % )account_total_reservation_uv
        #     up_num = up_all_num_for_new
        up_num = 2 # 所有账号只起量一个广告
        up_new_adgroup_num_for_new_account = 2

        print ('--- 最大一键起量数:', up_num)
        print ('--- 最大从未起量广告的一键起量数:', up_new_adgroup_num_for_new_account)
        
        select_up_adgroup_ids = []
        final_select_adgroup_ids = []
        not_select_up_adgroup_ids = []
        active_num = 0
        candidates = {'adgroup_id':[], 'created_time':[],'past_1day_reservation_uv':[], 'past_1day_cost':[], 'past_1day_reservation_cost':[],'msg':[]}
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
                print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            adgroup_name = data['adgroup_name']
            if adgroup_name[-2:] == 'ZD':
                print ('--- 最大转化成本广告, 不起量...')
                continue
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                continue
            active_num += 1 
            ds_list = data['report_data']['ds']
            if len(ds_list) == 0: # 没有历史数据
                continue
        
            if data['is_ever_up'] == 1 and \
                    ((data['last_up_reservation_uv'] == 0) or \
                     (data['last_up_reservation_uv'] > 0 and data['last_up_reservation_cost'] > final_cost_goal * 2)):
                mark = -1
                msg = '不进行一键起量： 最近一次起量转化数 %d, 转化消耗 %.2f, 转化成本 %d >%d' % \
                    (data['last_up_reservation_uv'], data['last_up_cost'], data['last_up_reservation_cost'], final_cost_goal * 2)
                print (adgroup_id, msg)
            elif data['past_1day_up'] == 0 and data['past_1day_cost'] >= final_cost_goal * 0.5 and \
                data['past_1day_reservation_uv'] >= 1 and data['past_1day_reservation_cost'] <= adgroup_reservation_cost_limit:
                # if data['is_ever_up'] == 1 and data['past_1day_reservation_cost'] <= final_cost_goal * 2: # adgroup_reservation_cost_limit * 1.2
                mark = 1
                msg = '开启一键起量： 前一天没有起量,  前一天转化数 %d >=1, 前一天转化成本 %.2f <=%d' % \
                        (data['past_1day_reservation_uv'], data['past_1day_reservation_cost'], adgroup_reservation_cost_limit)
                print (adgroup_id, msg)
            else:
                mark = 0 # 待选

            if mark == 1:
                candidates['adgroup_id'].append(adgroup_id)
                candidates['created_time'].append(data['created_time'])
                candidates['past_1day_reservation_uv'].append(data['past_1day_reservation_uv'])
                candidates['past_1day_cost'].append(data['past_1day_cost'])
                candidates['past_1day_reservation_cost'].append(data['past_1day_reservation_cost'])
                candidates['msg'].append(msg)
            elif mark == 0:
                not_select_up_adgroup_ids.append(adgroup_id)

        print ('--- active num: ', active_num)

        candidates = pd.DataFrame(candidates)
        candidates = candidates.sort_values('past_1day_reservation_uv', ascending=False)
        print (candidates)
        for i in range(len(candidates)):
            if candidates['past_1day_reservation_uv'].iloc[i] >=2:
                up_budget = 150000
            else:
                up_budget = 150000
            select_up_adgroup_ids.append(['startAutoAcquisition',account_id, candidates['adgroup_id'].iloc[i], candidates['msg'].iloc[i], up_budget])
            if len(select_up_adgroup_ids) == up_num:
                break

        if len(select_up_adgroup_ids) < up_num:
            candidates = {'adgroup_id':[], 'created_time':[], 'life_long_days':[], 'total_reservation_cost':[], \
                          'past_1day_up':[], 'past_1day_reservation_uv':[],'past_1day_reservation_cost':[], \
                          'past_3day_up':[], 'past_3day_reservation_uv':[],'past_3day_reservation_cost':[], \
                          'msg':[]}
            print ('--- 补充从来没有起量广告')
            for adgroup_id in not_select_up_adgroup_ids:
                data = adgroup_data[adgroup_id]
                candidates['adgroup_id'].append(adgroup_id)
                candidates['created_time'].append(data['created_time'])
                candidates['life_long_days'].append(data['life_long_days'])
                candidates['total_reservation_cost'].append(data['total_reservation_cost'])
                candidates['past_1day_up'].append(data['past_1day_up'])
                candidates['past_3day_up'].append(data['past_3day_up'])
                candidates['past_1day_reservation_uv'].append(data['past_1day_reservation_uv'])
                candidates['past_3day_reservation_uv'].append(data['past_3day_reservation_uv'])
                candidates['past_1day_reservation_cost'].append(data['past_1day_reservation_cost'])
                candidates['past_3day_reservation_cost'].append(data['past_3day_reservation_cost'])
                candidates['msg'].append('从未起量')
            candidates = pd.DataFrame(candidates)
            today = str(datetime.datetime.now()).split(' ')[0]
            week_day = get_weekday(today)
            if week_day == '星期一': # 查看星期五起量效果
               print ('--- 星期一, 查看星期五起量效果')
               temp = candidates[(candidates['past_3day_up']==1) & (candidates['past_3day_reservation_uv'] > 0) \
                              & (candidates['past_3day_reservation_cost'] < adgroup_reservation_cost_limit)]
            else:
               temp = candidates[(candidates['past_1day_up']==1) & (candidates['past_1day_reservation_uv'] > 0) \
                              & (candidates['past_1day_reservation_cost'] < adgroup_reservation_cost_limit)]
            if len(temp):
                if week_day == '星期一': # 查看星期五起量效果
                    temp = temp.sort_values(['past_3day_reservation_uv'], ascending=False)
                else:
                    temp = temp.sort_values(['past_1day_reservation_uv'], ascending=False)
                print ('--- 前一天起量成本好...')
                print (temp)
                for i in range(len(temp)):
                    if week_day == '星期一': # 查看星期五起量效果
                        msg = '开启一键起量： 前星期五起量,  前星期五转化数 %d >前星期五转化成本 %.2f <=%d' % \
                            (temp['past_3day_reservation_uv'].iloc[i], temp['past_3day_reservation_cost'].iloc[i], adgroup_reservation_cost_limit)
                        if temp['past_3day_reservation_cost'].iloc[i]/temp['past_3day_reservation_uv'].iloc[i] <= final_cost_goal:
                            up_budget = 150000
                        else:
                            up_budget = 150000
                    else:
                        msg = '开启一键起量： 前一天起量,  前一天转化数 %d >=0, 前一天转化成本 %.2f <=%d' % \
                            (temp['past_1day_reservation_uv'].iloc[i], temp['past_1day_reservation_cost'].iloc[i], adgroup_reservation_cost_limit)
                        if temp['past_1day_reservation_cost'].iloc[i]/temp['past_1day_reservation_uv'].iloc[i] <= final_cost_goal:
                            up_budget = 150000
                        else:
                            up_budget = 150000
                    
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], msg, up_budget])
                    final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                    print (temp['adgroup_id'].iloc[i], msg)
                    if len(select_up_adgroup_ids) == up_num:
                        break
            if len(select_up_adgroup_ids) < up_num:
                # candidates['cost'] = candidates['cost'] * -1
                # candidates = candidates.sort_values(['created_time', 'cost'])
                candidates = candidates.sort_values(['created_time'], ascending=False)
                print ('--- 按照创建时间排序')
                print (candidates)
                select_new_adgroup_id =[]
                for i in range(len(candidates)):
                    if candidates['adgroup_id'].iloc[i] in final_select_adgroup_ids:
                        print (candidates['adgroup_id'].iloc[i], '已选择')
                        continue
                    if candidates['life_long_days'].iloc[i] > 3:
                        print (candidates['adgroup_id'].iloc[i], '上线超过3天')
                        continue
                    if candidates['total_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: # 整体成本超标
                        print (candidates['adgroup_id'].iloc[i], '超本')
                        continue
                    if candidates['past_1day_up'].iloc[i] == 1 and candidates['past_1day_reservation_uv'].iloc[i] == 0: #前一天起量没有转化
                        print (candidates['adgroup_id'].iloc[i], '前一天起量没有转化')
                        continue
                    if candidates['past_1day_up'].iloc[i] == 1 and candidates['past_1day_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: #前一天起量超本
                        print (candidates['adgroup_id'].iloc[i], '前一天起量超本')
                        continue
                    if week_day == '星期一':
                        if candidates['past_3day_up'].iloc[i] == 1 and candidates['past_3day_reservation_uv'].iloc[i] == 0: #前一天起量没有转化
                            print (candidates['adgroup_id'].iloc[i], '前星期五起量没有转化')
                            continue
                        if candidates['past_3day_up'].iloc[i] == 1 and candidates['past_3day_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: #前一天起量没有转化
                            print (candidates['adgroup_id'].iloc[i], '前星期五起量超本')
                            continue
                    select_up_adgroup_ids.append(['startAutoAcquisition',account_id, candidates['adgroup_id'].iloc[i], \
                                                  '开启一键起量：最新广告, 创建时间%s' % candidates['created_time'].iloc[i], 150000])
                    select_new_adgroup_id.append(candidates['adgroup_id'].iloc[i])
                    print (candidates['adgroup_id'].iloc[i], '开启一键起量：最新广告, 创建时间%s' % candidates['created_time'].iloc[i])
                    if len(select_up_adgroup_ids) == up_num or len(select_new_adgroup_id) == up_new_adgroup_num_for_new_account:
                        break
        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        
        res[account_id] = select_up_adgroup_ids
        # return select_up_adgroup_ids
    return res

def get_up_adgroup_ids_for_all_early(select_account_id, adgroup_status, scale_ratio = 2): # 提早起量
    
    # 配置
    final_cost_goal = int(year_target * scale_ratio) # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2 # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3
    up_new_adgroup_num = 1 # 最多选择起量的从未起量广告数
    
    print ('---- 提早起量')
    print ('--- 基于早上跑量数据, 决定是否起量 ...')
    print ('--- final_cost_goal: ', final_cost_goal)
    print ('--- adgroup_reservation_cost_limit: ', adgroup_reservation_cost_limit)
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        account_last_1day_cost = 0
        account_last_1day_reservation_uv = 0
        for adgroup_id in adgroup_ids:
            data = adgroup_data[adgroup_id]
            account_past_7day_reservation_cost = data['account_past_7day_reservation_cost']
            account_past_3day_reservation_cost = data['account_past_3day_reservation_cost']
            account_past_3day_cost = data['account_past_3day_cost']
            account_total_reservation_uv = data['account_total_reservation_uv']
            if len(data['report_data']['ds']) > 0:
                account_last_1day_cost += data['report_data']['cost'][-1]
                account_last_1day_reservation_uv += data['report_data']['reservation_uv'][-1]
        
        if account_last_1day_reservation_uv == 0 and account_last_1day_cost > final_cost_goal * 2:
            print ('--- 账号前一天总成本超标,  转化数 %d 消耗 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost))
            res[account_id] = []
            continue
        if account_last_1day_reservation_uv > 0 and account_last_1day_cost/account_last_1day_reservation_uv > final_cost_goal * 2:
            print ('--- 账号前一天总成本超标,  转化数 %d 转化成本 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost/account_last_1day_reservation_uv))
            res[account_id] = []
            continue
        if account_id in old_accounts:
            past_3day_cost_limit = final_cost_goal * 1.3
        elif account_id in median_accounts:
            past_3day_cost_limit = final_cost_goal * 1.4
        else:
            past_3day_cost_limit = final_cost_goal * 1.5
        if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
            mark = 1
            if account_id in new_accounts:
                if random.random() < 0.5: # 新账户一定概率提高限制
                    print ('--- 提高新账号近3天成本限制...')
                    past_3day_cost_limit = final_cost_goal * 2
                    if account_past_3day_reservation_cost > past_3day_cost_limit or (account_past_3day_reservation_cost == 0 and account_past_3day_cost > past_3day_cost_limit):
                        mark = 1
                    else:
                        mark = 0
            if mark == 1:
                print ('--- 账号过去3天总成本超标,  总消耗 %.2f 转化成本 %.2f' % (account_past_3day_cost, account_past_3day_reservation_cost))
                # res[account_id] = []
                # continue

        up_num = up_all_num_for_new # 所有账号只起量一个广告

        print ('--- 最大一键起量数:', up_num)
        print ('--- 最大从未起量广告的一键起量数:', up_new_adgroup_num)
        
        select_up_adgroup_ids = []
        not_select_up_adgroup_ids = []
        active_num = 0
        candidates = {'adgroup_id':[], 'today_cost':[],'today_reservation_uv':[],\
                      'total_reservation_cost':[], 'past_1day_up':[], 'past_1day_reservation_uv':[], 'past_1day_reservation_cost':[], 'msg':[]}
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
                print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                continue
            adgroup_name = data['adgroup_name']
            if adgroup_name[-2:] == 'ZD':
                print ('--- 最大转化成本广告, 不起量...')
                continue
            active_num += 1 
            ds_list = data['report_data']['ds']
            if len(ds_list) == 0: # 没有历史数据
                continue
        
            if data['is_ever_up'] == 1 and \
                    ((data['last_up_reservation_uv'] == 0) or \
                     (data['last_up_reservation_uv'] > 0 and data['last_up_reservation_cost'] > final_cost_goal * 2)):
                mark = -1
                msg = '不进行一键起量： 最近一次起量转化数 %d, 转化消耗 %.2f, 转化成本 %d >%d' % \
                    (data['last_up_reservation_uv'], data['last_up_cost'], data['last_up_reservation_cost'], final_cost_goal * 2)
                print (adgroup_id, msg)
            elif data['today_cost'] > 100:
                mark = 1
                msg = '待选'
            else:
                mark = -1

            if mark == 1:
                candidates['adgroup_id'].append(adgroup_id)
                candidates['today_cost'].append(data['today_cost'])
                candidates['today_reservation_uv'].append(data['today_reservation_uv'])
                candidates['total_reservation_cost'].append(data['total_reservation_cost'])
                candidates['past_1day_up'].append(data['past_1day_up'])
                candidates['past_1day_reservation_uv'].append(data['past_1day_reservation_uv'])
                candidates['past_1day_reservation_cost'].append(data['past_1day_reservation_cost'])
                candidates['msg'].append(msg)
            elif mark == 0:
                not_select_up_adgroup_ids.append(adgroup_id)

        print ('--- active num: ', active_num)

        candidates = pd.DataFrame(candidates)
        candidates = candidates.sort_values(['today_cost', 'today_reservation_uv'], ascending=False)
        print (candidates)
        for i in range(len(candidates)):
            up_budget = 300000
            if candidates['adgroup_id'].iloc[i] in select_up_adgroup_ids:
                print (candidates['adgroup_id'].iloc[i], '已选择')
                continue
            if candidates['total_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: # 整体成本超标
                print (candidates['adgroup_id'].iloc[i], '超本')
                continue
            if candidates['past_1day_up'].iloc[i] == 1 and candidates['past_1day_reservation_uv'].iloc[i] == 0: #前一天起量没有转化
                print (candidates['adgroup_id'].iloc[i], '前一天起量没有转化')
                continue
            if candidates['past_1day_up'].iloc[i] == 1 and candidates['past_1day_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: #前一天起量超本
                print (candidates['adgroup_id'].iloc[i], '前一天起量超本')
                continue
            #if week_day == '星期一':
            #    if candidates['past_3day_up'].iloc[i] == 1 and candidates['past_3day_reservation_uv'].iloc[i] == 0: #前一天起量没有转化
            #        print (candidates['adgroup_id'].iloc[i], '前星期五起量没有转化')
            #        continue
            #    if candidates['past_3day_up'].iloc[i] == 1 and candidates['past_3day_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: #前一天起量没有转化
            #        print (candidates['adgroup_id'].iloc[i], '前星期五起量超本')
            #        continue
            msg = '开启一键起量：当天消耗%.2f' % candidates['today_cost'].iloc[i]
            select_up_adgroup_ids.append(['startAutoAcquisition',account_id, candidates['adgroup_id'].iloc[i], msg, 300000])
            print (candidates['adgroup_id'].iloc[i], msg)
            if len(select_up_adgroup_ids) == up_num:
                break
        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        
        res[account_id] = select_up_adgroup_ids
        # return select_up_adgroup_ids
    return res

def get_up_adgroup_ids_for_all_by_model(select_account_id, adgroup_status, account_status, scale_ratio = 2): # 模型预测
    
    global predict_data, predict_model

    # 配置
    follow_final_cost_goal = int(account_status['past2WeeksFollowCost'] * scale_ratio)
    register_final_cost_goal = int(account_status['past2WeeksRegCost'] * scale_ratio)
    reservation_final_cost_goal = int(account_status['past2WeeksReservationCost'] * scale_ratio)

    
    adgroup_follow_cost_limit = follow_final_cost_goal * 2
    adgroup_register_cost_limit = register_final_cost_goal * 2
    adgroup_reservation_cost_limit = reservation_final_cost_goal * 1

    print ('--- 目标关注成本： ', follow_final_cost_goal)
    print ('--- 目标注册成本： ', register_final_cost_goal)
    print ('--- 目标表单预约成本： ', reservation_final_cost_goal)

    print ('--- 关注成本限制： ', adgroup_follow_cost_limit)
    print ('--- 目标注册成本限制： ', adgroup_register_cost_limit)
    print ('--- 目标表单预约成本限制： ', adgroup_reservation_cost_limit)


    
    up_new_adgroup_num = 1 # 最多选择起量的从未起量广告数
    
    print ('---- 模型起量')
    print ('--- 基于历史数据, 寻找可以一键起量的广告 ...')
    print ('--- adgroup_reservation_cost_limit: ', adgroup_reservation_cost_limit)
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    # model_predict = json.load(open('test_pred_new.json', encoding='utf8'))
    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    hour = int(str(now).split(' ')[1].split(':')[0])
    week_day = get_weekday(day)
    print ('--- week_day: ', week_day)
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        up_num = 2 #up_all_num_for_new # 所有账号只起量一个广告

        print ('--- 最大一键起量数:', up_num)
        print ('--- 最大从未起量广告的一键起量数:', up_new_adgroup_num)
            
        select_up_adgroup_ids = []
        final_select_adgroup_ids = []
        not_select_up_adgroup_ids = []
        active_num = 0
        candidates = {'adgroup_id':[], 'created_time':[],'model_predict_score':[],'msg':[]}
        # best_threshold = open('best_threshold_new.txt', encoding='utf8').readlines()
        # best_threshold = float(best_threshold[0].strip())
        # print ('--- best_threshold: ', best_threshold)
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            # if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
            #     print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
            #     continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                continue
            adgroup_name = data['adgroup_name']
            if adgroup_name[-2:] == 'ZD':
                print ('--- %s 最大转化成本广告, 不起量...' % str(adgroup_id), adgroup_name)
                continue
            active_num += 1 
            # ds_list = data['report_data']['ds']
            # if len(ds_list) == 0: # 没有历史数据
            #     continue

            adgroup_predict_data = predict_data[(predict_data['account_id'] == int(account_id))&\
                                                    (predict_data['adgroup_id'] == int(adgroup_id))&\
                                                        (predict_data['ds'] == day)&\
                                                            (predict_data['hour'] == hour)]
        
            if len(adgroup_predict_data) == 0:
                print ('--- %s 没有预测数据...' % str(adgroup_id))
                continue
            
            
            adgroup_predict_data['prev_hours_cur_day_cost'].iloc[0] = data['today_cost']
            adgroup_predict_data['prev_hours_cur_day_biz_follow_uv'].iloc[0] = data['today_biz_follow_uv']
            adgroup_predict_data['prev_hours_cur_day_biz_follow_cost'].iloc[0] = data['today_biz_follow_cost']
            adgroup_predict_data['prev_hours_cur_day_biz_reg_uv'].iloc[0] = data['today_biz_reg_uv']
            adgroup_predict_data['prev_hours_cur_day_biz_reg_cost'].iloc[0] = data['today_biz_reg_cost']
            adgroup_predict_data['prev_hours_cur_day_reservation_uv'].iloc[0] = data['today_reservation_uv']
            adgroup_predict_data['prev_hours_cur_day_reservation_cost'].iloc[0] = data['today_reservation_cost']
            print (adgroup_predict_data[['account_id','adgroup_id','ds','hour', \
                                         'prev_hours_cur_day_cost', 'prev_hours_cur_day_biz_follow_uv', 'prev_hours_cur_day_biz_reg_uv', \
                                         'prev_hours_cur_day_reservation_uv', ]])
            adgroup_predict_data = adgroup_predict_data.drop(['account_id','adgroup_id','ds'], axis = 1)
            
            if len(adgroup_predict_data) == 0:
                print ('--- no adgroup predict data ...')
                continue
            # print (adgroup_predict_data)
            model_predict_score = predict_model.predict_proba(adgroup_predict_data)[:,1]
            print ('--- ', account_id, adgroup_id, model_predict_score)
        
        #     model_predict_score = 0
        #     mark = 0
        #     if check_not_up_ads(data, final_cost_goal, adgroup_reservation_cost_limit, week_day) == 0: #data['is_ever_up'] == 1 and \
        #             # ((data['last_up_reservation_uv'] == 0) or \
        #             #  (data['last_up_reservation_uv'] > 0 and data['last_up_reservation_cost'] > final_cost_goal * 2)):
        #         mark = -1
        #         msg = '不进行一键起量：不满足起量约束'
        #         print (adgroup_id, msg)
        #     elif adgroup_id in account_model_predict:
        #         model_predict_score = float(account_model_predict[adgroup_id])
        #         if model_predict_score >= best_threshold:
        #             mark = 1
        #             msg = '一键起量： 模型预估打分%.4f >= %.2f ...' % (model_predict_score, best_threshold)
        #             print (adgroup_id, msg)
        #         else:
        #             if data['is_ever_up'] == 0:
        #                 mark = 0
        #                 msg = '备选：模型预估打分%.4f < %.2f, 但是没有起过量 ...' % (model_predict_score, best_threshold)
        #                 print (adgroup_id, msg)
        #             else:
        #                 mark = -1 # 模型打分低, 不起量
        #                 msg = '不进行一键起量： 模型预估打分%.4f < %.2f ...' % (model_predict_score, best_threshold)
        #                 print (adgroup_id, msg)
        #     else:
        #         mark = 0 # 待选
        #         msg = '备选'
        #         print (adgroup_id, msg)

        #     if mark == 1:
        #         candidates['adgroup_id'].append(adgroup_id)
        #         candidates['created_time'].append(data['created_time'])
        #         candidates['model_predict_score'].append(model_predict_score)
        #         candidates['msg'].append(msg)
        #     elif mark == 0 and data['is_ever_up'] == 0: # 没有起过量的广告
        #         not_select_up_adgroup_ids.append(adgroup_id)

        # print ('--- active num: ', active_num)

        # candidates = pd.DataFrame(candidates)
        # if len(candidates) > 0:
        #     candidates = candidates.sort_values('model_predict_score', ascending=False)
        #     print (candidates)
        #     for i in range(len(candidates)):
        #         up_budget = 300000
        #         select_up_adgroup_ids.append(['startAutoAcquisition',account_id, candidates['adgroup_id'].iloc[i], candidates['msg'].iloc[i], up_budget])
        #         final_select_adgroup_ids.append(candidates['adgroup_id'].iloc[i])
        #         if len(select_up_adgroup_ids) == up_num:
        #             break
        # else:
        #     print ('--- 没有优质广告...')

        # if len(select_up_adgroup_ids) < up_num:
        #     candidates = {'adgroup_id':[], 'created_time':[], 'life_long_days':[], 'total_reservation_cost':[], \
        #                   'past_1day_up':[], 'past_1day_reservation_uv':[],'past_1day_reservation_cost':[], \
        #                   'past_3day_up':[], 'past_3day_reservation_uv':[],'past_3day_reservation_cost':[], \
        #                   'msg':[]}
        #     print ('--- 补充从来没有起量广告')
        #     for adgroup_id in not_select_up_adgroup_ids:
        #         data = adgroup_data[adgroup_id]
        #         candidates['adgroup_id'].append(adgroup_id)
        #         candidates['created_time'].append(data['created_time'])
        #         candidates['life_long_days'].append(data['life_long_days'])
        #         candidates['total_reservation_cost'].append(data['total_reservation_cost'])
        #         candidates['past_1day_up'].append(data['past_1day_up'])
        #         candidates['past_3day_up'].append(data['past_3day_up'])
        #         candidates['past_1day_reservation_uv'].append(data['past_1day_reservation_uv'])
        #         candidates['past_3day_reservation_uv'].append(data['past_3day_reservation_uv'])
        #         candidates['past_1day_reservation_cost'].append(data['past_1day_reservation_cost'])
        #         candidates['past_3day_reservation_cost'].append(data['past_3day_reservation_cost'])
        #         candidates['msg'].append('从未起量')
        #     candidates = pd.DataFrame(candidates)
        #     if len(select_up_adgroup_ids) < up_num:
        #         select_new_adgroup_id =[]
        #         candidates = candidates.sort_values(['created_time'], ascending=False)
        #         print ('--- 按照创建时间降序...')
        #         print (candidates)
        #         for i in range(len(candidates)):
        #             if candidates['adgroup_id'].iloc[i] in final_select_adgroup_ids:
        #                 print (candidates['adgroup_id'].iloc[i], '已选择')
        #                 continue
        #             if candidates['life_long_days'].iloc[i] > 3:
        #                 print (candidates['adgroup_id'].iloc[i], '上线超过3天')
        #                 continue
        #             if candidates['total_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: # 整体成本超标
        #                 print (candidates['adgroup_id'].iloc[i], '超本')
        #                 continue
        #             if candidates['past_1day_up'].iloc[i] == 1 and candidates['past_1day_reservation_uv'].iloc[i] == 0: #前一天起量没有转化
        #                 print (candidates['adgroup_id'].iloc[i], '前一天起量没有转化')
        #                 continue
        #             if candidates['past_1day_up'].iloc[i] == 1 and candidates['past_1day_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: #前一天起量超本
        #                 print (candidates['adgroup_id'].iloc[i], '前一天起量超本')
        #                 continue
        #             # if week_day == '星期一':
        #             #     if candidates['past_3day_up'].iloc[i] == 1 and candidates['past_3day_reservation_uv'].iloc[i] == 0: #前一天起量没有转化
        #             #         print (candidates['adgroup_id'].iloc[i], '前星期五起量没有转化')
        #             #         continue
        #             #     if candidates['past_3day_up'].iloc[i] == 1 and candidates['past_3day_reservation_cost'].iloc[i] > adgroup_reservation_cost_limit: #前一天起量没有转化
        #             #         print (candidates['adgroup_id'].iloc[i], '前星期五起量超本')
        #             #         continue
        #             select_up_adgroup_ids.append(['startAutoAcquisition',account_id, candidates['adgroup_id'].iloc[i], \
        #                                           '开启一键起量：最新广告, 创建时间%s' % candidates['created_time'].iloc[i], 300000])
        #             select_new_adgroup_id.append(candidates['adgroup_id'].iloc[i])
        #             print (candidates['adgroup_id'].iloc[i], '开启一键起量：最新广告, 创建时间%s' % candidates['created_time'].iloc[i])
        #             if len(select_new_adgroup_id) >= up_new_adgroup_num:
        #                 break
        # print ('--- select adgroup_ids: ')
        # for d in select_up_adgroup_ids:
        #     print (d)
        
        res[account_id] = select_up_adgroup_ids
        # return select_up_adgroup_ids
    return res

def check_if_reup_again(select_account_id, adgroup_status, account_status, scale_ratio = 2): # 连续起量

    # 配置
    follow_final_cost_goal = int(account_status['past2WeeksFollowCost'] * scale_ratio)
    register_final_cost_goal = int(account_status['past2WeeksRegCost'] * scale_ratio)
    reservation_final_cost_goal = int(account_status['past2WeeksReservationCost'] * scale_ratio)

    
    adgroup_follow_cost_limit = follow_final_cost_goal * 2
    adgroup_register_cost_limit = register_final_cost_goal * 2
    adgroup_reservation_cost_limit = reservation_final_cost_goal * 1

    print ('--- 目标关注成本： ', follow_final_cost_goal)
    print ('--- 目标注册成本： ', register_final_cost_goal)
    print ('--- 目标表单预约成本： ', reservation_final_cost_goal)

    print ('--- 关注成本限制： ', adgroup_follow_cost_limit)
    print ('--- 目标注册成本限制： ', adgroup_register_cost_limit)
    print ('--- 目标表单预约成本限制： ', adgroup_reservation_cost_limit)
    
    print ('--- 基于当天第一次起量数据, 决定是否第二次一键起量 ...')
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    res = {}
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == False:
                # print ('--- %s 当天没有起量过, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']: # 不在投放中
                # print ('--- %s 不在投放中, 不操作 ...' % str(adgroup_id))
                continue
            autoAcquisitionStatus = adgroup_status[adgroup_id]['autoAcquisitionStatus'] # 起量状态
            if autoAcquisitionStatus == 'AUTO_ACQUISTION_STATUS_PENDING': # 在起量中
                # print ('--- %s 在起量中, 不操作 ...' % str(adgroup_id))
                continue
            print ('--- %s %s' % (adgroup_id, autoAcquisitionStatus))
            if data['today_reservation_uv'] > 2:
                msg = '--- 第二次起量：当天表单数 %d > 2' % (data['today_reservation_uv'])
                print (msg)
                select_up_adgroup_ids.append(['startAutoAcquisition',account_id, adgroup_id, msg, 5000000])

        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res

def check_if_up_hourly(select_account_id, adgroup_status, scale_ratio = 2):
    
    # 配置
    final_cost_goal = int(year_target * scale_ratio) # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2 # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3
    
    print ('--- 基于当天自己跑量数据, 决定是否一键起量 ...')
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    res = {}
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]

        adgroup_ids = adgroup_data.keys()

        account_last_1day_cost = 0
        account_last_1day_reservation_uv = 0
        for adgroup_id in adgroup_ids:
            data = adgroup_data[adgroup_id]
            if len(data['report_data']['ds']) > 0:
                account_last_1day_cost += data['report_data']['cost'][-1]
                account_last_1day_reservation_uv += data['report_data']['reservation_uv'][-1]
        
        if account_last_1day_reservation_uv == 0 and account_last_1day_cost > final_cost_goal * 2:
            print ('--- 账号前一天总成本超标,  转化数 %d 消耗 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost))
            res[account_id] = []
            continue
        if account_last_1day_reservation_uv > 0 and account_last_1day_cost/account_last_1day_reservation_uv > final_cost_goal * 2:
            print ('--- 账号前一天总成本超标,  转化数 %d 转化成本 %.2f' % (account_last_1day_reservation_uv, account_last_1day_cost/account_last_1day_reservation_uv))
            res[account_id] = []
            continue

        select_up_adgroup_ids = []
        candidates = {'adgroup_id':[], 'today_cost':[], 'today_reservation_uv':[],'msg':[]}
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
                print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                continue
            ds_list = data['report_data']['ds']
            if len(ds_list) == 0: # 没有历史数据
                continue
            
            if data['up_trend'] == 1:
                msg = '--- 消耗有上升趋势, 一键起量 ...'
                print (adgroup_id, msg)
                candidates['adgroup_id'].append(adgroup_id)
                candidates['today_cost'].append(data['today_cost'])
                candidates['today_reservation_uv'].append(data['today_reservation_uv'])
                candidates['msg'].append(data['msg'])
        
        if len(candidates['adgroup_id']) > 0:
            candidates = pd.DataFrame(candidates)
            candidates = candidates.sort_values(['today_cost', 'today_reservation_uv'], ascending=False)
            select_up_adgroup_ids.append(['startAutoAcquisition', account_id, candidates['adgroup_id'].iloc[0], candidates['msg'].iloc[0], 1500000])
        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res

def check_if_end_up_per_10mins(select_account_id, adgroup_status, account_status, scale_ratio = 2):
    
    # 配置
    follow_final_cost_goal = int(account_status['past2WeeksFollowCost'] * scale_ratio)
    register_final_cost_goal = int(account_status['past2WeeksRegCost'] * scale_ratio)
    reservation_final_cost_goal = int(account_status['past2WeeksReservationCost'] * scale_ratio)

    
    adgroup_follow_cost_limit = follow_final_cost_goal * 2
    adgroup_register_cost_limit = register_final_cost_goal * 2
    adgroup_reservation_cost_limit = reservation_final_cost_goal * 1

    print ('--- 目标关注成本： ', follow_final_cost_goal)
    print ('--- 目标注册成本： ', register_final_cost_goal)
    print ('--- 目标表单预约成本： ', reservation_final_cost_goal)

    print ('--- 关注成本限制： ', adgroup_follow_cost_limit)
    print ('--- 目标注册成本限制： ', adgroup_register_cost_limit)
    print ('--- 目标表单预约成本限制： ', adgroup_reservation_cost_limit)
    

    print ('--- 基于当天一键起量跑量数据, 决定是否暂停一键起量 ...')
    print ('--- 成本限制: ', adgroup_reservation_cost_limit)
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['autoAcquisitionStatus'] != 'AUTO_ACQUISTION_STATUS_PENDING':
                # print ('--- %s 不在起量中, 不操作 ...' % str(adgroup_id))
                continue
            
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                # print ('--- %s 不投放中, 不操作 ...' % str(adgroup_id))
                continue

            data = adgroup_data[adgroup_id]
            # 第一天不操作
            # ds_list = data['report_data']['ds']
            # if len(ds_list) == 0: # 没有历史数据
            #     print ('--- %s 没有历史数据, 不操作 ...' % str(adgroup_id))
            #     continue
            end_up = 0
            # if str(adgroup_id) == str(33259702039): # 指定某一个广告暂停起量
            #     end_up = 1
            # print ('--- ', adgroup_id, data['today_reservation_uv'], data['today_cost'], data['today_reservation_cost'])
            # msg = '全部暂停一键起量'
            # print (msg)
            if data['today_reservation_uv'] == 0 and data['today_cost'] > adgroup_reservation_cost_limit: 
                end_up = 1
                msg = '暂停一键起量： 当天转化数为0, 当天消耗 %.2f >%d' % (data['today_cost'], adgroup_reservation_cost_limit)
                print (adgroup_id, msg)
            elif data['today_reservation_uv'] > 0 and data['today_reservation_cost'] > adgroup_reservation_cost_limit * 1.5:
                end_up = 1
                msg = '暂停一键起量： 当天转化数 %d > 0, 当天转化成本 %.2f >%d' % (data['today_reservation_uv'], data['today_reservation_cost'], adgroup_reservation_cost_limit)
                print (adgroup_id, msg)
            if end_up == 1:
                select_up_adgroup_ids.append(['stopAutoAcquisition', account_id, adgroup_id, msg])
        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res

def check_if_over_budget_old(select_account_id, adgroup_status, account_status, scale_ratio = 1):
    
    # 配置
    follow_final_cost_goal = int(account_status['past2WeeksFollowCost'] * scale_ratio)
    register_final_cost_goal = int(account_status['past2WeeksRegCost'] * scale_ratio)
    reservation_final_cost_goal = int(account_status['past2WeeksReservationCost'] * scale_ratio)

    
    adgroup_follow_cost_limit = follow_final_cost_goal * 2
    adgroup_register_cost_limit = register_final_cost_goal * 2
    adgroup_reservation_cost_limit = reservation_final_cost_goal * 1

    print ('--- 目标关注成本： ', follow_final_cost_goal)
    print ('--- 目标注册成本： ', register_final_cost_goal)
    print ('--- 目标表单预约成本： ', reservation_final_cost_goal)

    print ('--- 关注成本限制： ', adgroup_follow_cost_limit)
    print ('--- 目标注册成本限制： ', adgroup_register_cost_limit)
    print ('--- 目标表单预约成本限制： ', adgroup_reservation_cost_limit)

    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    hour = int(str(now).split(' ')[1].split(':')[0])
    miniute = int(str(now).split(' ')[1].split(':')[1])
    
    print ('--- 保成本 ...')
    print ('--- 当前时间：', day, hour, miniute)
    # 当天一件起量过的晚上6点之后判断, 当天没有起量过的, 每10分钟扫描
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                continue
            data = adgroup_data[adgroup_id]

            adgroup_past_houxiao_data = adgroup_status[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                continue

            print ('*** %d 当前数据 ...' % int(adgroup_id))
            msg = '--- 当天关注数 %d, 当天消耗 %.2f 当天关注成本 %.2f 限制: %d' % \
                        (data['today_biz_follow_uv'], data['today_cost'], data['today_biz_follow_cost'], adgroup_follow_cost_limit)
            print (msg)
            msg = '--- 当天注册数 %d, 当天消耗 %.2f 当天注册成本 %.2f 限制: %d' % \
                        (data['today_biz_reg_uv'], data['today_cost'], data['today_biz_reg_cost'], adgroup_register_cost_limit)
            print (msg)
            msg = '--- 当天表单预约数 %d, 当天消耗 %.2f 当天表单预约成本 %.2f 限制: %d' % \
                        (data['today_reservation_uv'], data['today_cost'], data['today_reservation_cost'], adgroup_reservation_cost_limit)
            print (msg)
            tomorrow = str(datetime.datetime.today() + datetime.timedelta(days = 1)).split(' ')[0]

            # 后效控停
            #后斌控停
            jinjian_num_limit = 20
            cost_rate_scale = 2
            dapan_reservation_cost = account_status['past2WeeksReservationCost']
            life_long_days = data['life_long_days']
            is_houxiao_stop = 0
            add_houxiao = 0
            if add_houxiao:
                if life_long_days >= 3 and data['past_3days_reservation_uv'] >= jinjian_num_limit and \
                    adgroup_past_houxiao_data['past 3days shouxin'] == 0:
                    print (account_id,adgroup_id, life_long_days, '3天内表单>=%d, 没有授信' * jinjian_num_limit)
                    is_houxiao_stop = 1
                elif life_long_days >= 7 and data['past 7days_reservation_uv'] >= jinjian_num_limit:
                    if data['past_7days_reservation_cost'] > dapan_reservation_cost:
                        print (account_id,adgroup_id, life_long_days, '7天内表单>=%d, 表单成本大于大盘' % (jinjian_num_limit, data['past_7days_reservation_cost']))
                        is_houxiao_stop = 1
                    else:
                        if adgroup_past_houxiao_data['past 7days shouxin'] == 0:
                            print (account_id, adgroup_id, life_long_days, '7天内表单>=10, 没有授信')
                            is_houxiao_stop = 1
                        else:
                            if adgroup_past_houxiao_data['past7DaysD7CostRate']> cost_rate_scale * account_status['assessCostRateD7']:
                                print (account_id, adgroup_id, life_long_days, \
                                    '7天内表单>=10, 有授信, 费率大于%d大盘费率' % (adgroup_past_houxiao_data['past7DaysD7CostRate'], account_status['assessCostRateD7']))
                                is_houxiao_stop = 1
                elif life_long_days >= 14 and data['past_14days_reservation_uv'] < jinjian_num_limit:
                    if data['past_14days_reservation_cost']> dapan_reservation_cost:
                        print (account_id,adgroup_id, life_long_days, '14天内表单<10, 表单成本 %.2f 大于大盘 %.2f' % \
                            (data['past_14days_reservation_cost'], dapan_reservation_cost))
                        is_houxiao_stop =1
                    else:
                        if adgroup_past_houxiao_data['past14daysShouxinUv'] == 0:
                            print(account_id,adgroup_id, life_long_days, '14天内表单<10, 没有授信')
                            is_houxiao_stop=1
                        else:
                            if adgroup_past_houxiao_data['past2WeeksD7CostRate']> cost_rate_scale * account_status['assessCostRateD7']:
                
                                print (account_id,adgroup_id, life_long_days, '14天内表单<10, 有授信, 14天费率 %.2f 大于大盘 %.2f' % \
                                        (adgroup_past_houxiao_data['past2WeeksD7CostRate'], account_status['assessCostRateD7']))
                                is_houxiao_stop = 1
                elif life_long_days >=7 and adgroup_past_houxiao_data['past_7days_jinjian'] >= jinjian_num_limit * 2:
                    if adgroup_past_houxiao_data['past7daysShouxinUv'] == 0:
                        print (account_id, adgroup_id, life_long_days, '7天内进件>=%d, 没有授信' * jinjian_num_limit * 2)
                        is_houxiao_stop = 1
                    else:
                        if adgroup_past_houxiao_data['past7daysD7CostRate']> cost_rate_scale * 0.5 * account_status['assessCostRateD7']:
                            print (account_id,adgroup_id,life_long_days, '7天内进件>=20, 有授信, 费率 %.2f 大于大盘的%.2f倍' %\
                                (adgroup_past_houxiao_data['past7daysD7CostRate'], cost_rate_scale * 0.5))
                            is_houxiao_stop =1
            if is_houxiao_stop == 1:
                select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
            else:
                # 前效控停
                if data['today_reservation_uv'] == 0 and data['today_cost'] > adgroup_reservation_cost_limit or \
                    data['today_reservation_uv'] > 0 and data['today_reservation_cost'] > adgroup_reservation_cost_limit * 1.5:
                    msg = '保本: 当天表单预约数 %d, 当天消耗 %.2f 当天表单预约成本 %.2f > %d' % \
                            (data['today_reservation_uv'], data['today_cost'], data['today_reservation_cost'], adgroup_reservation_cost_limit)
                    print (msg)
                    select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
                elif data['today_biz_reg_uv'] == 0 and data['today_cost'] > adgroup_register_cost_limit or \
                    data['today_biz_reg_uv'] > 0 and data['today_biz_reg_cost'] > adgroup_register_cost_limit:
                    msg = '保本: 当天注册数 %d, 当天消耗 %.2f 当天注册成本 %.2f > %d' % \
                            (data['today_biz_reg_uv'], data['today_cost'], data['today_biz_reg_cost'], adgroup_register_cost_limit)
                    print (msg)
                    select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
                # elif data['today_biz_follow_uv'] == 0 and data['today_cost'] > adgroup_follow_cost_limit or \
                #     data['today_biz_follow_uv'] > 0 and data['today_biz_follow_cost'] > adgroup_follow_cost_limit:
                #     msg = '保本: 当天关注数 %d, 当天消耗 %.2f 当天关注成本 %.2f > %d' % \
                #             (data['today_biz_follow_uv'], data['today_cost'], data['today_biz_follow_cost'], adgroup_follow_cost_limit)
                #     print (msg)
                #     select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])

        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res

# from budget_control import check_if_over_budget
sys.path.append('/data2/yuwu/')
from update_wyd.budget_control import check_if_over_budget
from update_wyd.config import AcquisitionControlConfig, AccountConfig, BudgetControlConfig, PathConfig, DataHeader, TargetConfig, StringFormat


def check_if_over_reg_reservation_rate(select_account_id, adgroup_status, scale_ratio = 1):
    
    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    hour = int(str(now).split(' ')[1].split(':')[0])
    miniute = int(str(now).split(' ')[1].split(':')[1])
    
    print ('--- 注册下单率 ...')
    print ('--- 当前时间：', day, hour, miniute)
    # 当天一件起量过的晚上6点之后判断, 当天没有起量过的, 每10分钟扫描
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE','ADGROUP_STATUS_NOT_IN_DELIVERY_TIME']:
                continue

            report_data = data['report_data']
            if len(report_data['ds']) == 0:
                continue

            total_reg_uv = 0
            total_reservation_uv = 0
            for reg, reserve in zip(report_data['biz_reg_uv'], report_data['reservation_uv']):
                total_reg_uv += reg
                total_reservation_uv += reserve
            
            if total_reg_uv > 0:
                ratio = total_reservation_uv/total_reg_uv
            else:
                ratio = 0

            print ('*** %d 近7天数据 ...' % int(adgroup_id))
            msg = '--- 注册数 %d 表单数 %d ratio: %.2f' % \
                        (total_reg_uv, total_reservation_uv, ratio)
            print (msg)

            one_month = str(datetime.datetime.today() + datetime.timedelta(days = 30)).split(' ')[0]
            if ratio > 0 and ratio < 0.35:
                msg = '注册转化率太低 ' + str(ratio)
                print (msg)
                select_up_adgroup_ids.append(['updateDeliveryDate-%s' % one_month, account_id, adgroup_id, msg])

            # if hour < 12:
            #     print ('--- 12点前...')
            #     if data['today_reservation_uv'] == 0 and data['today_cost'] > final_cost_goal * 5 or \
            #         data['today_reservation_uv'] > 0 and data['today_reservation_cost'] > final_cost_goal * 5:
            #         msg = '保本: 当天转化数 %d, 当天消耗 %.2f 当天转化成本 %.2f > %d' % \
            #                 (data['today_reservation_uv'], data['today_cost'], data['today_reservation_cost'], final_cost_goal * 5)
            #         print (msg)
            #         select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
            # elif hour >= 18:
            #     print ('--- 18点后...')
            #     if data['total_reservation_uv'] == 0 and data['total_cost'] > adgroup_reservation_cost_limit:
            #         msg = '保本: 总转化数 = 0, 总消耗 %.2f >%d' % (data['total_cost'], adgroup_reservation_cost_limit)
            #         print (msg)
            #         select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
            #     elif data['total_reservation_uv'] > 0 and data['total_reservation_cost'] > adgroup_reservation_cost_limit:
            #         msg = '保本: 总转化数 %d >0, 总转化成本 %.2f >%d' % (data['total_reservation_uv'], data['total_reservation_cost'], adgroup_reservation_cost_limit)
            #         print (msg)
            #         select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
            #     elif data['today_reservation_uv'] == 0 and data['today_cost'] > adgroup_reservation_cost_limit and \
            #             data['total_reservation_cost'] < adgroup_reservation_cost_limit:
            #         msg = '保本: 当天转化数 =0, 当天总消耗 %.2f >%d, 总转化成本 %.2f <%d' % \
            #                 (data['today_cost'], adgroup_reservation_cost_limit,\
            #                         data['total_reservation_cost'], adgroup_reservation_cost_limit)
            #         print (msg)
            #         select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
            #     elif data['today_is_up'] == 0 and data['today_reservation_uv'] > 0 and data['today_reservation_cost'] > adgroup_reservation_cost_limit * 1.2: # 仅适用于当天没有起量广告
            #         msg = '保本: 当天没有起量, 当天转化数 %d>0, 当天转化成本 %.2f >%d' % \
            #                 (data['today_reservation_uv'], data['today_reservation_cost'], adgroup_reservation_cost_limit * 1.2)
            #         print (msg)
            #         select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
            #     elif data['today_is_up'] == 0 and data['today_reservation_uv'] == 0 and data['today_cost'] > adgroup_reservation_cost_limit * 1.2: # 仅适用于当天没有起量广告
            #         msg = '保本: 当天没有起量, 当天转化数 %d =0, 当天消耗 %.2f >%d' % \
            #                 (data['today_reservation_uv'], data['today_cost'], adgroup_reservation_cost_limit * 1.2)
            #         print (msg)
            #         select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
            # else:
            #     print ('--- 18点前...')
            #     if data['today_is_up'] == 0 and data['today_reservation_uv'] > 0 and data['today_reservation_cost'] > adgroup_reservation_cost_limit * 1.2: # 仅适用于当天没有起量广告
            #         msg = '保本: 当天没有起量, 当天转化数 %d>0, 当天转化成本 %.2f >%d' % \
            #                 (data['today_reservation_uv'], data['today_reservation_cost'], adgroup_reservation_cost_limit * 1.2)
            #         print (msg)
            #         select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
            #     elif data['today_is_up'] == 0 and data['today_reservation_uv'] == 0 and data['today_cost'] > adgroup_reservation_cost_limit * 1.2: # 仅适用于当天没有起量广告
            #         msg = '保本: 当天没有起量, 当天转化数 %d =0, 当天消耗 %.2f >%d' % \
            #                 (data['today_reservation_uv'], data['today_cost'], adgroup_reservation_cost_limit * 1.2)
            #         print (msg)
            #         select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
 
        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res

def check_if_restart_again(select_account_id, adgroup_status, account_status, scale_ratio = 1):
    
    # 配置
    follow_final_cost_goal = int(account_status['past2WeeksFollowCost'] * scale_ratio)
    register_final_cost_goal = int(account_status['past2WeeksRegCost'] * scale_ratio)
    reservation_final_cost_goal = int(account_status['past2WeeksReservationCost'] * scale_ratio)

    
    adgroup_follow_cost_limit = follow_final_cost_goal * 2
    adgroup_register_cost_limit = register_final_cost_goal * 2
    adgroup_reservation_cost_limit = reservation_final_cost_goal * 1

    print ('--- 目标关注成本： ', follow_final_cost_goal)
    print ('--- 目标注册成本： ', register_final_cost_goal)
    print ('--- 目标表单预约成本： ', reservation_final_cost_goal)

    print ('--- 关注成本限制： ', adgroup_follow_cost_limit)
    print ('--- 目标注册成本限制： ', adgroup_register_cost_limit)
    print ('--- 目标表单预约成本限制： ', adgroup_reservation_cost_limit)
    
    now = datetime.datetime.now()
    today = str(now).split(' ')[0]
    hour = int(str(now).split(' ')[1].split(':')[0])
    miniute = int(str(now).split(' ')[1].split(':')[1])
    
    print ('--- 检查是否当天重新投放 ...')
    print ('--- 当前时间：', today, hour, miniute)
    # 当天一件起量过的晚上6点之后判断, 当天没有起量过的, 每10分钟扫描
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)

        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_NOT_IN_DELIVERY_TIME']:
                continue

            print ('*** %d 当前数据 ...' % int(adgroup_id))
            msg = '--- 当天关注数 %d, 当天消耗 %.2f 当天关注成本 %.2f > %d' % \
                        (data['today_biz_follow_uv'], data['today_cost'], data['today_biz_follow_cost'], adgroup_follow_cost_limit)
            print (msg)
            msg = '--- 当天注册数 %d, 当天消耗 %.2f 当天注册成本 %.2f > %d' % \
                        (data['today_biz_reg_uv'], data['today_cost'], data['today_biz_reg_cost'], adgroup_register_cost_limit)
            print (msg)
            msg = '--- 当天表单预约数 %d, 当天消耗 %.2f 当天表单预约成本 %.2f > %d' % \
                        (data['today_reservation_uv'], data['today_cost'], data['today_reservation_cost'], adgroup_reservation_cost_limit)
            print (msg)

            reservation_up_mark = 0
            register_up_mark = 0
            follow_up_mark = 1
            if data['today_reservation_uv'] > 0 and data['today_reservation_cost'] < adgroup_reservation_cost_limit \
                or data['today_reservation_uv'] == 0 and data['today_cost'] < adgroup_reservation_cost_limit:
                reservation_up_mark = 1
            if data['today_biz_reg_uv'] > 0 and data['today_biz_reg_cost'] < adgroup_register_cost_limit or \
                data['today_biz_reg_uv'] == 0 and data['today_cost'] < adgroup_register_cost_limit:
                register_up_mark = 1
            # if data['today_biz_follow_uv'] > 0 and data['today_biz_follow_cost'] < adgroup_follow_cost_limit:
            #     follow_up_mark = 1

            print (follow_up_mark, register_up_mark, reservation_up_mark)

            if data['today_cost'] > register_final_cost_goal and follow_up_mark and register_up_mark and reservation_up_mark:
                msg = '当天重启: 当天关注数 %d, 当天消耗 %.2f 当天关注成本 %.2f < %d' % \
                        (data['today_biz_follow_uv'], data['today_cost'], data['today_biz_follow_cost'], adgroup_follow_cost_limit)
                msg = '当天重启: 当天注册数 %d, 当天消耗 %.2f 当天注册成本 %.2f < %d' % \
                        (data['today_biz_reg_uv'], data['today_cost'], data['today_biz_reg_cost'], adgroup_register_cost_limit)
                msg = '当天重启: 当天表单预约数 %d, 当天消耗 %.2f 当天表单预约成本 %.2f < %d' % \
                        (data['today_reservation_uv'], data['today_cost'], data['today_reservation_cost'], adgroup_reservation_cost_limit)
                select_up_adgroup_ids.append(['updateDeliveryDate-%s' % today, account_id, adgroup_id, '三阶段成本全部达标(目标值2倍)...'])

        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res

def check_if_low_cost(select_account_id, adgroup_status, scale_ratio = 2):
    
    # 配置
    follow_final_cost_goal = int(follow_target * scale_ratio)
    register_final_cost_goal = int(register_target * scale_ratio)
    reservation_final_cost_goal = int(reservation_target * scale_ratio)
    
    print ('--- 低消 ...') # 每晚6点开始
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            data = adgroup_data[adgroup_id]
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                continue

            # ds_list = data['report_data']['ds']
            # if len(ds_list) > 0: # 只作用于第一天
            #     continue
            tomorrow = str(datetime.datetime.today() + datetime.timedelta(days = 1)).split(' ')[0]
            if data['today_reservation_uv'] == 0 and data['today_cost'] < 100:
                msg = '低消: 当天转化数 =0, 当天总消耗 %.2f <100' % (data['today_cost'])
                print (msg)
                select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
            else:
                if data['past_1hour_cost'] < follow_final_cost_goal: # 过去一个小时消耗低
                    msg = '低消: 前一个小时消耗 %.2f < %d' % (data['past_1hour_cost'], follow_final_cost_goal)
                    print (msg)
                    select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])

        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res

def check_if_good_enough(select_account_id, adgroup_status, scale_ratio = 2):
    
    # 配置
    final_cost_goal = int(year_target * scale_ratio) # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2 # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3
    
    print ('--- Good Enough ...') # 每晚6点开始
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            data = adgroup_data[adgroup_id]
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                continue

            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == False:
                if data['today_reservation_uv'] > 0 and data['today_reservation_cost'] < final_cost_goal:
                    msg = '成本达标: 当天没有起量, 转化数 %d > 0, 当天转化成本 %.2f < %d' % (data['today_reservation_uv'], data['today_reservation_cost'], final_cost_goal)
                    print (adgroup_id, msg)
                    tomorrow = str(datetime.datetime.today() + datetime.timedelta(days = 1)).split(' ')[0]
                    select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
                # else:
                #     msg = '成本没有达标: 当天没有起量, 转化数 %d > 0, 当天转化成本 %.2f < %d' % (data['today_reservation_uv'], data['today_reservation_cost'], final_cost_goal)
                #     print (adgroup_id, msg)
            # else:
            #     print (adgroup_id, '起量中...')

        print ('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res

def check_if_delete(select_account_id, adgroup_status, scale_ratio = 2):
    
    # 配置
    final_cost_goal = int(year_target * scale_ratio) # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2 # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3
    
    print ('--- 删除广告 ...') # 每周五晚暂停没有消耗广告
    now = datetime.datetime.now()
    prev_1day = now + datetime.timedelta(-1)
    prev_2day = now + datetime.timedelta(-2)
    prev_1day = str(prev_1day).split(' ')[0]
    prev_2day = str(prev_2day).split(' ')[0]
    print ('--- 前一天 %s 前两天 %s' % (prev_1day, prev_2day))
    max_suspend_num = 1
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()

        if account_id in ['********', '********']:
            max_suspend_num = 10
            min_active_num = 10
        elif account_id in new_accounts:
            max_suspend_num = 5
            min_active_num = 10
        else:
            max_suspend_num = 1
            min_active_num = 20
        print ('--- 最大删除量： ', max_suspend_num, ' 最少保留广告数: ', min_active_num)
        
        select_up_adgroup_ids = []
        last_7days_low_cost_adgroup_ids = []
        over_cost_adgroup_ids = []
        last_30day_no_reservation_uv_ids = []
        candidates = {'adgroup_id':[], 'created_time':[],'msg':[]}
        active_num = 0
        for adgroup_id in adgroup_ids:
            data = adgroup_data[adgroup_id]
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE', 'ADGROUP_STATUS_NOT_IN_DELIVERY_TIME', 'ADGROUP_STATUS_ACCOUNT_BALANCE_NOT_ENOUGH']:
                continue

            active_num += 1
            ds_list = data['report_data']['ds']
            life_long_cost = data['life_long_cost'] + data['today_cost']
            # print (adgroup_id, ds_list, len(ds_list), life_long_cost, data['life_long_reservation_uv'],data['life_long_days'])
            # print (data['life_long_reservation_uv'] > 0)
            # if data['life_long_reservation_uv'] > 0:
            #     print (life_long_cost/data['life_long_reservation_uv'] > 1000)
            #     print (data['life_long_reservation_uv'] > 0 and life_long_cost/data['life_long_reservation_uv'] > 1000)
            if 'life_long_days' in data and data['life_long_days'] > 7: # 只作用于投放超过7天
                if  life_long_cost < 100:
                    msg = '删除广告: 上线以来总消耗 %.2f <50' % (life_long_cost)
                    print (adgroup_id, msg)
                    candidates['adgroup_id'].append(adgroup_id)
                    candidates['created_time'].append(data['created_time'])
                    candidates['msg'].append(msg)
                elif data['today_cost'] == 0 and (len(data['report_data']['cost']) < 2 or \
                                                  (data['report_data']['cost'][-1] == 0 and data['report_data']['cost'][-2] == 0) or \
                                                    (prev_1day not in data['report_data']['ds'] and prev_2day not in data['report_data'][ 'ds'])):
                    msg = '删除广告: 连续3天消耗为0'
                    print (adgroup_id, msg)
                    candidates['adgroup_id'].append(adgroup_id)
                    candidates['created_time'].append(data['created_time'])
                    candidates['msg'].append(msg)
                
                if data['total_cost'] < 5:
                    # print ('--- 近7天低消')
                    last_7days_low_cost_adgroup_ids.append(adgroup_id)

                if account_id in old_accounts:
                    over_budget_ratio = 2
                else:
                    over_budget_ratio = 3
                if data['life_long_reservation_uv'] == 0 and life_long_cost > final_cost_goal * over_budget_ratio:
                    msg = '删除广告: 上线以来无转化, 总消耗 %.2f > %d' % (life_long_cost,  final_cost_goal * over_budget_ratio)
                    print (msg)
                    over_cost_adgroup_ids.append(adgroup_id)
                if data['life_long_reservation_uv'] > 0 and life_long_cost/data['life_long_reservation_uv'] > final_cost_goal * over_budget_ratio:
                    msg = '%s: 上线以来总成本 %.2f > %d, 总消耗 %.2f 总转化数 %d' % (str(adgroup_id), life_long_cost/data['life_long_reservation_uv'], \
                                                                       final_cost_goal * over_budget_ratio, life_long_cost, data['life_long_reservation_uv'])
                    print (msg)
                    over_cost_adgroup_ids.append(adgroup_id)

                if data['life_long_reservation_uv'] == 0 and data['life_long_days'] > 30:
                    last_30day_no_reservation_uv_ids.append(adgroup_id)
                    msg = '%s: 上线以来没有转化, 上线天数 %d>30' % (str(adgroup_id), data['life_long_days'])
                    print (msg)
                elif account_id in new_accounts and data['total_reservation_uv'] == 0 and data['life_long_days'] > 14:
                    last_30day_no_reservation_uv_ids.append(adgroup_id)
                    msg = '%s: 近7天没有转化, 上线天数 %d>14' % (str(adgroup_id), data['life_long_days'])
                    print (msg)

        candidates = pd.DataFrame(candidates)
        candidates = candidates.sort_values('created_time')
        print ('--- 上线以来低消...')
        print (candidates)
        for i in range(len(candidates)):
            select_up_adgroup_ids.append(['delete',account_id, candidates['adgroup_id'].iloc[i], candidates['msg'].iloc[i]])
            # break # 只选择一个
            if len(select_up_adgroup_ids) >= max_suspend_num:
                break

        # 近七天低消
        if len(select_up_adgroup_ids) < max_suspend_num:#== 0: # < max_suspend_num:
            candidates = {'adgroup_id':[], 'created_time':[],'life_long_cost':[],'msg':[]}
            for adgroup_id in last_7days_low_cost_adgroup_ids:
                data = adgroup_data[adgroup_id]
                life_long_cost = data['life_long_cost'] + data['today_cost']
                msg = '删除广告: 上线以来总消耗 %.2f 近7天消耗 %.2f' % (life_long_cost, data['total_cost'])
                # print (adgroup_id, msg)
                if life_long_cost < 500:
                    candidates['adgroup_id'].append(adgroup_id)
                    candidates['created_time'].append(data['created_time'])
                    candidates['life_long_cost'].append(life_long_cost)
                    candidates['msg'].append(msg)
            candidates = pd.DataFrame(candidates)
            candidates = candidates.sort_values('life_long_cost')
            print ('--- 近七天低消...')
            print (candidates)
            for i in range(len(candidates)):
                select_up_adgroup_ids.append(['delete',account_id, candidates['adgroup_id'].iloc[i], candidates['msg'].iloc[i]])
                # break
                #  每天删除最多一个近七天低消广告
                if len(select_up_adgroup_ids) == max_suspend_num:
                    break
        
        if len(select_up_adgroup_ids) < max_suspend_num: #== 0:
            candidates = {'adgroup_id':[], 'created_time':[],'life_long_reservation_cost':[], 'msg':[]}
            for adgroup_id in over_cost_adgroup_ids:
                data = adgroup_data[adgroup_id]
                life_long_cost = data['life_long_cost'] + data['today_cost']
                if data['life_long_reservation_uv']:
                    life_long_reservation_cost = life_long_cost/data['life_long_reservation_uv']
                    msg = '%s: 上线以来总成本 %.2f > %d, 总消耗 %.2f 总转化数 %d' % (str(adgroup_id), life_long_cost/data['life_long_reservation_uv'],  final_cost_goal * 2, life_long_cost, data['life_long_reservation_uv'])
                else:
                    life_long_reservation_cost = 0
                    msg = '删除广告: 上线以来无转化, 总消耗 %.2f > %d' % (life_long_cost,  final_cost_goal * 4)
                candidates['adgroup_id'].append(adgroup_id)
                candidates['created_time'].append(data['created_time'])
                candidates['life_long_reservation_cost'].append(life_long_reservation_cost)
                candidates['msg'].append(msg)
            candidates = pd.DataFrame(candidates)
            candidates = candidates.sort_values('created_time')
            print ('--- 上线以来总成本超标...')
            print (candidates)
            for i in range(len(candidates)):
                select_up_adgroup_ids.append(['delete',account_id, candidates['adgroup_id'].iloc[i], candidates['msg'].iloc[i]])
                # break # 每天删除最多一个近七天低消广告
                if len(select_up_adgroup_ids) == max_suspend_num:
                    break

        if len(select_up_adgroup_ids) < max_suspend_num: #== 0:
            candidates = {'adgroup_id':[], 'created_time':[],'life_long_days':[], 'total_cost':[], 'msg':[]}
            for adgroup_id in last_30day_no_reservation_uv_ids:
                data = adgroup_data[adgroup_id]
                candidates['adgroup_id'].append(adgroup_id)
                candidates['created_time'].append(data['created_time'])
                candidates['life_long_days'].append(data['life_long_days'])
                candidates['total_cost'].append(data['total_cost'])
                msg = '删除广告: 上线以来没有转化, 上线天数 %d>30 总消耗 %.2f' % (data['life_long_days'], data['total_cost'])
                candidates['msg'].append(msg)
            candidates = pd.DataFrame(candidates)
            candidates = candidates.sort_values('total_cost')
            print ('--- 上线超过30天, 没有转化...')
            print (candidates)
            for i in range(len(candidates)):
                select_up_adgroup_ids.append(['delete',account_id, candidates['adgroup_id'].iloc[i], candidates['msg'].iloc[i]])
                # break # 每天删除最多一个近七天低消广告
                if len(select_up_adgroup_ids) == max_suspend_num:
                    break

        print ('--- active num: ', active_num)
        print ('--- select adgroup_ids: ')
        if active_num - len(select_up_adgroup_ids) < min_active_num:
            select_up_adgroup_ids = select_up_adgroup_ids[:active_num - min_active_num]
        print ('--- 预计删除广告数: ', len(select_up_adgroup_ids))
        for d in select_up_adgroup_ids:
            print (d)
        if active_num >= min_active_num:
            res[account_id] = select_up_adgroup_ids
        else:
            print ('--- 投放广告数 %d < %d, 不删除广告...' % (active_num, min_active_num))
            res[account_id] = []



    return res

def check_if_delete_for_suspend_adgroup(select_account_id, adgroup_status, scale_ratio = 2):
    
    # 配置
    final_cost_goal = int(year_target * scale_ratio) # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2 # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3

    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    
    print ('--- 删除暂停广告 ...') # 每周五晚暂停没有消耗广告
    res = {}
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        select_up_adgroup_ids = []
        candidates = {'adgroup_id':[], 'msg':[]}
        active_num = 0
        for adgroup_id in adgroup_ids:
            data = adgroup_data[adgroup_id]
            system_status = data['system_status']
            if system_status in ['ADGROUP_STATUS_SUSPEND']: # 暂停中广告
                active_num += 1

                msg = '删除广告: 广告暂停中...'
                print (adgroup_id, msg)
                candidates['adgroup_id'].append(adgroup_id)
                candidates['msg'].append(msg)

        candidates = pd.DataFrame(candidates)
        print (candidates)
        for i in range(len(candidates)):
            select_up_adgroup_ids.append(['delete',account_id, candidates['adgroup_id'].iloc[i], candidates['msg'].iloc[i]])

        print ('--- select adgroup_ids: ', len(select_up_adgroup_ids))
        for d in select_up_adgroup_ids:
            print (d)
        res[account_id] = select_up_adgroup_ids
    return res

def update_ads_deliver_time(select_account_id, adgroup_status, scale_ratio = 2, days = 1):
    # 配置
    final_cost_goal = int(year_target * scale_ratio) # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2 # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3
    
    print ('--- 提早投放 表现好的广告 ...')
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        
        select_up_adgroup_ids = []
        active_num = 0
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
            #     print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE', 'ADGROUP_STATUS_NOT_IN_DELIVERY_TIME', 'ADGROUP_STATUS_ACCOUNT_BALANCE_NOT_ENOUGH']:
                continue

            data = adgroup_data[adgroup_id]
            adgroup_name = data['adgroup_name']
            if adgroup_name[-2:] == 'ZD':
                print ('--- %s 最大转化成本广告, 不操作...' % str(adgroup_id))
                continue

            delivery_time = ['1' for i in range(48)] # 默认 0-23点 投放
            delivery_time[-1] = '0'
            delivery_time[-2] = '0'

            if data['life_long_days'] <= 3:
                # 投放时间小于3天
                for i in range(12):
                    delivery_time[i] = '0'
                msg = '--- 修改投放时间: 6-23 广告上线时间：%d 广告过去7天消耗：%d 广告过去7天转化数：%d 广告过去7天成本：%.2f 账号过去7天转化数：%d 账号过去7天成本：%.2f' % \
                        (data['life_long_days'], data['total_cost'], data['total_reservation_uv'], data['total_reservation_cost'], data['account_past_7day_reservation_uv'], data['account_past_7day_reservation_cost'])

            elif (data['life_long_days'] > 3 and (data['total_reservation_uv'] == 0 or data['total_reservation_cost'] > adgroup_reservation_cost_limit)) or \
                (data['account_past_7day_reservation_uv'] == 0 or data['account_past_7day_reservation_cost'] > adgroup_reservation_cost_limit):
                # 广告过去7天无转化或是成本高
                # 账号过去7天无转化或是成本高
                for i in range(12):
                    delivery_time[i] = '0'
                msg = '--- 修改投放时间: 6-23 广告上线时间：%d 广告过去7天消耗：%d 广告过去7天转化数：%d 广告过去7天成本：%.2f 账号过去7天转化数：%d 账号过去7天成本：%.2f' % \
                    (data['life_long_days'], data['total_cost'], data['total_reservation_uv'], data['total_reservation_cost'], data['account_past_7day_reservation_uv'], data['account_past_7day_reservation_cost'])
            else:
                if data['total_cost'] > final_cost_goal * 2:
                    # 成本好, 消耗多
                    msg = '--- 修改投放时间: 0-23 广告上线时间：%d 广告过去7天消耗：%d 广告过去7天转化数：%d 广告过去7天成本：%.2f 账号过去7天转化数：%d 账号过去7天成本：%.2f' % \
                        (data['life_long_days'], data['total_cost'], data['total_reservation_uv'], data['total_reservation_cost'], data['account_past_7day_reservation_uv'], data['account_past_7day_reservation_cost'])
                else:
                    # 成本好, 消耗不多
                    for i in range(12):
                        delivery_time[i] = '0'
                    msg = '--- 修改投放时间: 6-23 广告上线时间：%d 广告过去7天消耗：%d 广告过去7天转化数：%d 广告过去7天成本：%.2f 账号过去7天转化数：%d 账号过去7天成本：%.2f' % \
                        (data['life_long_days'], data['total_cost'], data['total_reservation_uv'], data['total_reservation_cost'], data['account_past_7day_reservation_uv'], data['account_past_7day_reservation_cost'])

            active_num += 1
            
            delivery_time = delivery_time * 7
            delivery_time = ''.join(delivery_time)
            if delivery_time != adgroup_status[adgroup_id]['time_series']:
                print (adgroup_id, msg)
                print ('--- delivery_time: ', len(delivery_time), delivery_time)
                print ('-------------')
                select_up_adgroup_ids.append(['updateDeliveryTime-%s' % delivery_time, account_id, adgroup_id, msg])
        print ('--- active num: ', active_num)
        res[account_id] = select_up_adgroup_ids

    return res

def stop_ads_for_ndays(select_account_id, adgroup_status, scale_ratio = 2, days = 1):
    # 配置
    final_cost_goal = int(year_target * scale_ratio) # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2 # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3
    
    print ('--- 所有广告改到第二天投放 ...') # 每周五晚暂停没有消耗广告
    res = {}
    # adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    for account_id in [select_account_id]:
        print ('-----------------------------')
        print ('account_id: ', account_id)
        adgroup_ids = adgroup_status.keys()
        
        select_up_adgroup_ids = []
        active_num = 0
        for adgroup_id in adgroup_ids:
            # if adgroup_id not in adgroup_status:
            #     print ('--- %s adgroup status not found ...' % str(adgroup_id))
            #     continue
            
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE', 'ADGROUP_STATUS_NOT_IN_DELIVERY_TIME']: #, 'ADGROUP_STATUS_ACCOUNT_BALANCE_NOT_ENOUGH']:
                continue

            active_num += 1
            msg = '当天停投'
            print (adgroup_id, msg)
            tomorrow = str(datetime.datetime.today() + datetime.timedelta(days = days)).split(' ')[0]
            select_up_adgroup_ids.append(['updateDeliveryDate-%s' % tomorrow, account_id, adgroup_id, msg])
        print ('--- active num: ', active_num)
        res[account_id] = select_up_adgroup_ids

    print (res)

    return res


# predict_data = get_predict_data()
# predict_data.to_csv('predict_for_today.csv', encoding='gbk')
# exit()
predict_data = None
predict_model = None
def init_model():

    global predict_data, predict_model

    to_drop = ['Unnamed: 0', #'account_id', 'adgroup_id', 'ds',
        'operation_action', 'operation_log', 'create_time', 'adgroup_create_time']
    predict_data = pd.read_csv('predict_for_today.csv', encoding='gbk')
    for col in list(predict_data.columns):
        if 'cur_day' == col[:7]:
            to_drop.append(col)
    for col in list(predict_data.columns):
        if 'last_' in col:
            to_drop.append(col)

    # print (data[['prev_hours_cur_day_biz_follow_uv', 'life_long_days', 'hour']])
    for col in to_drop:
        if col in list(predict_data.columns):
            predict_data = predict_data.drop([col], axis = 1)

    for col in list(predict_data.columns):
        if '_uv' in col or 'hour' in col or 'life_long_days' in col:
            predict_data[col] = predict_data[col].apply(lambda x: float(x))

    predict_data['morning_or_afternoon'] = predict_data['hour'] < 12
    def expand_feature(adgroup_up_data):
        # 特征衍生
        for delta_day in [3,7,14]:
            for prev_delta_day in [3,7,14]:
                if prev_delta_day >= delta_day:
                    break
                adgroup_up_data['avg_cost_diff-%d-%d' % (delta_day, prev_delta_day)] = \
                    adgroup_up_data['past_%ddays_cost' % delta_day]/delta_day - adgroup_up_data['past_%ddays_cost' % prev_delta_day]/prev_delta_day
                adgroup_up_data['avg_reservation_cost_diff-%d-%d' % (delta_day, prev_delta_day)] = \
                    adgroup_up_data['past_%ddays_reservation_cost' % delta_day] - adgroup_up_data['past_%ddays_reservation_cost' % prev_delta_day]
                adgroup_up_data['avg_reservation_uv_diff-%d-%d' % (delta_day, prev_delta_day)] = \
                    adgroup_up_data['past_%ddays_reservation_uv' % delta_day]/delta_day - adgroup_up_data['past_%ddays_reservation_uv' % prev_delta_day]/prev_delta_day
                # adgroup_up_data['avg_up_num_diff-%d-%d' % (delta_day, prev_delta_day)] = adgroup_up_data['前%d天起量次数' % delta_day]/delta_day - adgroup_up_data['前%d天起量次数' % prev_delta_day]/prev_delta_day
                # adgroup_up_data['avg_up_cost_diff-%d-%d' % (delta_day, prev_delta_day)] = adgroup_up_data['前%d天起量消耗' % delta_day]/delta_day - adgroup_up_data['前%d天起量消耗' % prev_delta_day]/prev_delta_day
                # adgroup_up_data['avg_up_reservation_cost_diff-%d-%d' % (delta_day, prev_delta_day)] = adgroup_up_data['前%d天起量成本' % delta_day] - adgroup_up_data['前%d天起量成本' % prev_delta_day]


        adgroup_up_data = adgroup_up_data.replace([np.nan, np.inf], np.nan)

        return adgroup_up_data
    predict_data = expand_feature(predict_data)

    predict_model = XGBClassifier(n_jobs=1)
    predict_model.load_model('get_data/wyd_up_model.json')

    # predict_score = predict_model.predict_proba(predict_data)[:,1]
    # print (predict_score)

    # exit()

init_model()
# now = datetime.datetime.now()
# day = str(now).split(' ')[0]
# hour = int(str(now).split(' ')[1].split(':')[0])
# print (day, hour)
# account_id = ********
# adgroup_id = ***********
# adgroup_predict_data = predict_data[(predict_data['account_id'] == account_id)&\
#                                                     (predict_data['adgroup_id'] < adgroup_id)&\
#                                                         (predict_data['ds'] == day)&\
#                                                             (predict_data['hour'] == hour)]
# print (adgroup_predict_data)
# exit()

get_adgroup_report_date = '2025-07-11'
check_up_adgroups_date = {}
check_up_adgroups_date_1 = {}
check_reup_adgroups_date = {}

now = datetime.datetime.now()
day = str(now).split(' ')[0]
hour = int(str(now).split(' ')[1].split(':')[0])
miniute = int(str(now).split(' ')[1].split(':')[1])
res_list = []
week_day = get_weekday(day)
print ('--- now: ', str(now), week_day)

# signature_data_pd = pd.read_csv('signature_data.csv', encoding='gbk')
# signature_data = {}
# for signature, big_category, small_category, class_label, cost in zip(list(signature_data_pd['signature']), \
#                                                                 list(signature_data_pd['big_category']), list(signature_data_pd['small_category']), \
#                                                                 list(signature_data_pd['class_label']), list(signature_data_pd['class_cost'])):
#     if cost > 1000:
#         signature_data[signature] = {'big_category': big_category,  'small_category':small_category, 'class_label': class_label, 'cost':cost}

if day != get_adgroup_report_date: # 当天没有更新历史数据
    get_adgroup_report(account_ids)
    print ('--- download all history data...')
    get_adgroup_report_date = day
    account_up_time = get_account_up_time()
    with open('account_up_time.json', 'w', encoding = 'utf8') as f:
        json.dump(account_up_time, f, indent=4, ensure_ascii=False)
    account_create_time = get_account_create_time()
    with open('account_create_time.json', 'w', encoding = 'utf8') as f:
        json.dump(account_create_time, f, indent=4, ensure_ascii=False)
else:
    if os.path.exists('account_up_time.json') == False:
        account_up_time = get_account_up_time()
        with open('account_up_time.json', 'w', encoding = 'utf8') as f:
            json.dump(account_up_time, f, indent=4, ensure_ascii=False)
    else:
        account_up_time = json.load(open('account_up_time.json', encoding='utf8'))

    if os.path.exists('account_create_time.json') == False:
        account_create_time = get_account_create_time()
        with open('account_create_time.json', 'w', encoding = 'utf8') as f:
            json.dump(account_create_time, f, indent=4, ensure_ascii=False)
    else:
        account_create_time = json.load(open('account_create_time.json', encoding='utf8'))
print ('--- get_adgroup_report_date: ', get_adgroup_report_date)

class RequestHandler(tornado.web.RequestHandler):
    pass

class CreateNew(RequestHandler):
    '''广告投放
    '''
    
    
    executor = ThreadPoolExecutor(10)
    @tornado.web.asynchronous
    @tornado.gen.coroutine

    def post(self):
 
        global cur_time, thresh, get_adgroup_report_date, check_up_adgroups_date, account_create_time

        cur_time = datetime.datetime.now()
        t = time.time()
        upload_data = json.loads(self.request.body.decode('utf-8'))
        try:
              
            if 'localhost' in self.request.headers['Host']:
                upload_data = json.loads(upload_data)

            #task = upload_data['task']
            account_id = str(upload_data['accountId'])
            print ('--- account_id: ', account_id)

            now = datetime.datetime.now()
            day = str(now).split(' ')[0]
            hour = int(str(now).split(' ')[1].split(':')[0])
            miniute = int(str(now).split(' ')[1].split(':')[1])
            week_day = get_weekday(day)
            new_res = [{'accountId': account_id, 'actionList':[]}]
            today = str(datetime.datetime.today()).split(' ')[0]
            tomorrow = str(datetime.datetime.today() + datetime.timedelta(days = 1)).split(' ')[0]
            next_2day = str(datetime.datetime.today() + datetime.timedelta(days = 2)).split(' ')[0]
            if 0:
                code = False
                print ('---集体暂停新建广告...')
            elif account_id not in old_accounts + median_accounts + new_accounts:
                code = False
                print ('--- %s 不在操作账号列表中, 不新建...' % account_id)
            elif week_day in ['星期二', '星期四', '星期六', '星期日']: # 星期六不新建
                code = False
                print ('--- 星期二,  星期四, 星期六、星期日 不新建广告...')
            elif account_id in create_exclude_accounts:
                code = False
                print ('--- %s 不新建广告...' % account_id)
            # elif week_day == '星期日': # 星期日早上新建
            #     print ('--- 星期日早上新建广告...')
            #     create_time = account_create_time[account_id]
            #     print ('--- 账号新建时间: ', create_time)
            #     print ('--- 目前时间: ', hour, miniute)
            #     if hour == int(create_time.split('-')[0]) and abs(miniute - int(create_time.split('-')[1])) <= 2:
            #         print ('--- 周日上午新建广告...')
            #         code = True
            #     else:
            #         print ('--- 不在广告上新时间...')
            #         code = False
            elif week_day not in ['星期二', '星期四', '星期六', '星期日']:
                if account_id not in afternoon_accounts:
                    create_time = account_create_time[account_id]
                    print ('--- 账号新建时间: ', create_time)
                    print ('--- 目前时间: ', hour, miniute)
                    if hour == int(create_time.split('-')[0]) and abs(miniute - int(create_time.split('-')[1])) <= 2:
                        print ('--- 上午起量账号 工作日下午14-16点新建广告...')
                        code = True
                        new_res[0]['actionList'].append({'action':'createNew', 'deliveryDate':today})
                        # if week_day == '星期五':
                        #     print ('--- 创建周日新广告...')
                        #     new_res[0]['actionList'].append({'action':'createNew', 'deliveryDate':next_2day}) # 周日上新
                    else:
                        print ('--- 不在广告上新时间...')
                        code = False
                elif hour == 16 and miniute < 35 and account_id in afternoon_accounts:
                    print ('--- 下午起量账号 工作日下午16点半新建广告...')
                    code = True
                    new_res[0]['actionList'].append({'action':'createNew', 'deliveryDate':tomorrow})
                    # if week_day == '星期五':
                    #     print ('--- 创建周日新广告...')
                    #     new_res[0]['actionList'].append({'action':'createNew', 'deliveryDate':next_2day}) # 周日上新
                else:
                    print ('--- 不进行广告新建...')
                    code = False
                
                # if week_day == '星期五' and hour == 17 and miniute < 5:
                #     print ('--- 创建周日新广告...')
                #     new_res[0]['actionList'].append({'action':'createNew', 'deliveryDate':next_2day}) # 周日上新

            else:
                print ('--- 不进行广告新建...')
                code = False

            result = {}
            result['data'] = new_res
            result['code'] = 'True'
            result['msg'] = 'success...'

            print ('--- result: ', result)
            print ('--- getting result time is: ', time.time() - t)
        except Exception as e:
            print ('ERROR: ', e)
            result = {
                'data':'',
                'code': False,
                'msg': '请求失败: ' + str(e)
            }
        self.clear_header(name='Server')
        self.set_header('Content-Type', 'application/json;charset=utf-8')
        self.write(json.dumps(result))
        self.flush()
        self.finish()
        print ('write fininsh time: ', datetime.datetime.now())


account_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
dapan_data = json.load(open('get_data/dapan_data.json', encoding='utf8'))

class AdManage(RequestHandler):
    '''广告投放
    '''
    
    
    executor = ThreadPoolExecutor(1)
    @tornado.web.asynchronous
    @tornado.gen.coroutine

    def update_data(self, account_id):
        # update_adgroup_system_status(account_id) # 更新广告状态
        res = update_adgroup_today_report_data(account_id) # 更新广告当天投放数据
        print ('--- update_data res: ', res)
        return res

    def post(self):
 
        global cur_time, thresh, get_adgroup_report_date, check_up_adgroups_date, account_up_time, account_create_time
        global account_report_data, dapan_data
        global predict_data, predict_model

        cur_time = datetime.datetime.now()
        t = time.time()
        upload_data = json.loads(self.request.body.decode('utf-8'))
        try:
            print ('\n---------------------------------------------------------------')
            print ('current time: ', cur_time)
            print ('request headers: ', self.request.headers)
            print ('receive image ...')
            if 'localhost' in self.request.headers['Host']:
                upload_data = json.loads(upload_data)

            #task = upload_data['task']
            account_id = str(upload_data['accountId'])
            adgroup_list = upload_data['adgroupList']
            #print ('--- task: ', task)
            print ('--- account_id: ', account_id)
            
            # 大盘和账号级别数据
            account_status = {}
            account_status['goalBidPrice'] = upload_data['goalBidPrice']
            # account_status['accountDailyBudget'] = upload_data['accountDailyBudget']

            # 大盘前效广播口径表单预约成本
            account_status['past2WeeksFollowCost'] = dapan_data['biz_follow_cost'] # upload_data['past2WeeksFollowCost']
            account_status['past2WeeksRegCost'] = dapan_data['biz_reg_cost'] # upload_data['past2WeeksRegCost']
            # account_status['past3DaysReservationCost'] = upload_data['past3DaysReservationCost']
            # account_status['past5DaysReservationCost'] = upload_data['past5DaysReservationCost']
            # account_status['past7DaysReservationCost'] = upload_data['past7DaysReservationCost']
            account_status['past2WeeksReservationCost'] = dapan_data['reservation_cost'] # upload_data['past2WeeksReservationCost']

            # 考察
            # account_status['past7DaysAccountAssessCost'] = upload_data['past7DaysAccountAssessCost'] #当前账号播放口径近7天平均表单成本
            account_status['aveD7CostRate'] = upload_data['aveD7CostRate'] # 项目 平均 D7 费率: 过去14天大盘平均费率
            account_status['assessCostD7'] = upload_data['assessCostD7'] # 项目 D7 考核成本: 过去14天大盘平均企业进件成本 
            # account_status['assessCostRateD7'] = upload_data['assessCostRateD7'] # 

            print ('--- 账号大盘数据: ')
            print (account_status)

            adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
            account_data = adgroup_report_data[account_id]

            adgroup_status = {}
            uping_num = 0
            up_already_num = 0
            if len(adgroup_list) > 0:
                for adgroup in adgroup_list:  # 广告组级别数据
                    adgroup_id = str(adgroup['adgroupId'])
                    adgroup_status[adgroup_id] = {}
                    adgroup_status[adgroup_id]['todayAcquisitionStatus'] = adgroup['todayAcquisitionStatus']
                    adgroup_status[adgroup_id]['autoAcquisitionStatus'] = adgroup['autoAcquisitionStatus']
                    adgroup_status[adgroup_id]['systemStatus'] = adgroup['systemStatus']
                    
                    # 费率
                    adgroup_status[adgroup_id]['past3DaysD7CostRate'] = adgroup['past3DaysD7CostRate']
                    adgroup_status[adgroup_id]['past5DaysD7CostRate'] = adgroup['past5DaysD7CostRate']
                    adgroup_status[adgroup_id]['past7DaysD7CostRate'] = adgroup['past7DaysD7CostRate']
                    adgroup_status[adgroup_id]['past2WeeksD7CostRate'] = adgroup['past2WeeksD7CostRate']
                    
                    # 进件成本：后效企业进件个数和成本
                    adgroup_status[adgroup_id]['past3DaysD7ReservationUv'] = adgroup['past3DaysD7ReservationUv']
                    adgroup_status[adgroup_id]['past5DaysD7ReservationUv'] = adgroup['past5DaysD7ReservationUv']
                    adgroup_status[adgroup_id]['past7DaysD7ReservationUv'] = adgroup['past7DaysD7ReservationUv']
                    adgroup_status[adgroup_id]['past2WeeksD7ReservationUv'] = adgroup['past2WeeksD7ReservationUv']
                    adgroup_status[adgroup_id]['past3DaysD7ReservationCost'] = adgroup['past3DaysD7ReservationCost']
                    adgroup_status[adgroup_id]['past5DaysD7ReservationCost'] = adgroup['past5DaysD7ReservationCost']
                    adgroup_status[adgroup_id]['past7DaysD7ReservationCost'] = adgroup['past7DaysD7ReservationCost']
                    adgroup_status[adgroup_id]['past2WeeksD7ReservationCost'] = adgroup['past2WeeksD7ReservationCost']

                    # 授信个数
                    adgroup_status[adgroup_id]['past3DaysD7ShouxinUv'] = adgroup['past3DaysD7ShouxinUv']
                    adgroup_status[adgroup_id]['past5DaysD7ShouxinUv'] = adgroup['past5DaysD7ShouxinUv']
                    adgroup_status[adgroup_id]['past7DaysD7ShouxinUv'] = adgroup['past7DaysD7ShouxinUv']
                    adgroup_status[adgroup_id]['past2WeeksD7ShouxinUv'] = adgroup['past2WeeksD7ShouxinUv']
                    adgroup_status[adgroup_id]['past3DaysD7ShouxinCost'] = adgroup['past3DaysD7ShouxinCost']
                    adgroup_status[adgroup_id]['past5DaysD7ShouxinCost'] = adgroup['past5DaysD7ShouxinCost']
                    adgroup_status[adgroup_id]['past7DaysD7ShouxinCost'] = adgroup['past7DaysD7ShouxinCost']
                    adgroup_status[adgroup_id]['past2WeeksD7ShouxinCost'] = adgroup['past2WeeksD7ShouxinCost']

                    # 异常状态
                    adgroup_status[adgroup_id]['reservationState'] = adgroup['reservationState']
                    adgroup_status[adgroup_id]['backendState'] = adgroup['backendState']
                    adgroup_status[adgroup_id]['costState'] = adgroup['costState']
                    # adgroup_status[adgroup_id]['fluctuateDays'] = adgroup['fluctuateDays']
                    adgroup_status[adgroup_id]['past5RateFluctuate'] = adgroup['past5RateFluctuate']  # 费率异常
                    adgroup_status[adgroup_id]['past5CostFluctuate'] = adgroup['past5CostFluctuate'] # 企业进件成本异常
                    # adgroup_status[adgroup_id]['time_series'] = adgroup['time_series']

                    if adgroup['todayAcquisitionStatus']:
                        up_already_num += 1
                    if adgroup['autoAcquisitionStatus'] == 'AUTO_ACQUISTION_STATUS_PENDING':
                        uping_num += 1

            print ('--- 当天已经起量广告数: ', up_already_num)
            print ('--- 正在起量广告数: ', uping_num)

            # print (adgroup_status)

            print ('--- 账号广告数据: ')
            for adgroup_id in adgroup_status.keys():
                print ('---------------------------')
                adgroup_data = account_data[adgroup_id]
                print (adgroup_id, adgroup_data['created_time'])
                for dt in [3,7,14]:
                    if 'past_%dday_cost' % dt in adgroup_data.keys():
                        if dt == 14:
                            houxiao_dt = '2Weeks'
                        else:
                            houxiao_dt = '%dDays' % dt
                        cost = adgroup_data['past_%dday_cost' % dt]
                        cost_rate = adgroup_status[adgroup_id]['past%sD7CostRate' % houxiao_dt]
                        if str(cost_rate) in ["None", 'nan', '']:
                            cost_rate = 0
                            treaty = 0
                        else:
                            treaty = cost/cost_rate
                        if str(adgroup_status[adgroup_id]['past%sD7ReservationUv' % houxiao_dt]) in ["None", 'nan', '']:
                            pastD7ReservationUv = 0
                            pastD7ReservationCost = 0
                        else:
                            pastD7ReservationUv = adgroup_status[adgroup_id]['past%sD7ReservationUv' % houxiao_dt]
                        if str(adgroup_status[adgroup_id]['past%sD7ReservationCost' % houxiao_dt]) in ["None", 'nan', '']:
                            pastD7ReservationCost = 0
                        else:
                            pastD7ReservationCost = adgroup_status[adgroup_id]['past%sD7ReservationCost' % houxiao_dt]
                        if str(adgroup_status[adgroup_id]['past%sD7ShouxinUv' % houxiao_dt]) in ["None", 'nan', '']:
                            pastD7ShouxinUv = 0
                        else:
                            pastD7ShouxinUv = adgroup_status[adgroup_id]['past%sD7ShouxinUv' % houxiao_dt]
                        # print (pastD7ReservationCost, str(pastD7ReservationCost))
                        # print (adgroup_data['past_%dday_biz_follow_cost' % dt], adgroup_data['past_%dday_biz_reg_cost' % dt], adgroup_data['past_%dday_reservation_cost' % dt], \
                        #         adgroup_data['past_%dday_reservation_uv' % dt], pastD7ReservationUv, \
                        #              pastD7ReservationCost, pastD7ShouxinUv, treaty, cost_rate)
                        print ('近%d天: 消耗 %.2f 成本 (%.2f, %.2f, %.2f) 表单 %d 企业进件 %d 企业进件成本 %.2f 授信个数 %d 授信金额 %d 费率 %.2f' % \
                            (dt, cost, \
                             adgroup_data['past_%dday_biz_follow_cost' % dt], adgroup_data['past_%dday_biz_reg_cost' % dt], adgroup_data['past_%dday_reservation_cost' % dt], \
                                adgroup_data['past_%dday_reservation_uv' % dt], pastD7ReservationUv, \
                                     pastD7ReservationCost, pastD7ShouxinUv, treaty, cost_rate))
            msg = '成功'
            code = True
            result = {}
            result['data'] = {}
            # for site_set in sitesetlist:
            #     print ('--- site_set: ', site_set)
            #     result['data'][site_set] = get_weights(merchantId, start_date, end_date, site_set, widthHeightList, dataDimensionType, datacaliberType)
            now = datetime.datetime.now()
            day = str(now).split(' ')[0]
            hour = int(str(now).split(' ')[1].split(':')[0])
            miniute = int(str(now).split(' ')[1].split(':')[1])
            res_list = []
            week_day = get_weekday(day) 
            print ('--- now: ', str(now), week_day)

            if hour == 5 and miniute < 2: # 当天没有更新历史数据 day != get_adgroup_report_date or 
                get_adgroup_report(account_ids)
                print ('--- download all history data...')
                account_report_data = json.load(open('account_report_data.json', encoding='utf8'))
                dapan_data = json.load(open('get_data/dapan_data.json', encoding='utf8'))
                get_adgroup_report_date = day
                account_up_time = get_account_up_time()
                with open('account_up_time.json', 'w', encoding = 'utf8') as f:
                    json.dump(account_up_time, f, indent=4, ensure_ascii=False)
                account_create_time = get_account_create_time()
                with open('account_create_time.json', 'w', encoding = 'utf8') as f:
                    json.dump(account_create_time, f, indent=4, ensure_ascii=False)
                # 加载模型和预测数据
                predict_data = get_predict_data()
                predict_data.to_csv('predict_for_today.csv', encoding='gbk')
                init_model()
                res = [{'accountId': account_id, 'actionList':[]}]
                msg = '--- 拉取数据...'
                code = True
            elif account_id not in old_accounts + median_accounts + new_accounts:
                print ('--- %s 账号不在操作列表...' % account_id)
                res = [{'accountId': account_id, 'actionList':[]}]
                msg = '--- 不操作...'
                code = True
            else:
                print ('#############################################################################')
                print ('账号: ', account_id)
                res = update_adgroup_today_report_data(account_id)
                if res == -1:
                    res = [{'accountId': account_id, 'actionList':[]}]
                    msg = '--- 账号没有数据...'
                    code = True
                elif account_id in control_exclude_accounts:
                    res = [{'accountId': account_id, 'actionList':[]}]
                    msg = '--- 账号不操作...'
                    code = True
                else:
                    
                    scale_ratio = 1
                    print ('--- scale ratio: ', scale_ratio)
                    #print ('#### 测试 ####')
                    #if '1133' in account_id:
                    #    _ = get_up_adgroup_ids_for_all_2(account_id, adgroup_status, scale_ratio=scale_ratio) # 1133 激进
                    #    _ = get_up_adgroup_ids_for_all_early(account_id, adgroup_status, scale_ratio=scale_ratio) # 1133 激进

                    # 每天删除长期低消广告
                    # print ('#############################################################################')
                    # print ('--- 是否删除长期低消广告 ...')
                    # res = check_if_delete(account_id, adgroup_status, scale_ratio=scale_ratio)
                    # if hour == 19 and abs(miniute - 45) <= 2:#  and week_day == '星期五': 
                    #     print ('--- 下午5点30分, 执行广告删除 ...')
                    #     res_list.append(res)

                    # # 删除暂停的广告
                    # if '2678' in account_id: 
                    #     print ('#############################################################################')
                    #     # get_adgroup_report([account_id])
                    #     # update_adgroup_today_report_data(account_id)
                    #     print ('--- 广告是否已经被暂停 ...')
                    #     res = check_if_delete_for_suspend_adgroup(account_id, adgroup_status, scale_ratio=scale_ratio)
                    #     res_list.append(res)

                    # 后效暂停
                        

                    # 判断当天是否停投
                    print ('#############################################################################')
                    lines = open('stop_dates.txt').readlines()
                    stop_dates = [s.strip() for s in lines]
                    if len(stop_dates) == 0:
                        print ('--- 没有停投日期...')
                    else:
                        print ('--- 停投广告日期: ', stop_dates)
                    if  day in stop_dates:
                        stop_ad = True
                        print ('--- 今日停止投放广告....')
                    else:
                        print ('--- 当天不在停投日期中...')
                        stop_ad = False
                    
                    if stop_ad == True and account_id == '********':
                        if hour == 20 and abs(miniute - 25) < 2:
                            print ('#############################################################################')
                            print ('--- 当天停投所有广告 ...')
                            res = stop_ads_for_ndays(account_id, adgroup_status, scale_ratio=scale_ratio, days = 1)
                            res_list.append(res)
                    # elif hour == 20 and miniute < 30  and week_day == '星期五':
                    #     print ('#############################################################################')
                    #     print ('--- 周五晚集体改投放时间到下周一 ...')
                    #     res = stop_ads_for_ndays(account_id, adgroup_status, scale_ratio = 1, days = 3)
                    #     res_list.append(res)
                    else:

                        is_up_today = 1

                        _ = get_up_adgroup_ids_for_all_morning(account_id, adgroup_status,  account_status, scale_ratio=scale_ratio)
                        _ = get_up_adgroup_ids_for_all_afternoon(account_id, adgroup_status, account_status, scale_ratio=scale_ratio)
                        # if account_id == '********':
                        #     res_list.append(res)

                        if is_up_today == 1: # and account_id == '********': # and account_id == '********':
                            if week_day in ['星期六', '星期日']:
                                print ('#############################################################################')
                                print ('--- 星期六和星期日不起量...') 
                            elif account_id in up_exclude_accounts:
                                print ('#############################################################################')
                                print ('--- 账号不起量')
                            elif hour in [9, 10] and account_id not in old_accounts:
                                print ('#############################################################################')
                                print ('--- 上午 获取一键起量广告 ...') 
                                if uping_num >= 1 or up_already_num >= 1:
                                    print ('--- 上午起量广告>=1个, 不再起量...')
                                else:
                                    res = get_up_adgroup_ids_for_all_morning(account_id, adgroup_status, account_status, scale_ratio=scale_ratio)
                                    res_list.append(res)
                            elif hour in [14, 15] and account_id not in old_accounts:
                                print ('#############################################################################')
                                print ('--- 下午 获取一键起量广告 ...') 
                                if account_id in new_accounts + median_accounts and up_already_num >= 2 or account_id in old_accounts and up_already_num >= 1:
                                    print ('--- 全天已起量广告已满, 不再起量...')
                                else:
                                    res = get_up_adgroup_ids_for_all_afternoon(account_id, adgroup_status, account_status, scale_ratio=scale_ratio)
                                    res_list.append(res)

                        # if is_up_today and account_id in old_accounts + median_accounts:    # 一键起量策略

                        #     print ('#############################################################################')
                        #     print ('--- 测试 ...')
                        #     if 1:
                        #         res = get_up_adgroup_ids(account_id, adgroup_status, scale_ratio=scale_ratio)
                        #         print (res)

                        #     if week_day in ['星期六', '星期日']:
                        #         print ('#############################################################################')
                        #         print ('--- 星期六和星期日不起量...') 
                        #     elif account_id in up_exclude_accounts:
                        #         print ('#############################################################################')
                        #         print ('--- 账号不起量')
                        #     # elif hour == 10 and miniute < 30 and account_id not in afternoon_accounts: # 每天上午10点一件起量
                        #     elif hour <= 11 and account_id not in afternoon_accounts:
                        #         up_time = account_up_time[account_id]
                        #         print ('--- 账号起量时间: ', up_time)
                        #         if hour == int(up_time.split('-')[0]) and abs(miniute - int(up_time.split('-')[1])) <= 2:
                        #         # if '1133' in account_id or '1356' in account_id:
                        #             if account_id not in check_up_adgroups_date:
                        #                 check_up_adgroups_date[account_id] = ''
                        #             if day != check_up_adgroups_date[account_id]:
                        #                 print ('#############################################################################')
                        #                 print ('--- 上午 获取一键起量广告 ...') 
                        #                 if account_id in new_accounts:
                        #                     if account_id in two_small_up_account:
                        #                         res = get_up_adgroup_ids_for_all_3(account_id, adgroup_status, scale_ratio=scale_ratio)
                        #                     else:   
                        #                         res = get_up_adgroup_ids_for_all_1(account_id, adgroup_status, scale_ratio=scale_ratio)
                        #                 elif account_id in median_accounts + old_accounts:
                        #                     res = get_up_adgroup_ids_for_all_2(account_id, adgroup_status, scale_ratio=scale_ratio) # 1133 激进
                        #                 check_up_adgroups_date[account_id] = day
                        #                 print ('--- set check_up_adgroups_date: ', get_adgroup_report_date)
                        #                 res_list.append(res)
                        #         else:
                        #             print ('--- 不在账号起量时间...')
                        #     elif hour == 14 and miniute < 5 and account_id in afternoon_accounts: # 每天下午2点一件起量
                        #         if account_id not in check_up_adgroups_date:
                        #             check_up_adgroups_date[account_id] = ''
                        #         if day != check_up_adgroups_date[account_id]:
                        #             print ('#############################################################################')
                        #             print ('--- 下午 获取一键起量广告 ...')
                        #             if account_id in new_accounts:
                        #                 if account_id in two_small_up_account:
                        #                     res = get_up_adgroup_ids_for_all_3(account_id, adgroup_status, scale_ratio=scale_ratio)
                        #                 else:   
                        #                     res = get_up_adgroup_ids_for_all_1(account_id, adgroup_status, scale_ratio=scale_ratio)
                        #             elif account_id in median_accounts + old_accounts:
                        #                 res = get_up_adgroup_ids_for_all_2(account_id, adgroup_status, scale_ratio=scale_ratio) # 1133 激进
                        #             check_up_adgroups_date[account_id] = day
                        #             print ('--- set check_up_adgroups_date: ', get_adgroup_report_date)
                        #             res_list.append(res)

                        #     if week_day not in ['星期六', '星期日'] and account_id in old_accounts and hour == 8 and miniute < 5: #hour == 8 and 
                        #         print ('#############################################################################')
                        #         print ('--- 每天早上8点, 查看是否有可以提前起量的广告')
                        #         res = get_up_adgroup_ids_for_all_early(account_id, adgroup_status, scale_ratio=scale_ratio) # 1133 提早起量
                        #         res_list.append(res)
                            
                        #     if week_day not in ['星期六', '星期日'] and account_id not in up_exclude_accounts and \
                        #         hour < 7 and miniute <= 55 and miniute > 0: # 每天上午每个小时查看是否有自己跑量优质广告-********
                        #         if account_id in old_accounts:
                        #             if account_id not in check_up_adgroups_date_1:
                        #                 check_up_adgroups_date_1[account_id] = [day, 0]
                        #             print ('--- check_up_adgroups_date_1: ', check_up_adgroups_date_1)
                        #             if day != check_up_adgroups_date_1[account_id][0] or check_up_adgroups_date_1[account_id][1] == 0:
                        #                 print ('#############################################################################')
                        #                 print ('--- 每天上午九点前查看是否有自己跑量优质广告, 消耗有明显上升趋势 ...')
                        #                 res = check_if_up_hourly(account_id, adgroup_status)
                        #                 res_list.append(res)
                        #                 if len(res[account_id]) > 0:
                        #                     check_up_adgroups_date_1[account_id] = [day, 1]
                        #                 else:
                        #                     check_up_adgroups_date_1[account_id] = [day, 0]
                        #                     print ('#############################################################################')
                        #                     print ('--- 每天上午九点前查看是否有可以提前起量的广告')
                        #                     res = get_up_adgroup_ids_for_all_early(account_id, adgroup_status, scale_ratio=scale_ratio) # 1133 提早起量
                        #                     res_list.append(res)
                        #                     if len(res[account_id]) > 0:
                        #                         check_up_adgroups_date_1[account_id] = [day, 1]
                        #                     else:
                        #                         check_up_adgroups_date_1[account_id] = [day, 0]
                            
                            try:
                                if week_day not in ['星期六', '星期日']: 
                                    print ('#############################################################################')
                                    print ('--- 模型预测起量广告 ...')
                                    if 1: #account_id not in old_accounts: # 老账号不限制起量成本
                                        res = get_up_adgroup_ids_for_all_by_model(account_id, adgroup_status, account_status, scale_ratio=scale_ratio)
                                        # res_list.append(res)
                            except Exception as e:
                                print ('!!! model predict error: ', e)

                            if hour >= 12 and hour < 16 and account_id in median_accounts: 
                                print ('#############################################################################')
                                print ('--- 每天下午12-15点查看是否第二次起量上午起量的广告 ...')
                                res = check_if_reup_again(account_id, adgroup_status, account_status, scale_ratio=scale_ratio)
                                # res_list.append(res)


                            # res = check_if_end_up_per_10mins(account_id, adgroup_status, scale_ratio=scale_ratio)
                            # res_list.append(res)


                            stop_up_hour = 12
                            if hour >= stop_up_hour:
                                print ('#############################################################################')
                                print ('--- 每天%d点后检查是否暂停一键起量 ...' % stop_up_hour)
                                if account_id not in over_budget_exclude_accounts: # 老账号不限制起量成本
                                    res = check_if_end_up_per_10mins(account_id, adgroup_status, account_status, scale_ratio=scale_ratio) # 检查是否暂停一键起量
                                    # res_list.append(res)
                                else:
                                    print ('--- 不限制 起量成本 ...')
                        # else:
                        #     print ('--- 当天不起量...')
                        

                        # if account_id in new_accounts and hour >= 16: #week_day not in ['星期六', '星期日'] and hour >= 16: 
                        #     print ('#############################################################################')
                        #     print ('--- 每天下午14点后检查自己跑量优秀广告 ...')
                        #     res = check_if_good_enough(account_id, adgroup_status, scale_ratio=scale_ratio) # 自己跑量优秀广告
                        #     res_list.append(res)

                        if account_id not in over_budget_exclude_accounts:
                            print ('#############################################################################')
                            print ('--- 全天检查超成本广告 ...')
                            # res = check_if_over_budget_old(account_id, adgroup_status, account_status, scale_ratio=scale_ratio) # 检查超成本广告
                            res = check_if_over_budget(account_id, adgroup_status, account_status, BudgetControlConfig, AccountConfig, PathConfig, DataHeader, StringFormat, scale_ratio=scale_ratio)
                            res_list.append(res)
                        else:
                            print ('#############################################################################')
                            print ('--- 不限制 广告成本 ...')

                        if week_day not in ['星期六', '星期日'] and hour >= 6 and hour < 18 and account_id not in old_accounts: 
                            print ('#############################################################################')
                            print ('--- 每天18点前检查复起广告 ...')
                            res = check_if_restart_again(account_id, adgroup_status, account_status, scale_ratio=scale_ratio)
                            # res_list.append(res)

                        if account_id in new_accounts:
                            check_low_cost_hour = 20
                        elif account_id in median_accounts:
                            check_low_cost_hour = 20
                        elif account_id in old_accounts:
                            check_low_cost_hour = 18
                        else:
                            check_low_cost_hour = 20
                        print ('--- 开始检查低消时间: ', check_low_cost_hour)
                        if hour >= check_low_cost_hour:
                            if account_id not in low_cost_exclude_accounts:
                                print ('#############################################################################')
                                print ('--- 每天晚上%d点后检查低消广告 ...' % check_low_cost_hour)
                                res = check_if_low_cost(account_id, adgroup_status, scale_ratio=scale_ratio)
                                # res_list.append(res)
                            else:
                                print ('#############################################################################')
                                print ('--- 每天晚上%d点 不限制 广告低消 ...' % check_low_cost_hour)

                        try:
                            
                            print ('#############################################################################')
                            print ('--- 检查注册下单比过低的广告 ...')
                            res = check_if_over_reg_reservation_rate(account_id, adgroup_status, scale_ratio = 1)
                            # res_list.append(res)
                        except Exception as e:
                            print ('ERROR: ', e)
                        

                        # if hour == 23 and abs(miniute - 10) <= 3:
                        #     if account_id in median_accounts+ new_accounts:
                        #         print ('#############################################################################')
                        #         print ('--- 每天晚上23点调整广告投放时间 ...')
                        #         res = update_ads_deliver_time(account_id, adgroup_status, scale_ratio=scale_ratio)
                        #         res_list.append(res)

                    if res_list:
                        new_res = [{'accountId': account_id, 'actionList':[]}]
                        up_adgroup_ids = []
                        for res in res_list:
                            for action in res[account_id]:
                                if 'updateDeliveryDate' in action[0]:
                                    new_res[0]['actionList'].append({'action':action[0].split('-')[0],'deliveryDate':'-'.join(action[0].split('-')[1:]), 'adgroupId':action[2],'condition':action[3]})
                                if 'updateDeliveryTime' in action[0]:
                                    new_res[0]['actionList'].append({'action':action[0].split('-')[0],'deliveryTime':action[0].split('-')[1], 'adgroupId':action[2],'condition':action[3]})
                                elif 'startAutoAcquisition' in action[0]:
                                    if action[2] not in up_adgroup_ids:
                                        new_res[0]['actionList'].append({'action':action[0],'adgroupId':action[2],'condition':action[3], 'autoAcquisitionBudget':action[4]})
                                        up_adgroup_ids.append(action[2])
                                else:
                                    new_res[0]['actionList'].append({'action':action[0],'adgroupId':action[2],'condition':action[3]})
                               
                        res = new_res
                    else:
                        new_res = [{'accountId': account_id, 'actionList':[]}]
                        res = new_res

            result['data'] = res
            result['code'] = code
            result['msg'] = msg

            print ('--- result: ', result) 
            print ('--- getting result time is: ', time.time() - t)
        except Exception as e:
            print ('ERROR: ', e)
            result = {
                'data': {}, 
                'code': False,
                'msg': '请求失败: ' + str(e)
            }
        self.clear_header(name='Server')
        self.set_header('Content-Type', 'application/json;charset=utf-8')
        self.write(json.dumps(result))
        self.flush()
        self.finish()
        print ('write fininsh time: ', datetime.datetime.now())

class GetSignatureLabel(RequestHandler):
    '''广告投放
    '''

    executor = ThreadPoolExecutor(10)
    @tornado.web.asynchronous
    @tornado.gen.coroutine

    def post(self):
 
        global signature_data

        cur_time = datetime.datetime.now()
        t = time.time()
        upload_data = json.loads(self.request.body.decode('utf-8'))
        code = True
        class_type = '无'
        try:
              
            if 'localhost' in self.request.headers['Host']:
                upload_data = json.loads(upload_data)

            task = upload_data['task']
            if task == 'refresh_signature_data':
                signature_data_pd = pd.read_csv('signature_data.csv', encoding='gbk')
                signature_data = {}
                for signature, big_category, small_category, class_label, cost in zip(list(signature_data_pd['signature']), \
                                                                list(signature_data_pd['big_category']), list(signature_data_pd['small_category']), \
                                                                list(signature_data_pd['class_label']), list(signature_data_pd['class_cost'])):
                    if cost > 1000:
                        signature_data[str(signature)] = {'big_category': big_category,  'small_category':small_category, \
                                                          'class_label': class_label, 'cost': cost}
                print ('---  重新加载 signature_data...')
                print ('--- total signature: ', len(signature_data.keys()))
                print ('0084d1f1b78e465779d62990865bc446' in signature_data.keys())
                result = {}
                result['code'] = True
                result['msg'] = 'reload signature data ...'
            else:
                signature = str(upload_data['signature'])
                print ('--- signature: ', signature)
                print ('--- total signature: ', len(signature_data.keys()))
                if signature in signature_data.keys():
                    big_category = signature_data[signature]['big_category']
                    small_category = signature_data[signature]['small_category']
                    class_label = signature_data[signature]['class_label']
                    cost = signature_data[signature]['cost']
                else:
                    class_label = '无'
                    big_category = '无'
                    small_category = '无'
                    cost = -1

                result = {}
                result['big_category'] = big_category
                result['small_category'] = small_category
                result['class_label'] = class_label
                result['cost'] = cost
                result['code'] = code
                result['msg'] = 'get data success...'

            print ('--- result: ', result) 
            print ('--- getting result time is: ', time.time() - t)
        except Exception as e:
            print ('ERROR: ', e)
            result = {
                'big_category': '',
                'small_category': '',
                'class_label': '',
                'cost':-1,
                'code': False,
                'msg': '请求失败: ' + str(e)
            }
        self.clear_header(name='Server')
        self.set_header('Content-Type', 'application/json;charset=utf-8')
        self.write(json.dumps(result))
        self.flush()
        self.finish()
        print ('write fininsh time: ', datetime.datetime.now())

if __name__ == '__main__':

    port = 8866

    application = tornado.web.Application([
            (r"/AdManage", AdManage),
            (r"/CreateNew", CreateNew),
            (r"/GetSignatureLabel", GetSignatureLabel),
    ]) 
    application.listen(port)


    print('\n---- Start at port {} ----\n'.format(port))
    
    tornado.ioloop.IOLoop.current().start()
