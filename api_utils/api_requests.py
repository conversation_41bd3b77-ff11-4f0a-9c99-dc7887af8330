import json
import time
import random
import datetime
import requests
from typing import Optional, List, Dict, Any


def get_reports(
    account_id: str,
    api_config,
    page: int = 1,
    page_size: int = 100,
    report_date: Optional[str] = None,
    time_line: str = 'REQUEST_TIME',
    level: str = 'REPORT_LEVEL_DYNAMIC_CREATIVE',
    group_by: Optional[List[str]] = None,
    fields: Optional[List[str]] = None,
    filtering: Optional[List[Dict[str, Any]]] = None,
    adgroup_id: Optional[str] = None,
    hourly: bool = True
) -> Dict[str, Any]:
    """
    Get hourly reports through API call
    
    Args:
        account_id: The account ID to get reports for
        api_config: Configuration object containing API settings
        page: Page number for pagination (default: 1)
        page_size: Number of items per page (default: 100)
        report_date: Date for the report in YYYY-MM-DD format. If None, uses today
        time_line: Timeline type ('REQUEST_TIME' or 'REPORTING_TIME')
        level: Report level (e.g., 'REPORT_LEVEL_DYNAMIC_CREATIVE', 'REPORT_LEVEL_ADGROUP')
        group_by: List of fields to group by
        fields: List of fields to include in the response
        filtering: List of filter conditions
        adgroup_id: Specific adgroup ID to filter by (for old-style requests)
        hourly: Whether to get hourly reports (True) or daily reports (False)
        
    Returns:
        Dict containing the API response
    """
    # Build URL
    interface = api_config.HOURLY_INTERFACE if hourly else api_config.DAILY_INTERFACE
    url = api_config.API_URL + interface
    
    # Common authentication parameters
    common_parameters = {
        'access_token': api_config.ACCESS_TOKEN,
        'timestamp': int(time.time()),
        'nonce': str(time.time()) + str(random.randint(0, 999999)),
    }
    
    # Handle date range
    if report_date is None:
        # Use today's date
        today = datetime.datetime.today()
        report_date = str(today).split(' ')[0]
        start_date = report_date
        end_date = report_date
    else:
        # Parse provided date
        date_parts = report_date.split('-')
        start_date = "%d-%02d-%02d" % (int(date_parts[0]), int(date_parts[1]), int(date_parts[2]))
        end_date = start_date  # Same day for both start and end
    
    # Default group_by based on level
    if group_by is None:
        if level == 'REPORT_LEVEL_DYNAMIC_CREATIVE':
            group_by = ["date", "adgroup_id", "dynamic_creative_id", "hour"] if hourly else ["date", "adgroup_id", "dynamic_creative_id"]
        else:
            group_by = ["adgroup_id", "hour", "date"] if hourly else ["adgroup_id", "date"]
    
    # Default fields based on level
    if fields is None:
        if level == 'REPORT_LEVEL_DYNAMIC_CREATIVE':
            fields = [
                'adgroup_id', 'dynamic_creative_id', 'date', 'view_count', 
                'valid_click_count', 'cost', 'acquisition_cost', 'cpc', 
                'thousand_display_price', 'reservation_uv', 'biz_follow_uv', 
                'biz_follow_cost', 'biz_reg_uv', 'reg_cost_pla', 
                'page_reservation_cost_with_people'
            ]
        else:
            fields = [
                'adgroup_id', 'date', 'view_count', 'valid_click_count', 'ctr', 'cost', 
                'acquisition_cost', 'biz_follow_uv', 'biz_reg_uv', 'reservation_uv'
            ]
    if hourly:
        # insert 'hour' to header after 'date' if no 'hour' in fields
        if 'hour' not in fields:
            fields.insert(fields.index('date') + 1, 'hour')
    
    # Build request parameters
    parameters = {
        "account_id": account_id,
        "level": level,
        "date_range": {
            "start_date": start_date,
            "end_date": end_date
        },
        "group_by": group_by,
        "time_line": time_line,
        "page": page,
        "page_size": page_size,
        "fields": fields
    }
    
    # Build filtering for specific adgroup if provided
    if adgroup_id is not None and filtering is None:
        parameters["filtering"] = filtering
    
    # Merge with common parameters
    parameters.update(common_parameters)
    
    # Convert non-string parameters to JSON strings
    for key in parameters:
        if type(parameters[key]) is not str:
            parameters[key] = json.dumps(parameters[key])
    
    # Make the API request
    response = requests.get(url, params=parameters)
    
    return response.json()


def get_hourly_reports_dynamic_creative(
    account_id: str,
    api_config,
    page: int = 1,
    page_size: int = 500,
    report_date: Optional[str] = None,
    time_line: str = 'REQUEST_TIME'
) -> Dict[str, Any]:
    """
    Get hourly reports for dynamic creative level.
        
    Args:
        account_id: The account ID to get reports for
        api_config: Configuration object containing API settings
        page: Page number for pagination
        page_size: Number of items per page
        report_date: Date for the report in YYYY-MM-DD format
        time_line: Timeline type ('REQUEST_TIME' or 'REPORTING_TIME')
        
    Returns:
        Dict containing the API response
    """
    return get_reports(
        account_id=account_id,
        api_config=api_config,
        page=page,
        page_size=page_size,
        report_date=report_date,
        time_line=time_line,
        level='REPORT_LEVEL_DYNAMIC_CREATIVE',
        hourly=True
    )


def get_hourly_reports_adgroup(
    account_id: str,
    adgroup_id: str,
    api_config,
    page: int = 1,
    page_size: int = 100
) -> Dict[str, Any]:
    """
    Get hourly reports for a specific adgroup.
        
    Args:
        account_id: The account ID to get reports for
        adgroup_id: The specific adgroup ID to get reports for
        api_config: Configuration object containing API settings
        page: Page number for pagination
        page_size: Number of items per page
        
    Returns:
        Dict containing the API response
    """
    filtering = [
            {
                "field": "adgroup_id",
                "operator": "EQUALS",
                "values": [str(adgroup_id)]
            }
        ]
    return get_reports(
        account_id=account_id,
        api_config=api_config,
        page=page,
        page_size=page_size,
        report_date=None,  # Use today's date
        time_line='REQUEST_TIME',
        level='REPORT_LEVEL_ADGROUP',
        adgroup_id=adgroup_id,
        filtering=filtering,
        group_by=["date", "hour"],
        hourly=True,
        fields = ['date', 'hour', 'view_count', 'valid_click_count', 'ctr', 'cost', 
                'acquisition_cost', 'biz_follow_uv', 'biz_reg_uv', 'reservation_uv']
    )


def get_daily_reports_adgroup(
    account_id: str,
    api_config,
    start_date: str,
    end_date: str,
    page: int = 1,
    page_size: int = 100
) -> Dict[str, Any]:
    """
    Get daily reports for a specific adgroup.
        
    Args:
        account_id: The account ID to get reports for
        api_config: Configuration object containing API settings
        start_date: Start date for the report in YYYY-MM-DD format
        end_date: End date for the report in YYYY-MM-DD format
        page: Page number for pagination
        page_size: Number of items per page
        
    Returns:
        Dict containing the API response
    """
    return get_reports(
        account_id=account_id,
        api_config=api_config,
        page=page,
        page_size=page_size,
        start_date=start_date,
        end_date=end_date,
        level='REPORT_LEVEL_ADGROUP',
        hourly=False
    )


def get_adgroups_info(account_id, api_config, page, page_size):

    access_token = api_config.ACCESS_TOKEN
    interface = api_config.ADGROUPS_INTERFACE
    api_url = api_config.API_URL
    url = api_url + interface

    nonce = str(time.time()) + str(random.randint(0, 999999))
    # print ('nonce: ', nonce)

    common_parameters = {
            'access_token': access_token, 
            'timestamp': int(time.time()), 
            'nonce': nonce,
        }

    parameters = {
            "account_id": account_id,
            "page": page,
            "page_size": page_size,
            "is_deleted": False,
            'fields':["adgroup_id","adgroup_name",'created_time', 'last_modified_time', 'begin_date', 'system_status','first_day_begin_time', 'end_date', 'billing_event', 'bid_amount', 'total_budget', 'daily_budget', 'scene_spec', \
                'optimization_goal', 'targeting_translation', 'ad_count', 'bid_strategy', 'cold_start_audience', \
                'expand_enabled', 'smart_bid_type', 'smart_cost_cap', 'custom_adgroup_tag', 'flow_optimization_enabled', \
                'promoted_object_type', 'targeting_id', 'targeting', 'bid_strategy', 'cold_start_audience', 'is_deleted']
        }

    parameters.update(common_parameters)
    for k in parameters:
        if type(parameters[k]) is not str:
            parameters[k] = json.dumps(parameters[k])

    r = requests.get(url, params = parameters)

    return r.json()