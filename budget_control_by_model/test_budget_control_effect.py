import json
import pandas as pd
import numpy as np


# cost per pv thresholds
biz_follow_cost_thres = 220 
biz_reg_cost_thres = 701 
page_reservation_cost_with_people_thres =  2588

def calculate_overall_metrics(data):
    # Calculate overall metrics: sum all
    overall_metrics = {
        'cost': data['cost'].sum(),
        'biz_follow_uv': data['biz_follow_uv'].sum(),
        'biz_reg_uv': data['biz_reg_uv'].sum(),
        'reservation_uv': data['reservation_uv'].sum()
    }

    overall_metrics['biz_follow_cost'] = overall_metrics['cost'] / overall_metrics['biz_follow_uv']
    overall_metrics['biz_reg_cost'] = overall_metrics['cost'] / overall_metrics['biz_reg_uv']
    overall_metrics['page_reservation_cost_with_people'] = overall_metrics['cost'] / overall_metrics['reservation_uv']
    
    return {k:float(v) for k, v in overall_metrics.items()}


def filter_stopped_adgroups_hourly_entry(data, predict_result, score_thres=0.5, restart_hour=6, use_budget_control=False):
    filtered_data = data.copy()
    filtered_data['ts'] = pd.to_datetime(filtered_data['ts'])

    if use_budget_control:
        for metric in ['biz_follow_uv', 'biz_reg_uv', 'reservation_uv']:
            filtered_data[f'cur_day_{metric}_up_to_prev_h'] = 0
        filtered_data['cur_day_cost_up_to_prev_h'] = 0.0
        for metric in ['reg', 'apply', 'credit']:
            filtered_data[f'cur_day_{metric}_cost_up_to_prev_h'] = 0.0
        grouped = filtered_data.groupby(['account_id', 'adgroup_id', 'ds'])
        for (account_id, adgroup_id, date), group in grouped:
            group = group.sort_values('hour')
            for i in range(1, len(group)):
                """
                for metric in ['cost', 'biz_follow_uv', 'biz_reg_uv', 'reservation_uv']:
                    filtered_data.loc[group.index[i], f'cur_day_{metric}_up_to_prev_h'] = group.loc[group.index[:i], metric].sum()
                for metric in ['reg', 'apply', 'credit']:
                    if filtered_data.loc[group.index[i], f'cur_day_{metric}_up_to_prev_h'] == 0:
                        filtered_data.loc[group.index[i], f'cur_day_{metric}_cost_up_to_prev_h'] = 0.0
                    else:
                        filtered_data.loc[group.index[i], f'cur_day_{metric}_cost_up_to_prev_h'] = filtered_data.loc[group.index[i], 'cur_day_cost_up_to_prev_h'] / filtered_data.loc[group.index[i], f'cur_day_{metric}_up_to_prev_h']
                """
                filtered_data.loc[group.index[i], 'cur_day_cost_up_to_prev_h'] = group.loc[group.index[:i], 'cost'].sum()
                filtered_data.loc[group.index[i], 'cur_day_biz_follow_uv_up_to_prev_h'] = group.loc[group.index[:i], 'biz_follow_uv'].sum()
                filtered_data.loc[group.index[i], 'cur_day_biz_reg_uv_up_to_prev_h'] = group.loc[group.index[:i], 'biz_reg_uv'].sum()
                filtered_data.loc[group.index[i], 'cur_day_reservation_uv_up_to_prev_h'] = group.loc[group.index[:i], 'reservation_uv'].sum()

                if filtered_data.loc[group.index[i], 'cur_day_biz_follow_uv_up_to_prev_h'] == 0:
                    filtered_data.loc[group.index[i], 'cur_day_biz_follow_cost_up_to_prev_h'] = 0.0
                else:
                    filtered_data.loc[group.index[i], 'cur_day_biz_follow_cost_up_to_prev_h'] = filtered_data.loc[group.index[i], 'cur_day_cost_up_to_prev_h'] / filtered_data.loc[group.index[i], 'cur_day_biz_follow_uv_up_to_prev_h']
                
                if filtered_data.loc[group.index[i], 'cur_day_biz_reg_uv_up_to_prev_h'] == 0:
                    filtered_data.loc[group.index[i], 'cur_day_biz_reg_cost_up_to_prev_h'] = 0.0
                else:
                    filtered_data.loc[group.index[i], 'cur_day_biz_reg_cost_up_to_prev_h'] = filtered_data.loc[group.index[i], 'cur_day_cost_up_to_prev_h'] / filtered_data.loc[group.index[i], 'cur_day_biz_reg_uv_up_to_prev_h']

                if filtered_data.loc[group.index[i], 'cur_day_reservation_uv_up_to_prev_h'] == 0:
                    filtered_data.loc[group.index[i], 'cur_day_page_reservation_cost_with_people_up_to_prev_h'] = 0.0
                else:
                    filtered_data.loc[group.index[i], 'cur_day_page_reservation_cost_with_people_up_to_prev_h'] = filtered_data.loc[group.index[i], 'cur_day_cost_up_to_prev_h'] / filtered_data.loc[group.index[i], 'cur_day_reservation_uv_up_to_prev_h']
        # budget control based on budget limit
        reg_limit = biz_follow_cost_thres * 2
        apply_limit = biz_reg_cost_thres * 2
        credit_limit = page_reservation_cost_with_people_thres * 1
        stop_by_cost_limit = filtered_data[filtered_data['cur_day_biz_follow_cost_up_to_prev_h'] > reg_limit].copy()
        stop_by_cost_limit = stop_by_cost_limit[stop_by_cost_limit['cur_day_biz_reg_cost_up_to_prev_h'] > apply_limit]
        stop_by_cost_limit = stop_by_cost_limit[stop_by_cost_limit['cur_day_page_reservation_cost_with_people_up_to_prev_h'] > credit_limit]
        stop_by_cost_limit['stop_start'] = stop_by_cost_limit['ts']
        stop_by_cost_limit['stop_end'] = (
            pd.to_datetime(stop_by_cost_limit['ts'].dt.date) +
            pd.Timedelta(days=1, hours=restart_hour)
        )
        print(f"Found {len(stop_by_cost_limit)} stop signals by cost limit")

    predict_df = predict_result.copy()
    print(f"Current date range: {predict_df['ds'].min()} to {predict_df['ds'].max()}")

    predict_df['ts'] = pd.to_datetime(predict_df['ts'])

    stop_signals = predict_df[predict_df['predict_score'] > score_thres].copy()

    """if stop_signals.empty:
        print(f"No stop signals found (no predictions > {score_thres})")
        return filtered_data"""

    print(f"Found {len(stop_signals)} stop signals by model")

    stop_signals['stop_start'] = stop_signals['ts']
    stop_signals['stop_end'] = (
        pd.to_datetime(stop_signals['ts'].dt.date) +
        pd.Timedelta(days=1, hours=restart_hour)
    )

    # Create a list to collect rows to remove
    rows_to_remove = []

    # For each stop signal, find overlapping data entries
    for _, stop_row in stop_signals.iterrows():
        account_id = stop_row['account_id']
        adgroup_id = stop_row['adgroup_id']
        stop_start = stop_row['stop_start']
        stop_end = stop_row['stop_end']

        mask = (
            (filtered_data['account_id'] == account_id) &
            (filtered_data['adgroup_id'] == adgroup_id) &
            (filtered_data['ts'] >= stop_start) &
            (filtered_data['ts'] < stop_end)
        )

        rows_to_remove.extend(filtered_data[mask].index.tolist())
    
    if use_budget_control:
        for _, stop_row in stop_by_cost_limit.iterrows():
            account_id = stop_row['account_id']
            adgroup_id = stop_row['adgroup_id']
            stop_start = stop_row['stop_start']
            stop_end = stop_row['stop_end']

            mask = (
                (filtered_data['account_id'] == account_id) &
                (filtered_data['adgroup_id'] == adgroup_id) &
                (filtered_data['ts'] >= stop_start) &
                (filtered_data['ts'] < stop_end)
            )

            rows_to_remove.extend(filtered_data[mask].index.tolist())


    rows_to_remove = list(set(rows_to_remove))
    filtered_data = filtered_data.drop(index=rows_to_remove)

    print(f"Original data: {len(data)} rows")
    print(f"Filtered data: {len(filtered_data)} rows")
    print(f"Removed: {len(rows_to_remove)} rows")

    return filtered_data


if __name__ == "__main__":
    save_dir = 'data_20250323-********/'
    import os

    score_thres = 0.7
    restart_hour = 5
    # score_thres = 0.68
    # restart_hour = 8
    print(f"===Config: score threshold: {score_thres}, restart hour: {restart_hour}")

    data = pd.read_csv(os.path.join(save_dir, 'aggregated_adgroup_hourly_data.csv'))
    data['ts'] = pd.to_datetime(data['ds']) + pd.to_timedelta(data['hour'], unit='h')

    print(f'===Running test for train data...')
    predict_result = pd.read_csv(os.path.join(save_dir, '0_day_predict_result_train.csv'))
    train_data = data[data['ts'] < max(predict_result['ts'])]
    filtered_data = filter_stopped_adgroups_hourly_entry(train_data, predict_result, score_thres, restart_hour)
    org_metrics = calculate_overall_metrics(train_data)
    print(f"\nOverall metrics for original data:")
    print(json.dumps(org_metrics))
    train_metrics = calculate_overall_metrics(filtered_data)
    print(f"\nOverall metrics for filtered data:")
    print(json.dumps(train_metrics))
    # compute overall cost, reservation_uv diff ratio
    cost_diff = (train_metrics['cost'] - org_metrics['cost']) / org_metrics['cost']
    reservation_uv_diff = (train_metrics['reservation_uv'] - org_metrics['reservation_uv']) / org_metrics['reservation_uv']
    page_reservation_cost_with_people_diff = (train_metrics['page_reservation_cost_with_people'] - org_metrics['page_reservation_cost_with_people']) / org_metrics['page_reservation_cost_with_people']
    print(f"\nCost diff ratio: {cost_diff * 100:.2f}%")
    print(f"Credit PV diff ratio: {reservation_uv_diff * 100:.2f}%")
    print(f"Credit cost diff ratio: {page_reservation_cost_with_people_diff * 100:.2f}%")

    # filtered_data.to_csv('filtered_adgroup_hourly_data.csv', index=False)
    print(f'\n===Running test for test data...')
    print(f'===Running test for test data (first 7 days)...')
    predict_result = pd.read_csv(os.path.join(save_dir, '0_day_predict_result_test.csv'))

    predict_result['ts'] = pd.to_datetime(predict_result['ts'])
    cut_thres = min(predict_result['ts']) + pd.Timedelta(days=7)
    predict_result = predict_result[predict_result['ts'] <= cut_thres]

    test_data = data[data['ts'] >= min(predict_result['ts'])]
    filtered_data = filter_stopped_adgroups_hourly_entry(test_data, predict_result, score_thres, restart_hour)
    org_metrics = calculate_overall_metrics(test_data)
    print(f"\nOverall metrics for original data:")
    print(json.dumps(org_metrics))
    test_metrics = calculate_overall_metrics(filtered_data)
    print(f"\nOverall metrics for filtered data:")
    print(json.dumps(test_metrics))

    cost_diff = (test_metrics['cost'] - org_metrics['cost']) / org_metrics['cost']
    reservation_uv_diff = (test_metrics['reservation_uv'] - org_metrics['reservation_uv']) / org_metrics['reservation_uv']
    page_reservation_cost_with_people_diff = (test_metrics['page_reservation_cost_with_people'] - org_metrics['page_reservation_cost_with_people']) / org_metrics['page_reservation_cost_with_people']
    print(f"\nCost diff ratio: {cost_diff * 100:.2f}%") # percentage
    print(f"Credit PV diff ratio: {reservation_uv_diff * 100:.2f}%")
    print(f"Credit cost diff ratio: {page_reservation_cost_with_people_diff * 100:.2f}%")

    print(f'\n===Running test for test data (last 7 days)...')
    predict_result = pd.read_csv(os.path.join(save_dir,'0_day_predict_result_test.csv'))
    predict_result['ts'] = pd.to_datetime(predict_result['ts'])
    predict_result = predict_result[predict_result['ts'] > cut_thres]

    test_data = data[data['ts'] >= min(predict_result['ts'])]
    filtered_data = filter_stopped_adgroups_hourly_entry(test_data, predict_result, score_thres, restart_hour)
    org_metrics = calculate_overall_metrics(test_data)
    print(f"\nOverall metrics for original data:")
    print(json.dumps(org_metrics))
    test_metrics = calculate_overall_metrics(filtered_data)
    print(f"\nOverall metrics for filtered data:")
    print(json.dumps(test_metrics))

    cost_diff = (test_metrics['cost'] - org_metrics['cost']) / org_metrics['cost']
    reservation_uv_diff = (test_metrics['reservation_uv'] - org_metrics['reservation_uv']) / org_metrics['reservation_uv']
    page_reservation_cost_with_people_diff = (test_metrics['page_reservation_cost_with_people'] - org_metrics['page_reservation_cost_with_people']) / org_metrics['page_reservation_cost_with_people']
    print(f"\nCost diff ratio: {cost_diff * 100:.2f}%") # percentage
    print(f"Credit PV diff ratio: {reservation_uv_diff * 100:.2f}%")
    print(f"Credit cost diff ratio: {page_reservation_cost_with_people_diff * 100:.2f}%")
    