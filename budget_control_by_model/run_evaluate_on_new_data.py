import pandas as pd 
import numpy as np
import os 
import json
import xgboost as xgb
import lightgbm as lgb
from aggregate_adgroup_data import load_and_parse_data, aggregate_by_adgroup, calculate_daily_totals, calculate_historical_totals
from budget_control_model import hourly_feature_engineering, daily_feature_engineering, prepare_data, evaluate_model
from test_budget_control_effect import filter_stopped_adgroups_hourly_entry, calculate_overall_metrics


def main(save_dir = 'samples/'):
    source_dir = 'samples/'
    # source_dir = 'data_20250323-********-full'
    # save_dir = 'data_20250623-********'
    score_thres = 0.8
    restart_hour = 5
    print(f"===Config: score threshold: {score_thres}, restart hour: {restart_hour}")
    file_dir = 'data_dynamic_creative_level/hourly_reports_REQUEST_TIME/'
    lines = open('../shipinhao_accounts.txt').readlines()
    accounts_list = [str(l.strip()) for l in lines]
    print("Loading data...")
    all_df = []
    for file in os.listdir(file_dir):
        if file.endswith('.csv'):
            file_path = os.path.join(file_dir, file)
            if file.split('.')[0] not in accounts_list:
                continue
            print(f"Loading {file_path}...")
            df = load_and_parse_data(file_path)
            all_df.append(df)
    df = pd.concat(all_df, ignore_index=True)
    
    # Convert date to datetime
    df['ds'] = pd.to_datetime(df['ds'])
    
    # Aggregate by adgroup
    print("Aggregating data by adgroup...")
    hourly_data = aggregate_by_adgroup(df)


    print("Calculating daily totals...")
    daily_totals = calculate_daily_totals(df)
    
    print("Calculating historical totals...")
    past_1day = calculate_historical_totals(hourly_data, 1)
    past_3day = calculate_historical_totals(hourly_data, 3)
    past_7day = calculate_historical_totals(hourly_data, 7)
    
    # Merge all data together
    print("Merging all data...")
    daily_data = daily_totals.copy()
    
    daily_data = daily_data.merge(past_1day, on=['account_id', 'adgroup_id', 'ds'], how='left')
    daily_data = daily_data.merge(past_3day, on=['account_id', 'adgroup_id', 'ds'], how='left')
    daily_data = daily_data.merge(past_7day, on=['account_id', 'adgroup_id', 'ds'], how='left')

    print(f"Columns after merge: {list(daily_data.columns)}")
    
    daily_data = daily_data.fillna(0)
    
    # Sort by account_id, adgroup_id, date, hour (if hour column exists)
    sort_columns = ['account_id', 'adgroup_id', 'ds']
    if 'hour' in daily_data.columns:
        sort_columns.append('hour')
    daily_data = daily_data.sort_values(sort_columns)

    hourly_data = hourly_feature_engineering(hourly_data)
    daily_data = daily_feature_engineering(daily_data)
    n = 0 # train for next n days, 0 = same day prediction
    print(f"===Preparing data for next {n} day...")

    all_data = prepare_data(hourly_data, daily_data, n)
    cut_date = all_data.ds.max() - - pd.DateOffset(weeks=2)

    # train
    full_data = all_data[all_data['ds'] < cut_date]

    full_data.to_csv(os.path.join(save_dir, f'{n}_day_merged_data.csv'), index=False)
    # label_col = f'{n}_day_over_page_reservation_cost_with_people_thres'
    label_col = f'{n}_day_no_credit'
    target = full_data[label_col]
    target = target.dropna()
    features = full_data.drop([label_col], axis=1)
    features = features.loc[target.index]

    model = xgb.XGBClassifier()
    # model = lgb.LGBMClassifier()
    model.load_model(os.path.join(source_dir, f'{n}_day_model.json'))
    res_df = evaluate_model(model, features, target, 'Train Data')
    res_df.to_csv(os.path.join(save_dir, f'{n}_day_predict_result_new.csv'))

    hourly_data['ts'] = pd.to_datetime(hourly_data['ds']) + pd.to_timedelta(hourly_data['hour'], unit='h')
    filtered_data = filter_stopped_adgroups_hourly_entry(hourly_data, res_df, score_thres, restart_hour, use_budget_control=True)
    org_metrics = calculate_overall_metrics(hourly_data)

    print(f"\nOverall metrics for original data:")
    print(json.dumps(org_metrics))

    filtered_metrics = calculate_overall_metrics(filtered_data)
    print(f"\nOverall metrics for filtered data:")
    print(json.dumps(filtered_metrics))

    cost_diff = (filtered_metrics['cost'] - org_metrics['cost']) / org_metrics['cost']
    reservation_uv_diff = (filtered_metrics['reservation_uv'] - org_metrics['reservation_uv']) / org_metrics['reservation_uv']
    page_reservation_cost_with_people_diff = (filtered_metrics['page_reservation_cost_with_people'] - org_metrics['page_reservation_cost_with_people']) / org_metrics['page_reservation_cost_with_people']
    print(f"\nCost diff ratio: {cost_diff * 100:.2f}%") # percentage
    print(f"Credit PV diff ratio: {reservation_uv_diff * 100:.2f}%")
    print(f"Credit cost diff ratio: {page_reservation_cost_with_people_diff * 100:.2f}%")

    # test
    full_data = all_data[all_data['ds'] >= cut_date]

    full_data.to_csv(os.path.join(save_dir, f'{n}_day_merged_data.csv'), index=False)
    # label_col = f'{n}_day_over_page_reservation_cost_with_people_thres'
    label_col = f'{n}_day_no_credit'
    target = full_data[label_col]
    target = target.dropna()
    features = full_data.drop([label_col], axis=1)
    features = features.loc[target.index]

    model = xgb.XGBClassifier()
    model.load_model(os.path.join(source_dir, f'{n}_day_model.json'))
    res_df = evaluate_model(model, features, target, 'Test Data')
    res_df.to_csv(os.path.join(save_dir, f'{n}_day_predict_result_new.csv'))

    hourly_data['ts'] = pd.to_datetime(hourly_data['ds']) + pd.to_timedelta(hourly_data['hour'], unit='h')
    filtered_data = filter_stopped_adgroups_hourly_entry(hourly_data, res_df, score_thres, restart_hour, use_budget_control=True)
    org_metrics = calculate_overall_metrics(hourly_data)

    print(f"\nOverall metrics for original data:")
    print(json.dumps(org_metrics))

    filtered_metrics = calculate_overall_metrics(filtered_data)
    print(f"\nOverall metrics for filtered data:")
    print(json.dumps(filtered_metrics))

    cost_diff = (filtered_metrics['cost'] - org_metrics['cost']) / org_metrics['cost']
    reservation_uv_diff = (filtered_metrics['reservation_uv'] - org_metrics['reservation_uv']) / org_metrics['reservation_uv']
    page_reservation_cost_with_people_diff = (filtered_metrics['page_reservation_cost_with_people'] - org_metrics['page_reservation_cost_with_people']) / org_metrics['page_reservation_cost_with_people']
    print(f"\nCost diff ratio: {cost_diff * 100:.2f}%") # percentage
    print(f"Credit PV diff ratio: {reservation_uv_diff * 100:.2f}%")
    print(f"Credit cost diff ratio: {page_reservation_cost_with_people_diff * 100:.2f}%")

if __name__ == "__main__":
    main()