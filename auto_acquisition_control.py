# -*- coding: utf-8 -*-
import json
import datetime
import pandas as pd
from helpers import get_weekday


def check_if_meet_up_condition(adgroup_id, adgroup_status, adgroup_data, account_config):
    if adgroup_id not in adgroup_status:
        # print ('--- %s adgroup status not found ...' % str(adgroup_id))
        return 0
    # if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
    #     print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
    #     return 0
    system_status = adgroup_status[adgroup_id]['systemStatus']
    if system_status not in account_config.ACTIVE_STATUS:
        return 0
    data = adgroup_data[adgroup_id]
    adgroup_name = data['adgroup_name']
    if adgroup_name[-2:] == 'ZD':
        print('--- 最大转化成本广告, 不起量...')
        return 0
    ds_list = data['report_data']['ds']
    if len(ds_list) == 0:  # 没有历史数据
        return 0

    return 1


def get_up_adgroup_ids_for_all_morning(select_account_id, adgroup_status, control_config, account_config,
                                       target_config, path_config, data_header, scale_ratio=1):  # 方案1

    print('--------------------------------------')
    print('--- 上午起量')

    # 配置
    step_1_final_cost_goal = int(target_config.STEP_1_TARGET * scale_ratio)
    adgroup_follow_cost_limit = step_1_final_cost_goal * control_config.STEP_1_COST_LIMIT_SCALE_FOR_MORNING
    step_2_final_cost_goal = int(target_config.STEP_2_TARGET * scale_ratio)
    adgroup_register_cost_limit = step_2_final_cost_goal * control_config.STEP_2_COST_LIMIT_SCALE_FOR_MORNING
    step_3_final_cost_goal = int(target_config.STEP_3_TARGET * scale_ratio)
    adgroup_reservation_cost_limit = step_3_final_cost_goal * control_config.STEP_3_COST_LIMIT_SCALE_FOR_MORNING

    print('--- 目标关注成本： ', step_1_final_cost_goal)
    print('--- 目标注册成本： ', step_2_final_cost_goal)
    print('--- 目标表单预约成本： ', step_3_final_cost_goal)

    print('--- 关注成本限制： ', adgroup_follow_cost_limit)
    print('--- 目标注册成本限制： ', adgroup_register_cost_limit)
    print('--- 目标表单预约成本限制： ', adgroup_reservation_cost_limit)

    today_cost = data_header.JOIN_CHAR.join([data_header.TODAY_PREFIX, data_header.COST_POSTFIX])

    today_step_1_uv = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_1_STAT_NAME, data_header.UV_POSTFIX])
    today_step_2_uv = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_2_STAT_NAME, data_header.UV_POSTFIX])
    today_step_3_uv = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_3_STAT_NAME, data_header.UV_POSTFIX])

    res = {}
    adgroup_report_data = json.load(open(path_config.ADGROUP_REPORT_PATH, encoding='utf8'))
    for account_id in [select_account_id]:
        print('-----------------------------')
        print('account_id: ', account_id)

        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        up_num = control_config.UP_ADGROUP_NUM_FOR_MORNING  # 所有账号只起量一个广告
        up_new_adgroup_num =  control_config.UP_NEW_ADGROUP_NUM # 起量新广告数

        print('--- 最大一键起量数:', up_num)
        print('--- 最大从未起量广告的一键起量数:', up_new_adgroup_num)

        select_up_adgroup_ids = []
        final_select_adgroup_ids = []
        not_select_up_adgroup_ids = []
        active_num = 0
        candidates = {'adgroup_id': [], 'rank': [], 'created_time': [], 'today_cost': [], today_step_1_uv: [],
                      today_step_2_uv: [], today_step_3_uv: [], 'msg': []}
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
                print('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in account_config.ACTIVE_STATUS:
                print('--- 不在投放状态, 不起量...')
                continue
            adgroup_name = data['adgroup_name']
            if adgroup_name[-2:] == 'ZD':
                print('--- 最大转化成本广告, 不起量...')
                continue
            active_num += 1
            ds_list = data['report_data']['ds']
            # if len(ds_list) == 0: # 没有历史数据
            #     continue
            if data[today_step_3_uv] >= 1 and data[today_step_2_uv] >= 2:
                rank = 1
                msg = '开启一键起量： 当天目前表单数 %d >= 1, 并且当天注册数 %d >=2' % \
                      (data[today_step_3_uv], data[today_step_2_uv])
                print(adgroup_id, msg)
            else:
                if account_id in account_config.MEDIAN_ACCOUNTS + account_config.NEW_ACCOUNTS:
                    if data[today_step_2_uv] >= 1 or data[today_step_1_uv] >= 2:
                        rank = 2
                        msg = '开启一键起量： 当天目前关注数 %d >= 2, 或者当天目前注册数 %d >= 1' % \
                              (data[today_step_1_uv], data[today_step_2_uv])
                        print(adgroup_id, msg)
                    elif data[today_cost] >= control_config.COST_THRESHOLD and data[data_header.UP_TREND] == 1:
                        rank = 3
                        msg = '开启一键起量： 当前消耗 %.2f > 700, 且成上升趋势' % \
                              (data[today_cost])
                        print(adgroup_id, msg)
                    elif data[data_header.LIFE_LONG_DAYS] <= 3 and data[data_header.IS_EVER_UP] == 0:
                        rank = 4
                        msg = '上线3天内新广告, 并且没有起过量'
                        print(adgroup_id, msg)
                    else:
                        rank = 0  # 待选
                else:
                    rank = 0

            if rank != 0:
                candidates['adgroup_id'].append(adgroup_id)
                candidates['rank'].append(rank)
                candidates['created_time'].append(data['created_time'])
                candidates[today_cost].append(data[today_cost])
                candidates[today_step_1_uv].append(data[today_step_1_uv])
                candidates[today_step_2_uv].append(data[today_step_2_uv])
                candidates[today_step_3_uv].append(data[today_step_3_uv])
                candidates['msg'].append(msg)

        print('--- active num: ', active_num)

        candidates = pd.DataFrame(candidates)
        print(candidates)

        if account_id in account_config.OLD_ACCOUNTS:
            up_budget = control_config.ACQUISITION_BUDGET_FOR_OLD
        elif account_id in account_config.MEDIAN_ACCOUNTS:
            up_budget = control_config.ACQUISITION_BUDGET_FOR_MED
        else:
            up_budget = control_config.ACQUISITION_BUDGET_FOR_NEW
        print('--- 起量预算: ', up_budget)

        # 已经有表单
        temp = candidates[candidates['rank'] == 1]
        if len(temp):
            print('--- 有表单广告 ...')
            temp = temp.sort_values(today_step_3_uv, ascending=False)
            print(temp)
            for i in range(len(temp)):
                select_up_adgroup_ids.append(
                    ['startAutoAcquisition', account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i], up_budget])
                print(temp['adgroup_id'].iloc[i])
                final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                if len(select_up_adgroup_ids) == up_num:
                    break

        if len(select_up_adgroup_ids) < up_num:
            temp = candidates[candidates['rank'] == 2]
            if len(temp):
                print('--- 有注册广告 ...')
                temp = temp.sort_values([today_step_2_uv, today_step_1_uv], ascending=False)
                print(temp)
                for i in range(len(temp)):
                    select_up_adgroup_ids.append(
                        ['startAutoAcquisition', account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i],
                         up_budget])
                    print(temp['adgroup_id'].iloc[i])
                    final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                    break  # 仅加入一个
                    # if len(select_up_adgroup_ids) == up_num:
                    #     break

        if len(select_up_adgroup_ids) < up_num:
            temp = candidates[candidates['rank'] == 3]
            if len(temp):
                print('--- 有消耗上升趋势广告 ...')
                temp = temp.sort_values('today_cost', ascending=False)
                print(temp)
                for i in range(len(temp)):
                    select_up_adgroup_ids.append(
                        ['startAutoAcquisition', account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i],
                         up_budget])
                    print(temp['adgroup_id'].iloc[i])
                    final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                    break  # 仅加入一个
                    # if len(select_up_adgroup_ids) == up_num:
                    #     break

        # select_new_adgroup_id = []
        # if len(select_up_adgroup_ids) < up_num:
        #     temp = candidates[candidates['rank'] == 4]
        #     if len(temp):
        #         print ('--- 新创建广告, 且没有起过量 ...')
        #         temp = temp.sort_values(['created_time'], ascending=False)
        #         print (temp)
        #         for i in range(len(temp)):
        #             select_up_adgroup_ids.append(['startAutoAcquisition',account_id, temp['adgroup_id'].iloc[i], \
        #                                         '开启一键起量：最新广告, 创建时间%s' % temp['created_time'].iloc[i], up_budget])
        #             select_new_adgroup_id.append(candidates['adgroup_id'].iloc[i])
        #             print (candidates['adgroup_id'].iloc[i], '开启一键起量：最新广告, 创建时间%s' % candidates['created_time'].iloc[i])
        #             if len(select_up_adgroup_ids) == up_num or len(select_new_adgroup_id) == up_new_adgroup_num:
        #                 break
        print('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print(d)

        res[account_id] = select_up_adgroup_ids
        # return select_up_adgroup_ids
    return res


def get_up_adgroup_ids_for_all_afternoon(select_account_id, adgroup_status, control_config, account_config,
                                         target_config, path_config, data_header, scale_ratio=1):  # 方案1

    print('--------------------------------------')
    print('--- 下午起量')

    # 配置
    step_1_final_cost_goal = int(target_config.STEP_1_TARGET * scale_ratio)
    adgroup_follow_cost_limit = step_1_final_cost_goal * control_config.STEP_1_COST_LIMIT_SCALE_FOR_AFTERNOON
    step_2_final_cost_goal = int(target_config.STEP_2_TARGET * scale_ratio)
    adgroup_register_cost_limit = step_2_final_cost_goal * control_config.STEP_2_COST_LIMIT_SCALE_FOR_AFTERNOON
    step_3_final_cost_goal = int(target_config.STEP_3_TARGET * scale_ratio)
    adgroup_reservation_cost_limit = step_3_final_cost_goal * control_config.STEP_3_COST_LIMIT_SCALE_FOR_AFTERNOON

    print('--- 目标关注成本： ', step_1_final_cost_goal)
    print('--- 目标注册成本： ', step_2_final_cost_goal)
    print('--- 目标表单预约成本： ', step_3_final_cost_goal)

    print('--- 关注成本限制： ', adgroup_follow_cost_limit)
    print('--- 目标注册成本限制： ', adgroup_register_cost_limit)
    print('--- 目标表单预约成本限制： ', adgroup_reservation_cost_limit)

    past_1day_step_3_uv = data_header.JOIN_CHAR.join(
        [data_header.PAST_1_DAYS_PREFIX, data_header.STEP_3_STAT_NAME, data_header.UV_POSTFIX])
    today_step_2_uv = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_2_STAT_NAME, data_header.UV_POSTFIX])
    today_step_3_uv = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_3_STAT_NAME, data_header.UV_POSTFIX])

    res = {}
    adgroup_report_data = json.load(open(path_config.ADGROUP_REPORT_PATH, encoding='utf8'))
    for account_id in [select_account_id]:

        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        up_num = control_config.UP_ADGROUP_NUM_FOR_AFTERNOON  # 所有账号只起量一个广告

        print('--- 下午最大一键起量数:', up_num)

        select_up_adgroup_ids = []
        final_select_adgroup_ids = []
        # not_select_up_adgroup_ids = []
        active_num = 0
        candidates = {'adgroup_id': [], 'rank': [], 'created_time': [], past_1day_step_3_uv: [],
                      today_step_3_uv: [], today_step_2_uv: [], 'msg': []}
        for adgroup_id in adgroup_ids:

            if adgroup_id not in adgroup_status:
                print('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
                print('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in account_config.ACTIVE_STATUS:
                print('--- 不在投放状态, 不起量...')
                continue
            adgroup_name = data['adgroup_name']
            if adgroup_name[-2:] == 'ZD':
                print('--- 最大转化成本广告, 不起量...')
                continue
            active_num += 1
            ds_list = data['report_data']['ds']
            # if len(ds_list) == 0: # 没有历史数据
            #     continue

            if data[today_step_3_uv] >= 1:
                rank = 1
                msg = '开启一键起量： 当天表单数 %d >= 1' % \
                      (data[today_step_3_uv])
                print(adgroup_id, msg)
            elif past_1day_step_3_uv in data.keys() and data[past_1day_step_3_uv] >= 1 \
                    and data[past_1day_step_3_uv] < step_3_final_cost_goal * 1.2:
                rank = 2
                msg = '开启一键起量： 前一天表单数 %d >= 1' % \
                      (data[past_1day_step_3_uv])
                print(adgroup_id, msg)
            elif data[today_step_2_uv] >= 1:
                rank = 3
                msg = '开启一键起量： 当天目前注册数 %d >= 1' % \
                      (data[today_step_2_uv])
                print(adgroup_id, msg)
            else:
                rank = 0  # 待选

            if rank != 0:
                candidates['adgroup_id'].append(adgroup_id)
                candidates['rank'].append(rank)
                candidates['created_time'].append(data['created_time'])
                if past_1day_step_3_uv in data.keys():
                    candidates[past_1day_step_3_uv].append(data[past_1day_step_3_uv])
                else:
                    candidates[past_1day_step_3_uv].append(0)
                candidates[today_step_3_uv].append(data[today_step_3_uv])
                candidates[today_step_2_uv].append(data[today_step_2_uv])
                candidates['msg'].append(msg)

        print('--- active num: ', active_num)

        candidates = pd.DataFrame(candidates)
        print(candidates)

        if account_id in account_config.OLD_ACCOUNTS:
            up_budget = control_config.ACQUISITION_BUDGET_FOR_OLD
        elif account_id in account_config.MEDIAN_ACCOUNTS:
            up_budget = control_config.ACQUISITION_BUDGET_FOR_MED
        else:
            up_budget = control_config.ACQUISITION_BUDGET_FOR_NEW
        print('--- 起量预算: ', up_budget)

        # 已经有表单
        temp = candidates[candidates['rank'] == 1]
        if len(temp):
            print('--- 有表单广告 ...')
            temp = temp.sort_values(today_step_3_uv, ascending=False)
            print(temp)
            for i in range(len(temp)):
                select_up_adgroup_ids.append(
                    ['startAutoAcquisition', account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i], up_budget])
                print(temp['adgroup_id'].iloc[i])
                final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                if len(select_up_adgroup_ids) == up_num:
                    break

        if len(select_up_adgroup_ids) < up_num:
            temp = candidates[candidates['rank'] == 2]
            if len(temp):
                print('--- 前一天有表单广告 ...')
                temp = temp.sort_values(past_1day_step_3_uv, ascending=False)
                print(temp)
                for i in range(len(temp)):
                    select_up_adgroup_ids.append(
                        ['startAutoAcquisition', account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i],
                         up_budget])
                    print(temp['adgroup_id'].iloc[i])
                    final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                    if len(select_up_adgroup_ids) == up_num:
                        break

        if len(select_up_adgroup_ids) < up_num:
            temp = candidates[candidates['rank'] == 3]
            if len(temp):
                print('--- 有注册广告 ...')
                temp = temp.sort_values(today_step_2_uv, ascending=False)
                print(temp)
                for i in range(len(temp)):
                    select_up_adgroup_ids.append(
                        ['startAutoAcquisition', account_id, temp['adgroup_id'].iloc[i], temp['msg'].iloc[i],
                         up_budget])
                    print(temp['adgroup_id'].iloc[i])
                    final_select_adgroup_ids.append(temp['adgroup_id'].iloc[i])
                    if len(select_up_adgroup_ids) == up_num:
                        break

        print('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print(d)

        res[account_id] = select_up_adgroup_ids
        # return select_up_adgroup_ids
    return res


def get_up_adgroup_ids_for_all_by_model(select_account_id, adgroup_status, control_config, account_config,
                                        target_config, path_config, data_header, scale_ratio=2):  # 模型预测

    global predict_data, predict_model

    # 配置
    final_cost_goal = int(target_config.YEAR_TARGET * scale_ratio)  # 440
    # adgroup_reservation_cost_limit = final_cost_goal * 1.2  # 660 #550 #int(final_cost_goal * 1.6) # 704
    adgroup_reservation_cost_limit = final_cost_goal * 1.2  # Use hardcoded value since FINAL_COST_LIMIT_SCALE not in control_config
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 3
    up_new_adgroup_num = control_config.UP_NEW_ADGROUP_NUM  # 最多选择起量的从未起量广告数

    print('---- 模型起量')
    print('--- 基于历史数据, 寻找可以一键起量的广告 ...')
    print('--- final_cost_goal: ', final_cost_goal)
    print('--- adgroup_reservation_cost_limit: ', adgroup_reservation_cost_limit)
    res = {}
    adgroup_report_data = json.load(open('adgroup_report_data.json', encoding='utf8'))
    # model_predict = json.load(open('test_pred_new.json', encoding='utf8'))
    now = datetime.datetime.now()
    day = str(now).split(' ')[0]
    hour = int(str(now).split(' ')[1].split(':')[0])
    week_day = get_weekday(day)
    print('--- week_day: ', week_day)
    for account_id in [select_account_id]:
        print('-----------------------------')
        print('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()

        up_num = control_config.UP_ADGROUP_NUM_FOR_MODEL  # up_all_num_for_new # 所有账号只起量一个广告

        print('--- 最大一键起量数:', up_num)
        print('--- 最大从未起量广告的一键起量数:', up_new_adgroup_num)

        select_up_adgroup_ids = []
        final_select_adgroup_ids = []
        not_select_up_adgroup_ids = []
        active_num = 0
        candidates = {'adgroup_id': [], 'created_time': [], 'model_predict_score': [], 'msg': []}
        # best_threshold = open('best_threshold_new.txt', encoding='utf8').readlines()
        # best_threshold = float(best_threshold[0].strip())
        # print ('--- best_threshold: ', best_threshold)
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            # if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == True:
            #     print ('--- %s 当天起量过, 不操作 ...' % str(adgroup_id))
            #     continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']:
                continue
            adgroup_name = data['adgroup_name']
            if adgroup_name[-2:] == 'ZD':
                print('--- %s 最大转化成本广告, 不起量...' % str(adgroup_id), adgroup_name)
                continue
            active_num += 1
            # ds_list = data['report_data']['ds']
            # if len(ds_list) == 0: # 没有历史数据
            #     continue

            adgroup_predict_data = predict_data[(predict_data['account_id'] == int(account_id)) & \
                                                (predict_data['adgroup_id'] == int(adgroup_id)) & \
                                                (predict_data['ds'] == day) & \
                                                (predict_data['hour'] == hour)]

            if len(adgroup_predict_data) == 0:
                print('--- %s 没有预测数据...' % str(adgroup_id))
                continue

            prefix = 'prev_hours_cur_day'
            adgroup_predict_data[data_header.JOIN_CHAR.join([prefix, data_header.COST_POSTFIX])].iloc[0] = data[
                data_header.JOIN_CHAR.join([data_header.TODAY_PREFIX, data_header.COST_POSTFIX])]
            for name in data_header.STEP_1_STAT_NAME, data_header.STEP_2_STAT_NAME, data_header.STEP_3_STAT_NAME:
                adgroup_predict_data[data_header.JOIN_CHAR.join([prefix, name, data_header.UV_POSTFIX])] = data[
                    data_header.JOIN_CHAR.join([data_header.TODAY_PREFIX, name, data_header.UV_POSTFIX])]
                adgroup_predict_data[data_header.JOIN_CHAR.join([prefix, name, data_header.COST_POSTFIX])] = data[
                    data_header.JOIN_CHAR.join([data_header.TODAY_PREFIX, name, data_header.COST_POSTFIX])]

            """
            adgroup_predict_data['prev_hours_cur_day_cost'].iloc[0] = data['today_cost']
            adgroup_predict_data['prev_hours_cur_day_biz_follow_uv'].iloc[0] = data['today_biz_follow_uv']
            adgroup_predict_data['prev_hours_cur_day_biz_follow_cost'].iloc[0] = data['today_biz_follow_cost']
            adgroup_predict_data['prev_hours_cur_day_biz_reg_uv'].iloc[0] = data['today_biz_reg_uv']
            adgroup_predict_data['prev_hours_cur_day_biz_reg_cost'].iloc[0] = data['today_biz_reg_cost']
            adgroup_predict_data['prev_hours_cur_day_reservation_uv'].iloc[0] = data['today_reservation_uv']
            adgroup_predict_data['prev_hours_cur_day_reservation_cost'].iloc[0] = data['today_reservation_cost']
            """

            print(adgroup_predict_data[adgroup_predict_data.columns])
            adgroup_predict_data = adgroup_predict_data.drop(['account_id', 'adgroup_id', 'ds'], axis=1)

            if len(adgroup_predict_data) == 0:
                print('--- no adgroup predict data ...')
                continue
            # print (adgroup_predict_data)
            model_predict_score = predict_model.predict_proba(adgroup_predict_data)[:, 1]
            print('--- ', account_id, adgroup_id, model_predict_score)

        res[account_id] = select_up_adgroup_ids
        # return select_up_adgroup_ids
    return res


def check_if_reup_again(select_account_id, adgroup_status, control_config, account_config,
                        target_config, path_config, data_header, scale_ratio=2):  # 连续起量
    """
    # 配置
    step_1_final_cost_goal = int(TargetConfig.STEP_1_TARGET * scale_ratio)
    step_2_final_cost_goal = int(TargetConfig.STEP_2_TARGET * scale_ratio)
    step_3_final_cost_goal = int(TargetConfig.STEP_3_TARGET * scale_ratio)
    """
      

    print('--- 基于当天第一次起量数据, 决定是否第二次一键起量 ...')
    today_step_3_uv = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_2_STAT_NAME, data_header.UV_POSTFIX])
    adgroup_report_data = json.load(open(path_config.ADGROUP_REPORT_PATH, encoding='utf8'))
    res = {}
    for account_id in [select_account_id]:
        print('-----------------------------')
        print('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()
        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['todayAcquisitionStatus'] == False:
                # print ('--- %s 当天没有起量过, 不操作 ...' % str(adgroup_id))
                continue
            data = adgroup_data[adgroup_id]
            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in account_config.ACTIVE_STATUS:  # 不在投放中
                # print ('--- %s 不在投放中, 不操作 ...' % str(adgroup_id))
                continue
            autoAcquisitionStatus = adgroup_status[adgroup_id]['autoAcquisitionStatus']  # 起量状态
            # if autoAcquisitionStatus == 'AUTO_ACQUISTION_STATUS_PENDING':  # 在起量中
            if autoAcquisitionStatus == account_config.AUTO_ACQUISITION_PENDING_STATUS:  # 在起量中
                # print ('--- %s 在起量中, 不操作 ...' % str(adgroup_id))
                continue
            print('--- %s %s' % (adgroup_id, autoAcquisitionStatus))
            if data[today_step_3_uv] > 2:
                msg = '--- 第二次起量：当天表单数 %d > 2' % (data[today_step_3_uv])
                print(msg)
                select_up_adgroup_ids.append(['startAutoAcquisition', account_id, adgroup_id, msg, 5000000])

        print('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print(d)
        res[account_id] = select_up_adgroup_ids
    return res


def check_if_end_up_per_10mins(select_account_id, adgroup_status, control_config, account_config,
                               target_config, path_config, data_header, scale_ratio=2):
    # 配置

    final_cost_goal = int(target_config.YEAR_TARGET * scale_ratio)  # 440
    adgroup_reservation_cost_limit = final_cost_goal * 1.2  # Use hardcoded value since FINAL_COST_LIMIT_SCALE not in control_config
    adgroup_up_cost_limit = adgroup_reservation_cost_limit * 2.1

    print('--- 起量成本限制：', adgroup_up_cost_limit)

    print('--- 基于当天一键起量跑量数据, 决定是否暂停一键起量 ...')
    print('--- 成本限制: ', adgroup_up_cost_limit)
    today_step_3_uv = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_3_STAT_NAME, data_header.UV_POSTFIX])
    today_step_3_cost = data_header.JOIN_CHAR.join(
        [data_header.TODAY_PREFIX, data_header.STEP_3_STAT_NAME, data_header.COST_POSTFIX])
    today_cost = data_header.JOIN_CHAR.join([data_header.TODAY_PREFIX, data_header.COST_POSTFIX])
    res = {}
    adgroup_report_data = json.load(open(path_config.ADGROUP_REPORT_PATH, encoding='utf8'))
    for account_id in [select_account_id]:
        print('-----------------------------')
        print('account_id: ', account_id)
        adgroup_data = adgroup_report_data[account_id]
        adgroup_ids = adgroup_data.keys()

        select_up_adgroup_ids = []
        for adgroup_id in adgroup_ids:
            if adgroup_id not in adgroup_status:
                # print ('--- %s adgroup status not found ...' % str(adgroup_id))
                continue
            if adgroup_status[adgroup_id]['autoAcquisitionStatus'] != account_config.AUTO_ACQUISITION_PENDING_STATUS:
                # print ('--- %s 不在起量中, 不操作 ...' % str(adgroup_id))
                continue

            system_status = adgroup_status[adgroup_id]['systemStatus']
            if system_status not in account_config.ACTIVE_STATUS:
                # print ('--- %s 不投放中, 不操作 ...' % str(adgroup_id))
                continue

            data = adgroup_data[adgroup_id]
            # 第一天不操作
            # ds_list = data['report_data']['ds']
            # if len(ds_list) == 0: # 没有历史数据
            #     print ('--- %s 没有历史数据, 不操作 ...' % str(adgroup_id))
            #     continue
            end_up = 1
            # if str(adgroup_id) == str(***********): # 指定某一个广告暂停起量
            #     end_up = 1
            print('--- ', adgroup_id, data[today_step_3_uv], data[today_cost], data[today_step_3_cost])
            msg = '全部暂停一键起量'
            print(msg)
            # if data['today_reservation_uv'] == 0 and data['today_cost'] > adgroup_up_cost_limit:
            #     end_up = 1
            #     msg = '暂停一键起量： 当天转化数为0, 当天消耗 %.2f >%d' % (data['today_cost'], adgroup_up_cost_limit)
            #     print (adgroup_id, msg)
            # elif data['today_reservation_uv'] > 0 and data['today_reservation_cost'] > adgroup_up_cost_limit:
            #     end_up = 1
            #     msg = '暂停一键起量： 当天转化数 %d > 0, 当天转化成本 %.2f >%d' % (data['today_reservation_uv'], data['today_reservation_cost'], adgroup_up_cost_limit)
            #     print (adgroup_id, msg)
            if end_up == 1:
                select_up_adgroup_ids.append(['stopAutoAcquisition', account_id, adgroup_id, msg])
        print('--- select adgroup_ids: ')
        for d in select_up_adgroup_ids:
            print(d)
        res[account_id] = select_up_adgroup_ids
    return res

