import urllib.request
import numpy as np
import cv2
import calendar
import random
from config.config import GeneralConfig

def url_to_image(url):
    resp = urllib.request.urlopen(url)
    image = np.asarray(bytearray(resp.read()), dtype = "uint8")
    image = cv2.imdecode(image, cv2.IMREAD_COLOR)
    return image
    
def get_weekday(date_string):
    """
    weekday_dict = {
        0: "星期一",
        1: "星期二",
        2: "星期三",
        3: "星期四",
        4: "星期五",
        5: "星期六",
        6: "星期日"
    }
    """
    weekday_dict = GeneralConfig.WEEKDAY_DICT
    weekday_dict = {str(k): v for k, v in weekday_dict.items()} # Ensure keys are string
    date = calendar.weekday(int(date_string.split("-")[0]), int(date_string.split("-")[1]), int(date_string.split("-")[2]))
    return weekday_dict[str(date)]  # Convert integer to string for dictionary lookup



def parse_request_data(request_data_dict, account_config, account_report_data_header, adgroup_report_data_header):
    """
    Parse the request data to extract account IDs and their corresponding values.
    :param request_data: request_data in dict format from json string
    :return: A dictionary with account IDs as keys and their corresponding values.
    """
    # task = request_data_dict['task']
    account_id = str(request_data_dict['accountId'])
    adgroup_list = request_data_dict['adgroupList']
    # print ('--- task: ', task)
    print('--- account_id: ', account_id)

    # 大盘和账号级别数据
    account_status = {}
    account_status['past7DaysAccountAssessCost'] = request_data_dict['past7DaysAccountAssessCost']
    account_status['goalBidPrice'] = request_data_dict['goalBidPrice']
    account_status['accountDailyBudget'] = request_data_dict['accountDailyBudget']
    account_status['past3DaysReservationCost'] = request_data_dict['past3DaysReservationCost']
    account_status[account_report_data_header.COST_RATE] = request_data_dict[account_report_data_header.COST_RATE]  # 费率
    account_status[account_report_data_header.ASSESS_COST] = request_data_dict[account_report_data_header.ASSESS_COST]  # 进件成本
    account_status[account_report_data_header.ASSESS_COST_RATE] = request_data_dict[account_report_data_header.ASSESS_COST_RATE]  # 进件费率

    adgroup_status = {}
    uping_num = 0
    up_already_num = 0
    if len(adgroup_list) > 0:
        for adgroup in adgroup_list:  # 广告组级别数据
            adgroup_id = str(adgroup['adgroupId'])
            adgroup_status[adgroup_id] = {}
            adgroup_status[adgroup_id]['todayAcquisitionStatus'] = adgroup['todayAcquisitionStatus']
            adgroup_status[adgroup_id]['autoAcquisitionStatus'] = adgroup['autoAcquisitionStatus']
            adgroup_status[adgroup_id]['systemStatus'] = adgroup['systemStatus']

            # 费率
            adgroup_status[adgroup_id]['past3DaysD7CostRate'] = adgroup['past3DaysD7CostRate']
            adgroup_status[adgroup_id]['past5DaysD7CostRate'] = adgroup['past5DaysD7CostRate']
            adgroup_status[adgroup_id]['past7DaysD7CostRate'] = adgroup['past7DaysD7CostRate']
            adgroup_status[adgroup_id]['past2WeeksD7CostRate'] = adgroup['past2WeeksD7CostRate']

            # 进件成本：后效企业进件个数和成本
            adgroup_status[adgroup_id]['past3DaysD7ReservationUv'] = adgroup['past3DaysD7ReservationUv']
            adgroup_status[adgroup_id]['past5DaysD7ReservationUv'] = adgroup['past5DaysD7ReservationUv']
            adgroup_status[adgroup_id]['past7DaysD7ReservationUv'] = adgroup['past7DaysD7ReservationUv']
            adgroup_status[adgroup_id]['past2WeeksD7ReservationUv'] = adgroup['past2WeeksD7ReservationUv']
            adgroup_status[adgroup_id]['past3DaysD7ReservationCost'] = adgroup['past3DaysD7ReservationCost']
            adgroup_status[adgroup_id]['past5DaysD7ReservationCost'] = adgroup['past5DaysD7ReservationCost']
            adgroup_status[adgroup_id]['past7DaysD7ReservationCost'] = adgroup['past7DaysD7ReservationCost']
            adgroup_status[adgroup_id]['past2WeeksD7ReservationCost'] = adgroup['past2WeeksD7ReservationCost']

            # 授信个数
            adgroup_status[adgroup_id]['past3DaysD7ShouxinUv'] = adgroup['past3DaysD7ShouxinUv']
            adgroup_status[adgroup_id]['past5DaysD7ShouxinUv'] = adgroup['past5DaysD7ShouxinUv']
            adgroup_status[adgroup_id]['past7DaysD7ShouxinUv'] = adgroup['past7DaysD7ShouxinUv']
            adgroup_status[adgroup_id]['past2WeeksD7ShouxinUv'] = adgroup['past2WeeksD7ShouxinUv']
            adgroup_status[adgroup_id]['past3DaysD7ShouxinCost'] = adgroup['past3DaysD7ShouxinCost']
            adgroup_status[adgroup_id]['past5DaysD7ShouxinCost'] = adgroup['past5DaysD7ShouxinCost']
            adgroup_status[adgroup_id]['past7DaysD7ShouxinCost'] = adgroup['past7DaysD7ShouxinCost']
            adgroup_status[adgroup_id]['past2WeeksD7ShouxinCost'] = adgroup['past2WeeksD7ShouxinCost']

            # 异常状态
            adgroup_status[adgroup_id]['reservationState'] = adgroup['reservationState']
            adgroup_status[adgroup_id]['backendState'] = adgroup['backendState']
            adgroup_status[adgroup_id]['costState'] = adgroup['costState']
            adgroup_status[adgroup_id]['fluctuateDays'] = adgroup['fluctuateDays']
            adgroup_status[adgroup_id]['past5RateFluctuate'] = adgroup['past5RateFluctuate']
            adgroup_status[adgroup_id]['past5CostFluctuate'] = adgroup['past5CostFluctuate']
            # adgroup_status[adgroup_id]['time_series'] = adgroup['time_series']

            if adgroup['todayAcquisitionStatus']:
                up_already_num += 1
            if adgroup['autoAcquisitionStatus'] == account_config.AUTO_ACQUISITION_PENDING_STATUS:
                uping_num += 1
    return adgroup_status, account_status, account_id, up_already_num, uping_num


def load_adgroup_report():
    import json
    from config.config import PathConfig
    return json.load(open(PathConfig.ADGROUP_REPORT_PATH, encoding='utf8'))
