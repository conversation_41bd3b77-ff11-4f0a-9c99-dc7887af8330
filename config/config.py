# -*- coding: utf-8 -*-

"""
Layered Configuration System

This module provides account-type-specific configuration classes that automatically
select the appropriate configuration layer based on account ID.

Usage:
    # Natural usage - automatically selects the right config based on account_id
    budget_config = get_budget_control_config(account_id)
    
    # Or use the layered classes directly
    old_budget = BudgetControlConfigOld()
    med_budget = BudgetControlConfigMed()
    new_budget = BudgetControlConfigNew()
"""

import json

# ============================================================================
# General Configuration Classes
# ============================================================================

from config.general_config import TargetConfig, GeneralConfig, AccountConfig, APIConfig, PathConfig, AccountReportDataHeader, AdGroupReportDataHeader, StringFormat, CONFIG_JSON_PATH

# __all__ = ['TargetConfig', 'GeneralConfig', 'AccountConfig', 'APIConfig', 'PathConfig', 'AccountReportDataHeader', 'AdGroupReportDataHeader', 'StringFormat']

# ============================================================================
# Layered Configuration Classes
# ============================================================================

class BudgetControlConfigBase:
    """Base class for budget control configuration"""
    
    # Default values
    CHECK_PRE_THRESHOLD_RATIO = 0.5
    STEP_1_COST_LIMIT_SCALE = 2
    STEP_2_COST_LIMIT_SCALE = 2
    STEP_3_COST_LIMIT_SCALE = 1
    USE_HOUXIAO_STOP = 0
    POST_EFFECT_STEP_3_NUM_LIMIT = 10
    COST_RATE_SCALE = 2
    MORNING_SCALE = 1.0
    AFTERNOON_SCALE = 1.0
    NIGHT_SCALE = 1.0
    
    @classmethod
    def load_from_json(cls, config_key):
        """Load configuration from JSON file using the specified config key"""
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                budget_config = config_data.get(config_key, {})
                
                cls.CHECK_PRE_THRESHOLD_RATIO = budget_config.get('check_pre_threshold_ratio', cls.CHECK_PRE_THRESHOLD_RATIO)
                cls.STEP_1_COST_LIMIT_SCALE = budget_config.get('step_1_cost_limit_scale', cls.STEP_1_COST_LIMIT_SCALE)
                cls.STEP_2_COST_LIMIT_SCALE = budget_config.get('step_2_cost_limit_scale', cls.STEP_2_COST_LIMIT_SCALE)
                cls.STEP_3_COST_LIMIT_SCALE = budget_config.get('step_3_cost_limit_scale', cls.STEP_3_COST_LIMIT_SCALE)
                cls.USE_HOUXIAO_STOP = budget_config.get('use_houxiao_stop', cls.USE_HOUXIAO_STOP)
                cls.POST_EFFECT_STEP_3_NUM_LIMIT = budget_config.get('post_effect_step_3_num_limit', cls.POST_EFFECT_STEP_3_NUM_LIMIT)
                cls.COST_RATE_SCALE = budget_config.get('cost_rate_scale', cls.COST_RATE_SCALE)
                cls.MORNING_SCALE = budget_config.get('morning_scale', cls.MORNING_SCALE)
                cls.AFTERNOON_SCALE = budget_config.get('afternoon_scale', cls.AFTERNOON_SCALE)
                cls.NIGHT_SCALE = budget_config.get('night_scale', cls.NIGHT_SCALE)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load {config_key} from {CONFIG_JSON_PATH}: {e}")
            print(f"Using default values for {cls.__name__}")


class BudgetControlConfigOld(BudgetControlConfigBase):
    """Budget control configuration for OLD accounts"""
    
    @classmethod
    def load_from_json(cls):
        super().load_from_json('budget_control_config_old')


class BudgetControlConfigMed(BudgetControlConfigBase):
    """Budget control configuration for MEDIAN accounts"""
    
    @classmethod
    def load_from_json(cls):
        super().load_from_json('budget_control_config_med')


class BudgetControlConfigNew(BudgetControlConfigBase):
    """Budget control configuration for NEW accounts"""
    
    @classmethod
    def load_from_json(cls):
        super().load_from_json('budget_control_config_new')


class AcquisitionControlConfigBase:
    """Base class for acquisition control configuration"""
    
    # Default values
    RANK_1_LIMIT_FOR_MORNING = {"today_reservation_uv": 1, "today_biz_reg_uv": 1}
    RANK_2_LIMIT_FOR_MORNING = {"today_biz_follow_uv": 2, "today_biz_reg_uv": 1}
    RANK_3_LIMIT_FOR_MORNING = {"today_cost": 700, "up_trend": 1}
    RANK_4_LIMIT_FOR_MORNING = {"life_long_days": 3, "is_ever_up": 0}
    RANK_1_LIMIT_FOR_AFTERNOON = {"today_reservation_uv": 1}
    RANK_2_LIMIT_FOR_AFTERNOON = {"past_1day_reservation_uv": 1, "past_1day_reservation_cost": 1.2}
    RANK_3_LIMIT_FOR_AFTERNOON = {"today_biz_reg_uv": 1}
    UP_ADGROUP_NUM_FOR_MORNING = 1 # 所有账号只起量一个广告
    UP_ADGROUP_NUM_FOR_AFTERNOON = 1 # 所有账号只起量一个广告
    UP_ADGROUP_NUM_FOR_MODEL = 2 
    UP_NEW_ADGROUP_NUM = 1 # 起量新广告数
    UP_ALL_NUM = 1
    MORNING_HOURS = [9, 10]
    AFTERNOON_HOURS = [14, 15]
    NO_UP_DAY_OF_WEEK = ['星期六', '星期日']
    REUP_HOURS = [12, 13, 14, 15]
    COST_THRESHOLD = 700

    ACQUISITION_BUDGET = 3000000
    ACQUISITION_BUDGET_FOR_REUP = 5000000
    FINAL_COST_LIMIT_SCALE = 1.2
    STEP_3_COST_LIMIT_SCALE = 2.1
    STEP_3_LIMIT_FOR_REUP = 2
    
    @classmethod
    def load_from_json(cls, config_key):
        """Load configuration from JSON file using the specified config key"""
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                acq_config = config_data.get(config_key, {})
                
                cls.RANK_1_LIMIT_FOR_MORNING = acq_config.get('rank_1_limit_for_morning', cls.RANK_1_LIMIT_FOR_MORNING)
                cls.RANK_2_LIMIT_FOR_MORNING = acq_config.get('rank_2_limit_for_morning', cls.RANK_2_LIMIT_FOR_MORNING)
                cls.RANK_3_LIMIT_FOR_MORNING = acq_config.get('rank_3_limit_for_morning', cls.RANK_3_LIMIT_FOR_MORNING)
                cls.RANK_4_LIMIT_FOR_MORNING = acq_config.get('rank_4_limit_for_morning', cls.RANK_4_LIMIT_FOR_MORNING)
                cls.RANK_1_LIMIT_FOR_AFTERNOON = acq_config.get('rank_1_limit_for_afternoon', cls.RANK_1_LIMIT_FOR_AFTERNOON)
                cls.RANK_2_LIMIT_FOR_AFTERNOON = acq_config.get('rank_2_limit_for_afternoon', cls.RANK_2_LIMIT_FOR_AFTERNOON)
                cls.RANK_3_LIMIT_FOR_AFTERNOON = acq_config.get('rank_3_limit_for_afternoon', cls.RANK_3_LIMIT_FOR_AFTERNOON)
                cls.UP_ADGROUP_NUM_FOR_MORNING = acq_config.get('up_adgroup_num_for_morning', cls.UP_ADGROUP_NUM_FOR_MORNING)
                cls.UP_ADGROUP_NUM_FOR_AFTERNOON = acq_config.get('up_adgroup_num_for_afternoon', cls.UP_ADGROUP_NUM_FOR_AFTERNOON)
                cls.UP_ADGROUP_NUM_FOR_MODEL = acq_config.get('up_adgroup_num_for_model', cls.UP_ADGROUP_NUM_FOR_MODEL)
                cls.UP_NEW_ADGROUP_NUM = acq_config.get('up_new_adgroup_num', cls.UP_NEW_ADGROUP_NUM)
                cls.UP_ALL_NUM = acq_config.get('up_all_num', cls.UP_ALL_NUM)
                cls.MORNING_HOURS = acq_config.get('morning_hours', cls.MORNING_HOURS)
                cls.AFTERNOON_HOURS = acq_config.get('afternoon_hours', cls.AFTERNOON_HOURS)
                cls.NO_UP_DAY_OF_WEEK = acq_config.get('no_up_day_of_week', cls.NO_UP_DAY_OF_WEEK)
                cls.REUP_HOURS = acq_config.get('reup_hours', cls.REUP_HOURS)
                cls.COST_THRESHOLD = acq_config.get('cost_threshold', cls.COST_THRESHOLD)
                cls.ACQUISITION_BUDGET = acq_config.get('acquisition_budget', cls.ACQUISITION_BUDGET)
                cls.ACQUISITION_BUDGET_FOR_REUP = acq_config.get('acquisition_budget_for_reup', cls.ACQUISITION_BUDGET_FOR_REUP)
                cls.FINAL_COST_LIMIT_SCALE = acq_config.get('final_cost_limit_scale', cls.FINAL_COST_LIMIT_SCALE)
                cls.STEP_3_COST_LIMIT_SCALE = acq_config.get('step_3_cost_limit_scale', cls.STEP_3_COST_LIMIT_SCALE)
                
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load {config_key} from {CONFIG_JSON_PATH}: {e}")
            print(f"Using default values for {cls.__name__}")


class AcquisitionControlConfigOld(AcquisitionControlConfigBase):
    """Acquisition control configuration for OLD accounts"""
    
    @classmethod
    def load_from_json(cls):
        super().load_from_json('acquisition_control_config_old')


class AcquisitionControlConfigMed(AcquisitionControlConfigBase):
    """Acquisition control configuration for MEDIAN accounts"""
    
    @classmethod
    def load_from_json(cls):
        super().load_from_json('acquisition_control_config_med')


class AcquisitionControlConfigNew(AcquisitionControlConfigBase):
    """Acquisition control configuration for NEW accounts"""
    
    @classmethod
    def load_from_json(cls):
        super().load_from_json('acquisition_control_config_new')


# ============================================================================
# Additional Layered Configuration Classes
# ============================================================================

class CreateConfigBase:
    """Base class for create configuration"""

    # Default values
    NO_CREATE_WEEK_OF_DAY = ["星期二", "星期四", "星期六", "星期日"]
    CREATE_HOUR_FOR_SPECIAL_DAY_OF_WEEK = [6, 7]
    CREATE_HOUR_FOR_WEEKDAY = [14, 15]

    @classmethod
    def load_from_json(cls, config_key):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                create_config = config_data.get(config_key, {})

                cls.NO_CREATE_WEEK_OF_DAY = create_config.get('no_create_week_of_day', cls.NO_CREATE_WEEK_OF_DAY)
                cls.CREATE_HOUR_FOR_SPECIAL_DAY_OF_WEEK = create_config.get('create_hour_for_special_day_of_week', cls.CREATE_HOUR_FOR_SPECIAL_DAY_OF_WEEK)
                cls.CREATE_HOUR_FOR_WEEKDAY = create_config.get('create_hour_for_weekday', cls.CREATE_HOUR_FOR_WEEKDAY)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load {config_key} from {CONFIG_JSON_PATH}: {e}")
            print(f"Using default values for {cls.__name__}")


class CreateConfigOld(CreateConfigBase):
    @classmethod
    def load_from_json(cls):
        super().load_from_json('create_config_old')


class CreateConfigMed(CreateConfigBase):
    @classmethod
    def load_from_json(cls):
        super().load_from_json('create_config_med')


class CreateConfigNew(CreateConfigBase):
    @classmethod
    def load_from_json(cls):
        super().load_from_json('create_config_new')


class RestartControlConfigBase:
    """Base class for restart control configuration"""

    # Default values
    CHECK_RESTART_HOURS = [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]  # [6, 18)
    CHECK_RESTART_INTERVAL = 10  # minutes

    STEP_1_COST_LIMIT_SCALE = 2
    STEP_2_COST_LIMIT_SCALE = 2
    STEP_3_COST_LIMIT_SCALE = 1.3333

    @classmethod
    def load_from_json(cls, config_key):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                restart_config = config_data.get(config_key, {})

                cls.CHECK_RESTART_HOURS = restart_config.get('check_restart_hours', cls.CHECK_RESTART_HOURS)
                cls.CHECK_RESTART_INTERVAL = restart_config.get('check_restart_interval', cls.CHECK_RESTART_INTERVAL)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load {config_key} from {CONFIG_JSON_PATH}: {e}")
            print(f"Using default values for {cls.__name__}")


class RestartControlConfigOld(RestartControlConfigBase):
    @classmethod
    def load_from_json(cls):
        super().load_from_json('restart_control_config_old')


class RestartControlConfigMed(RestartControlConfigBase):
    @classmethod
    def load_from_json(cls):
        super().load_from_json('restart_control_config_med')


class RestartControlConfigNew(RestartControlConfigBase):
    @classmethod
    def load_from_json(cls):
        super().load_from_json('restart_control_config_new')


class LowCostControlConfigBase:
    """Base class for low cost control configuration"""

    # Default values
    CHECK_LOW_COST_HOURS = [20, 21, 22, 23]  # [6, 18)
    CHECK_LOW_COST_INTERVAL = 10  # minutes

    @classmethod
    def load_from_json(cls, config_key):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                low_cost_config = config_data.get(config_key, {})

                cls.CHECK_LOW_COST_HOURS = low_cost_config.get('check_low_cost_hours', cls.CHECK_LOW_COST_HOURS)        
                cls.CHECK_LOW_COST_INTERVAL = low_cost_config.get('check_low_cost_interval', cls.CHECK_LOW_COST_INTERVAL)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load {config_key} from {CONFIG_JSON_PATH}: {e}")
            print(f"Using default values for {cls.__name__}")


class LowCostControlConfigOld(LowCostControlConfigBase):
    @classmethod
    def load_from_json(cls):
        super().load_from_json('low_cost_control_config_old')               


class LowCostControlConfigMed(LowCostControlConfigBase):
    @classmethod
    def load_from_json(cls):
        super().load_from_json('low_cost_control_config_med')


class LowCostControlConfigNew(LowCostControlConfigBase):
    @classmethod
    def load_from_json(cls):
        super().load_from_json('low_cost_control_config_new')


# ============================================================================
# Natural Usage Helper Functions
# ============================================================================

def get_account_type(account_id):
    """Determine account type based on account_id"""
    try:
        if account_id in AccountConfig.OLD_ACCOUNTS:
            return 'old'
        elif account_id in AccountConfig.MEDIAN_ACCOUNTS:
            return 'med'
        elif account_id in AccountConfig.NEW_ACCOUNTS:
            return 'new'
        else:
            # Default to 'new' for unknown accounts
            return 'new'
    except Exception as e:
        print(f"Error in get_account_type: {e}, defaulting to 'new'")
        return 'new'


def get_create_config(account_id):
    """Get the appropriate CreateConfig class for the given account_id"""
    account_type = get_account_type(account_id)
    if account_type == 'old':
        return CreateConfigOld
    elif account_type == 'med':
        return CreateConfigMed
    else:
        return CreateConfigNew


def get_budget_control_config(account_id):
    """Get the appropriate BudgetControlConfig class for the given account_id"""
    account_type = get_account_type(account_id)
    
    if account_type == 'old':
        return BudgetControlConfigOld
    elif account_type == 'med':
        return BudgetControlConfigMed
    else:  # 'new'
        return BudgetControlConfigNew


def get_acquisition_control_config(account_id):
    """Get the appropriate AcquisitionControlConfig class for the given account_id"""
    account_type = get_account_type(account_id)

    if account_type == 'old':
        return AcquisitionControlConfigOld
    elif account_type == 'med':
        return AcquisitionControlConfigMed
    else:  # 'new'
        return AcquisitionControlConfigNew


def get_create_config(account_id):
    """Get the appropriate CreateConfig class for the given account_id"""
    account_type = get_account_type(account_id)

    if account_type == 'old':
        return CreateConfigOld
    elif account_type == 'med':
        return CreateConfigMed
    else:  # 'new'
        return CreateConfigNew


def get_restart_control_config(account_id):
    """Get the appropriate RestartControlConfig class for the given account_id"""
    account_type = get_account_type(account_id)

    if account_type == 'old':
        return RestartControlConfigOld
    elif account_type == 'med':
        return RestartControlConfigMed
    else:  # 'new'
        return RestartControlConfigNew
    

def get_low_cost_control_config(account_id):
    """Get the appropriate LowCostControlConfig class for the given account_id"""
    account_type = get_account_type(account_id)

    if account_type == 'old':
        return LowCostControlConfigOld
    elif account_type == 'med':
        return LowCostControlConfigMed
    else:  # 'new'
        return LowCostControlConfigNew
    




# ============================================================================
# Unified Config Getter (Most Natural Usage)
# ============================================================================

def get_config(account_id, config_type):
    """
    Get any configuration class for the given account_id and config_type.

    Args:
        account_id: The account ID to determine the config layer
        config_type: Type of config ('budget_control', 'acquisition_control', 'create', 'restart_control', 'low_cost_control')

    Returns:
        The appropriate configuration class

    Usage:
        budget_config = get_config(account_id, 'budget_control')
    """
    config_getters = {
        'budget_control': get_budget_control_config,
        'acquisition_control': get_acquisition_control_config,
        'create': get_create_config,
        'restart_control': get_restart_control_config,
        'low_cost_control': get_low_cost_control_config
    }

    if config_type not in config_getters:
        raise ValueError(f"Unknown config_type: {config_type}. Available types: {list(config_getters.keys())}")

    return config_getters[config_type](account_id)


class ConfigManager:
    """
    Configuration manager for easy access to all account-specific configs.

    Usage:
        config_mgr = ConfigManager(account_id)
        budget_config = config_mgr.budget_control
        acquisition_config = config_mgr.acquisition_control
        all_configs = config_mgr.get_all_configs()
    """

    def __init__(self, account_id):
        self.account_id = account_id
        self.account_type = get_account_type(account_id)

        # Cache config classes for this account
        self._budget_control = None
        self._acquisition_control = None
        self._create_config = None
        self._restart_control = None
        self._low_cost_control = None

        self._target_config = None
        self._general_config = None
        self._account_config = None
        self._api_config = None
        self._path_config = None
        self._adgroup_report_data_header = None
        self._account_report_data_header = None
        self._string_format = None
        

    @property
    def budget_control(self):
        """Get BudgetControlConfig for this account"""
        if self._budget_control is None:
            self._budget_control = get_budget_control_config(self.account_id)
        return self._budget_control
    
    @property
    def acquisition_control(self):
        """Get AcquisitionControlConfig for this account"""
        if self._acquisition_control is None:
            self._acquisition_control = get_acquisition_control_config(self.account_id)
        return self._acquisition_control

    @property
    def create_config(self):
        """Get CreateConfig for this account"""
        if self._create_config is None:
            self._create_config = get_create_config(self.account_id)
        return self._create_config
    

    @property
    def restart_control(self):
        """Get RestartControlConfig for this account"""
        if self._restart_control is None:
            self._restart_control = get_restart_control_config(self.account_id)
        return self._restart_control
    
    @property
    def low_cost_control(self):
        """Get LowCostControlConfig for this account"""
        if self._low_cost_control is None:
            self._low_cost_control = get_low_cost_control_config(self.account_id)
        return self._low_cost_control
    
    @property
    def target_config(self):
        if self._target_config is None:
            self._target_config = TargetConfig
        return self._target_config
    
    @property
    def general_config(self):
        if self._general_config is None:
            self._general_config = GeneralConfig
        return self._general_config
    
    @property
    def account_config(self):
        if self._account_config is None:
            self._account_config = AccountConfig
        return self._account_config
    
    @property
    def api_config(self):
        if self._api_config is None:
            self._api_config = APIConfig
        return self._api_config
    
    @property
    def path_config(self):
        if self._path_config is None:
            self._path_config = PathConfig
        return self._path_config
    
    @property
    def adgroup_report_data_header(self):
        if self._adgroup_report_data_header is None:
            self._adgroup_report_data_header = AdGroupReportDataHeader
        return self._adgroup_report_data_header
    
    @property
    def account_report_data_header(self):
        if self._account_report_data_header is None:
            self._account_report_data_header = AccountReportDataHeader
        return self._account_report_data_header
    
    @property
    def string_format(self):
        if self._string_format is None:
            self._string_format = StringFormat
        return self._string_format
    
    @classmethod
    def get_all_config_types(cls):
        """Get all available config types"""
        return [
            'target_control', 'budget_control', 'acquisition_control', 'create_config', 'restart_control', 'low_cost_control',
            'general_config', 'account_config', 'api_config', 'path_config', 'adgroup_report_data_header', 'account_report_data_header', 'string_format'
        ]

    def get_all_configs(self):
        """Get all configuration classes for this account as a dictionary"""
        return {
            'target_control': self.target_config,
            'budget_control': self.budget_control,
            'acquisition_control': self.acquisition_control,
            'create_config': self.create_config,
            'restart_control': self.restart_control,
            'low_cost_control': self.low_cost_control,
            'general_config': self.general_config,  # Same for all accounts
            'account_config': self.account_config,
            'api_config': self.api_config,
            'path_config': self.path_config,
            'adgroup_report_data_header': self.adgroup_report_data_header,
            'account_report_data_header': self.account_report_data_header,
            'string_format': self.string_format
        }


# ============================================================================
# Initialization
# ============================================================================

def load_all_layered_configs():
    """Load all layered configurations from JSON file"""
    print(f"Loading all layered configurations from {CONFIG_JSON_PATH}")

    # Load unchanged configs
    TargetConfig.load_from_json()
    GeneralConfig.load_from_json()
    AccountConfig.load_from_json()
    APIConfig.load_from_json()
    PathConfig.load_from_json()
    AccountReportDataHeader.load_from_json()
    AdGroupReportDataHeader.load_from_json()
    StringFormat.load_from_json()

    BudgetControlConfigOld.load_from_json()
    BudgetControlConfigMed.load_from_json()
    BudgetControlConfigNew.load_from_json()

    AcquisitionControlConfigOld.load_from_json()
    AcquisitionControlConfigMed.load_from_json()
    AcquisitionControlConfigNew.load_from_json()

    CreateConfigOld.load_from_json()
    CreateConfigMed.load_from_json()
    CreateConfigNew.load_from_json()

    RestartControlConfigOld.load_from_json()
    RestartControlConfigMed.load_from_json()
    RestartControlConfigNew.load_from_json()

    LowCostControlConfigOld.load_from_json()
    LowCostControlConfigMed.load_from_json()
    LowCostControlConfigNew.load_from_json()

    print("All layered configurations loaded successfully")


# ============================================================================
# Usage Examples and Documentation
# ============================================================================

"""
Usage Examples:

1. Simple function-based approach:
    budget_config = get_budget_control_config(account_id)

2. Unified getter approach:
    budget_config = get_config(account_id, 'budget_control')

3. Manager-based approach (most convenient for multiple configs):
    config_mgr = ConfigManager(account_id)
    budget_config = config_mgr.budget_control
    all_configs = config_mgr.get_all_configs()

4. Direct class usage (when you know the account type):
    med_budget = BudgetControlConfigMed()

5. Replacing existing code:
    # Old way:
    # result = some_function(account_id, BudgetControlConfig, ...)

    # New way:
    # result = some_function(account_id, get_budget_control_config(account_id), ...)

    # Or with manager:
    # config_mgr = ConfigManager(account_id)
    # result = some_function(account_id, config_mgr.budget_control, ...)
"""


# Auto-load configurations when module is imported
if __name__ != '__main__':
    load_all_layered_configs()
