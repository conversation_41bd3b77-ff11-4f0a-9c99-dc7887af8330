import json
import os

# JSON configuration file path - constant
CONFIG_JSON_PATH = "config/wyd_config_comprehensive.json"


class TargetConfig:

    # Default values  
    YEAR_TARGET = 210
    STEP_1_TARGET = 350
    STEP_2_TARGET = 800
    STEP_3_TARGET = 6000

    @classmethod
    def load_from_json(cls):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                target_config = config_data.get('target_config', {})

                cls.YEAR_TARGET = target_config.get('year_target', cls.YEAR_TARGET)
                cls.STEP_1_TARGET = target_config.get('step_1_target', cls.STEP_1_TARGET)
                cls.STEP_2_TARGET = target_config.get('step_2_target', cls.STEP_2_TARGET)
                cls.STEP_3_TARGET = target_config.get('step_3_target', cls.STEP_3_TARGET)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load target config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for TargetConfig")


class AccountConfig:

    # Default values  
    OLD_ACCOUNTS = []
    MEDIAN_ACCOUNTS = []
    NEW_ACCOUNTS = ['********']
    ONE_HUNDRED_ACCOUNT = []
    ONE_THIRD_ACCOUNT = []
    TWO_SMALL_UP_ACCOUNT = []
    TWO_UPS_ACCOUNT = []
    AFTERNOON_ACCOUNTS = []
    CONTROL_EXCLUDE_ACCOUNTS = []
    CREATE_EXCLUDE_ACCOUNTS = []
    UP_EXCLUDE_ACCOUNTS = []
    OVER_BUDGET_EXCLUDE_ACCOUNTS = []
    LOW_COST_EXCLUDE_ACCOUNTS = []
    YOUJU_ACCOUNTS = []
    STOP_ACCOUNTS = []
    ACTIVE_STATUS = ['ADGROUP_STATUS_PARTIAL_ACTIVE', 'ADGROUP_STATUS_ACTIVE']
    NOT_IN_DELIVERY_STATUS = 'ADGROUP_STATUS_NOT_IN_DELIVERY'
    AUTO_ACQUISITION_PENDING_STATUS = 'AUTO_ACQUISITION_STATUS_PENDING'

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                account_config = config_data.get('account_config', {})

                cls.OLD_ACCOUNTS = account_config.get('old_accounts', cls.OLD_ACCOUNTS)
                cls.MEDIAN_ACCOUNTS = account_config.get('median_accounts', cls.MEDIAN_ACCOUNTS)
                cls.NEW_ACCOUNTS = account_config.get('new_accounts', cls.NEW_ACCOUNTS)
                cls.ONE_HUNDRED_ACCOUNT = account_config.get('one_hundred_account', cls.ONE_HUNDRED_ACCOUNT)
                cls.ONE_THIRD_ACCOUNT = account_config.get('one_third_account', cls.ONE_THIRD_ACCOUNT)
                cls.TWO_SMALL_UP_ACCOUNT = account_config.get('two_small_up_account', cls.TWO_SMALL_UP_ACCOUNT)
                cls.TWO_UPS_ACCOUNT = account_config.get('two_ups_account', cls.TWO_UPS_ACCOUNT)
                cls.AFTERNOON_ACCOUNTS = account_config.get('afternoon_accounts', cls.AFTERNOON_ACCOUNTS)
                cls.CONTROL_EXCLUDE_ACCOUNTS = account_config.get('control_exclude_accounts', cls.CONTROL_EXCLUDE_ACCOUNTS)
                cls.CREATE_EXCLUDE_ACCOUNTS = account_config.get('create_exclude_accounts', cls.CREATE_EXCLUDE_ACCOUNTS)
                cls.UP_EXCLUDE_ACCOUNTS = account_config.get('up_exclude_accounts', cls.UP_EXCLUDE_ACCOUNTS)
                cls.OVER_BUDGET_EXCLUDE_ACCOUNTS = account_config.get('over_budget_exclude_accounts', cls.OVER_BUDGET_EXCLUDE_ACCOUNTS)
                cls.LOW_COST_EXCLUDE_ACCOUNTS = account_config.get('low_cost_exclude_accounts', cls.LOW_COST_EXCLUDE_ACCOUNTS)
                cls.YOUJU_ACCOUNTS = account_config.get('youju_accounts', cls.YOUJU_ACCOUNTS)
                cls.STOP_ACCOUNTS = account_config.get('stop_accounts', cls.STOP_ACCOUNTS)
                cls.ACTIVE_STATUS = account_config.get('active_status', cls.ACTIVE_STATUS)
                cls.NOT_IN_DELIVERY_STATUS = account_config.get('not_in_delivery_status', cls.NOT_IN_DELIVERY_STATUS)
                cls.AUTO_ACQUISITION_PENDING_STATUS = account_config.get('auto_acquisition_pending_status', cls.AUTO_ACQUISITION_PENDING_STATUS)

                # Computed property
                cls.ACCOUNT_IDS = cls.OLD_ACCOUNTS + cls.MEDIAN_ACCOUNTS + cls.NEW_ACCOUNTS

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load account config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for AccountConfig")
            # Ensure computed property is set even with defaults
            cls.ACCOUNT_IDS = cls.OLD_ACCOUNTS + cls.MEDIAN_ACCOUNTS + cls.NEW_ACCOUNTS


class APIConfig:
    # Default values  
    API_URL = 'https://api.e.qq.com/v3.0/'
    ADGROUPS_INTERFACE = 'adgroups/get'
    HOURLY_INTERFACE = 'hourly_reports/get'
    DAILY_INTERFACE = 'daily_reports/get'
    ACCESS_TOKEN = '66c1748bdca5e0016250f3cdd4cdaa4f'
    SERVER_PORT = 8866  # Default server port


    @classmethod
    def load_from_json(cls):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                api_config = config_data.get('api_config', {})

                cls.API_URL = api_config.get('api_url', cls.API_URL)
                cls.ADGROUPS_INTERFACE = api_config.get('adgroups_interface', cls.ADGROUPS_INTERFACE)
                cls.HOURLY_INTERFACE = api_config.get('hourly_interface', cls.HOURLY_INTERFACE)
                cls.DAILY_INTERFACE = api_config.get('daily_interface', cls.DAILY_INTERFACE)
                cls.ACCESS_TOKEN = api_config.get('access_token', cls.ACCESS_TOKEN)
                cls.SERVER_PORT = api_config.get('server_port', cls.SERVER_PORT)


        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load API config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for APIConfig")


class PathConfig:

    # Default values  
    ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
    ADGROUPS_DIR = "./adgroups"
    ADGROUP_REPORT_PATH = 'adgroup_report_data.json'
    ACCOUNT_REPORT_PATH = 'account_report_data.json'
    STOP_DATES_PATH = 'stop_dates.txt'
    ACCOUNT_UP_TIME_PATH = 'account_up_time.json'
    ACCOUNT_CREATE_TIME_PATH = 'account_create_time.json'
    PREDICT_DATA_PATH = 'predict_for_today.csv'
    SIGNATURE_DATA_PATH = 'signature_data.csv'

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                path_config = config_data.get('path_config', {})

                cls.ADGROUPS_DIR = path_config.get('adgroups_dir', cls.ADGROUPS_DIR)
                cls.ADGROUP_REPORT_PATH = path_config.get('adgroup_report_path', cls.ADGROUP_REPORT_PATH)
                cls.ACCOUNT_REPORT_PATH = path_config.get('account_report_path', cls.ACCOUNT_REPORT_PATH)
                cls.STOP_DATES_PATH = path_config.get('stop_dates_path', cls.STOP_DATES_PATH)
                cls.ACCOUNT_UP_TIME_PATH = path_config.get('account_up_time_path', cls.ACCOUNT_UP_TIME_PATH)
                cls.ACCOUNT_CREATE_TIME_PATH = path_config.get('account_create_time_path', cls.ACCOUNT_CREATE_TIME_PATH)
                cls.PREDICT_DATA_PATH = path_config.get('predict_data_path', cls.PREDICT_DATA_PATH)

                # ROOT_DIR is computed, not loaded from JSON

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load path config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for PathConfig")

"""
class CreateConfig:

    # Default values  
    NO_CREATE_WEEK_OF_DAY = ['星期二', '星期四', '星期六', '星期日']
    CREATE_HOUR_FOR_SPECIAL_DAY_OF_WEEK = [6, 7]
    CREATE_HOUR_FOR_WEEKDAY = [14, 15]

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                create_config = config_data.get('create_config', {})

                cls.NO_CREATE_WEEK_OF_DAY = create_config.get('no_create_week_of_day', cls.NO_CREATE_WEEK_OF_DAY)
                cls.CREATE_HOUR_FOR_SPECIAL_DAY_OF_WEEK = create_config.get('create_hour_for_special_day_of_week', cls.CREATE_HOUR_FOR_SPECIAL_DAY_OF_WEEK)
                cls.CREATE_HOUR_FOR_WEEKDAY = create_config.get('create_hour_for_weekday', cls.CREATE_HOUR_FOR_WEEKDAY)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load create config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for CreateConfig")


class BudgetControlConfig:
    # Default values  
    CHECK_PRE_THRESHOLD_RATIO = 0.5
    STEP_1_COST_LIMIT_SCALE = 2
    STEP_2_COST_LIMIT_SCALE = 2
    STEP_3_COST_LIMIT_SCALE = 1
    USE_HOUXIAO_STOP = 0
    POST_EFFECT_STEP_3_NUM_LIMIT = 10
    COST_RATE_SCALE = 2
    MORNING_SCALE = 1.0
    AFTERNOON_SCALE = 1.0
    NIGHT_SCALE = 1.0

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                budget_config = config_data.get('budget_control_config', {})

                cls.CHECK_PRE_THRESHOLD_RATIO = budget_config.get('check_pre_threshold_ratio', cls.CHECK_PRE_THRESHOLD_RATIO)
                cls.STEP_1_COST_LIMIT_SCALE = budget_config.get('step_1_cost_limit_scale', cls.STEP_1_COST_LIMIT_SCALE)
                cls.STEP_2_COST_LIMIT_SCALE = budget_config.get('step_2_cost_limit_scale', cls.STEP_2_COST_LIMIT_SCALE)
                cls.STEP_3_COST_LIMIT_SCALE = budget_config.get('step_3_cost_limit_scale', cls.STEP_3_COST_LIMIT_SCALE)
                cls.USE_HOUXIAO_STOP = budget_config.get('use_houxiao_stop', cls.USE_HOUXIAO_STOP)
                cls.POST_EFFECT_STEP_3_NUM_LIMIT = budget_config.get('post_effect_step_3_num_limit', cls.POST_EFFECT_STEP_3_NUM_LIMIT)
                cls.COST_RATE_SCALE = budget_config.get('cost_rate_scale', cls.COST_RATE_SCALE)
                cls.MORNING_SCALE = budget_config.get('morning_scale', cls.MORNING_SCALE)
                cls.AFTERNOON_SCALE = budget_config.get('afternoon_scale', cls.AFTERNOON_SCALE)
                cls.NIGHT_SCALE = budget_config.get('night_scale', cls.NIGHT_SCALE)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load budget control config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for BudgetControlConfig")


class AcquisitionControlConfig:
    # Default values  
    UP_ADGROUP_NUM_FOR_MORNING = 1 # 所有账号只起量一个广告
    UP_ADGROUP_NUM_FOR_AFTERNOON = 1 # 所有账号只起量一个广告
    UP_ADGROUP_NUM_FOR_MODEL = 2 
    UP_NEW_ADGROUP_NUM = 1 # 起量新广告数
    UP_ALL_NUM_FOR_NEW = 1
    UP_ALL_NUM_FOR_MEDIAN = 2
    UP_ALL_NUM_FOR_OLD = 3
    MORNING_HOURS = [9, 10]
    AFTERNOON_HOURS = [14, 15]
    NO_UP_DAY_OF_WEEK = ['星期六', '星期日']
    REUP_HOURS = [12, 13, 14, 15]
    COST_THRESHOLD = 700
    ACQUISITION_BUDGET_FOR_OLD = 2000000
    ACQUISITION_BUDGET_FOR_MED = 5000000
    ACQUISITION_BUDGET_FOR_NEW = 3000000
    ACQUISITION_BUDGET_FOR_REUP = 5000000
    RANK_1_LIMIT_FOR_MORNING = {"today_reservation_uv": 1, "today_biz_reg_uv": 1}
    RANK_2_LIMIT_FOR_MORNING = {"today_biz_follow_uv": 2, "today_biz_reg_uv": 1}
    RANK_3_LIMIT_FOR_MORNING = {"today_cost": 700, "up_trend": 1}
    RANK_4_LIMIT_FOR_MORNING = {"life_long_days": 3, "is_ever_up": 0}
    RANK_1_LIMIT_FOR_AFTERNOON = {"today_reservation_uv": 1}
    RANK_2_LIMIT_FOR_AFTERNOON = {"past_1day_reservation_uv": 1, "past_1day_reservation_cost": 1.2}
    RANK_3_LIMIT_FOR_AFTERNOON = {"today_biz_reg_uv": 1}
    FINAL_COST_LIMIT_SCALE = 1.2
    STEP_3_COST_LIMIT_SCALE = 2.1
    STEP_3_LIMIT_FOR_REUP = 2

    @classmethod
    def load_from_json(cls):

        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                acquisition_config = config_data.get('acquisition_control_config', {})

                cls.UP_ALL_NUM_FOR_NEW = acquisition_config.get('up_all_num_for_new', cls.UP_ALL_NUM_FOR_NEW)
                cls.UP_ALL_NUM_FOR_MEDIAN = acquisition_config.get('up_all_num_for_median', cls.UP_ALL_NUM_FOR_MEDIAN)
                cls.UP_ALL_NUM_FOR_OLD = acquisition_config.get('up_all_num_for_old', cls.UP_ALL_NUM_FOR_OLD)
                cls.MORNING_HOURS = acquisition_config.get('morning_hours', cls.MORNING_HOURS)
                cls.AFTERNOON_HOURS = acquisition_config.get('afternoon_hours', cls.AFTERNOON_HOURS)
                cls.NO_UP_DAY_OF_WEEK = acquisition_config.get('no_up_day_of_week', cls.NO_UP_DAY_OF_WEEK)
                cls.REUP_HOURS = acquisition_config.get('reup_hours', cls.REUP_HOURS)
                cls.COST_THRESHOLD = acquisition_config.get('cost_threshold', cls.COST_THRESHOLD)
                cls.ACQUISITION_BUDGET_FOR_OLD = acquisition_config.get('acquisition_budget_for_old', cls.ACQUISITION_BUDGET_FOR_OLD)
                cls.ACQUISITION_BUDGET_FOR_MED = acquisition_config.get('acquisition_budget_for_med', cls.ACQUISITION_BUDGET_FOR_MED)
                cls.ACQUISITION_BUDGET_FOR_NEW = acquisition_config.get('acquisition_budget_for_new', cls.ACQUISITION_BUDGET_FOR_NEW)
                cls.ACQUISITION_BUDGET_FOR_REUP = acquisition_config.get('acquisition_budget_for_reup', cls.ACQUISITION_BUDGET_FOR_REUP)

                cls.RANK_1_LIMIT_FOR_MORNING = acquisition_config.get('rank_1_limit_for_morning', cls.RANK_1_LIMIT_FOR_MORNING)
                cls.RANK_2_LIMIT_FOR_MORNING = acquisition_config.get('rank_2_limit_for_morning', cls.RANK_2_LIMIT_FOR_MORNING)
                cls.RANK_3_LIMIT_FOR_MORNING = acquisition_config.get('rank_3_limit_for_morning', cls.RANK_3_LIMIT_FOR_MORNING)
                cls.RANK_4_LIMIT_FOR_MORNING = acquisition_config.get('rank_4_limit_for_morning', cls.RANK_4_LIMIT_FOR_MORNING)

                cls.RANK_1_LIMIT_FOR_AFTERNOON = acquisition_config.get('rank_1_limit_for_afternoon', cls.RANK_1_LIMIT_FOR_AFTERNOON)
                cls.RANK_2_LIMIT_FOR_AFTERNOON = acquisition_config.get('rank_2_limit_for_afternoon', cls.RANK_2_LIMIT_FOR_AFTERNOON)
                cls.RANK_3_LIMIT_FOR_AFTERNOON = acquisition_config.get('rank_3_limit_for_afternoon', cls.RANK_3_LIMIT_FOR_AFTERNOON)

                cls.STEP_3_LIMIT_FOR_REUP = acquisition_config.get('step_3_limit_for_reup', cls.STEP_3_LIMIT_FOR_REUP)
                cls.FINAL_COST_LIMIT_SCALE = acquisition_config.get('final_cost_limit_scale', cls.FINAL_COST_LIMIT_SCALE)
                cls.STEP_3_COST_LIMIT_SCALE = acquisition_config.get('step_3_cost_limit_scale', cls.STEP_3_COST_LIMIT_SCALE)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load acquisition control config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for AcquisitionControlConfig")



class RestartControlConfig:
    # includes config for restart & low cost
    # Default values
    CHECK_RESTART_HOURS = [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]  # [6, 18)
    # CHECK_RESTART_MINUTE = 0
    CHECK_RESTART_INTERVAL = 10 # minutes

    STEP_1_COST_LIMIT_SCALE = 2
    STEP_2_COST_LIMIT_SCALE = 2
    STEP_3_COST_LIMIT_SCALE = 1.3333

    @classmethod
    def load_from_json(cls):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                restart_config = config_data.get('restart_config', {})

                cls.CHECK_RESTART_HOURS = restart_config.get('check_restart_hours', cls.CHECK_RESTART_HOURS)
                # cls.CHECK_RESTART_MINUTE = restart_config.get('check_restart_minute', cls.CHECK_RESTART_MINUTE)
                cls.CHECK_RESTART_INTERVAL = restart_config.get('check_restart_interval', cls.CHECK_RESTART_INTERVAL)
                cls.STEP_1_COST_LIMIT_SCALE = restart_config.get('step_1_cost_limit_scale', cls.STEP_1_COST_LIMIT_SCALE)
                cls.STEP_2_COST_LIMIT_SCALE = restart_config.get('step_2_cost_limit_scale', cls.STEP_2_COST_LIMIT_SCALE)
                cls.STEP_3_COST_LIMIT_SCALE = restart_config.get('step_3_cost_limit_scale', cls.STEP_3_COST_LIMIT_SCALE)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load restart config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for RestartControlConfig")

class LowCostControlConfig:
    # Default values
    CHECK_LOW_COST_HOURS_FOR_OLD = [18, 19, 20, 21, 22, 23]  # [18, 24)
    CHECK_LOW_COST_HOURS_FOR_MEDIAN = [20, 21, 22, 23]  # [20, 24)
    CHECK_LOW_COST_HOURS_FOR_NEW = [20, 21, 22, 23]  # [20, 24)
    # CHECK_LOW_COST_MINUTE = 0
    CHECK_LOW_COST_INTERVAL = 10 # minutes

    @classmethod
    def load_from_json(cls):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                low_cost_config = config_data.get('low_cost_config', {})

                cls.CHECK_LOW_COST_HOURS_FOR_OLD = low_cost_config.get('check_low_cost_hours_for_old', cls.CHECK_LOW_COST_HOURS_FOR_OLD)
                cls.CHECK_LOW_COST_HOURS_FOR_MEDIAN = low_cost_config.get('check_low_cost_hours_for_median', cls.CHECK_LOW_COST_HOURS_FOR_MEDIAN)
                cls.CHECK_LOW_COST_HOURS_FOR_NEW = low_cost_config.get('check_low_cost_hours_for_new', cls.CHECK_LOW_COST_HOURS_FOR_NEW)
                # cls.CHECK_LOW_COST_MINUTE = low_cost_config.get('check_low_cost_minute', cls.CHECK_LOW_COST_MINUTE)
                cls.CHECK_LOW_COST_INTERVAL = low_cost_config.get('check_low_cost_interval', cls.CHECK_LOW_COST_INTERVAL)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load low cost config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for LowCostControlConfig")
"""

class AccountReportDataHeader:
    # Default values
    COST_RATE = 'aveD7CostRate'
    ASSESS_COST = 'assessCostD7'
    ASSESS_COST_RATE = 'assessCostRateD7'
    STEP_1_COST = 'FollowCost'
    STEP_2_COST = 'RegCost'
    STEP_3_COST = 'ReservationCost'

    PAST_2_WEEKS_PREFIX = 'past2Weeks'

    @classmethod
    def load_from_json(cls):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                account_report_header_config = config_data.get('account_report_data_header_config', {})

                cls.COST_RATE = account_report_header_config.get('cost_rate', cls.COST_RATE)
                cls.ASSESS_COST = account_report_header_config.get('assess_cost', cls.ASSESS_COST)
                cls.ASSESS_COST_RATE = account_report_header_config.get('assess_cost_rate', cls.ASSESS_COST_RATE)
                cls.STEP_1_COST = account_report_header_config.get('step_1_cost', cls.STEP_1_COST)
                cls.STEP_2_COST = account_report_header_config.get('step_2_cost', cls.STEP_2_COST)
                cls.STEP_3_COST = account_report_header_config.get('step_3_cost', cls.STEP_3_COST)
                cls.PAST_2_WEEKS_PREFIX = account_report_header_config.get('past_2_weeks_prefix', cls.PAST_2_WEEKS_PREFIX)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load account report data header config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for AccountReportDataHeader")


class AdGroupReportDataHeader:
    # Default values

    LIFE_LONG_DAYS = 'life_long_days'
    UP_TREND = 'up_trend'
    IS_EVER_UP = 'is_ever_up'
    TODAY_PREFIX = 'today'
    PAST_1_DAYS_PREFIX = 'past_1day'
    PAST_3_DAYS_PREFIX = 'past_3day'
    PAST_7_DAYS_PREFIX = 'past_7day'
    PAST_14_DAYS_PREFIX = 'past_14day'
    COST_POSTFIX = 'cost'
    UV_POSTFIX = 'uv'
    STEP_1_STAT_NAME = 'biz_follow'
    STEP_2_STAT_NAME = 'biz_reg'
    STEP_3_STAT_NAME = 'reservation'
    JOIN_CHAR = '_'

    @classmethod
    def load_from_json(cls):
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                data_header_config = config_data.get('adgroup_reportdata_header_config', {})

                cls.LIFE_LONG_DAYS = data_header_config.get('life_long_days', cls.LIFE_LONG_DAYS)
                cls.UP_TREND = data_header_config.get('up_trend', cls.UP_TREND)
                cls.IS_EVER_UP = data_header_config.get('is_ever_up', cls.IS_EVER_UP)
                cls.TODAY_PREFIX = data_header_config.get('today_prefix', cls.TODAY_PREFIX)
                cls.PAST_1_DAYS_PREFIX = data_header_config.get('past_1_days_prefix', cls.PAST_1_DAYS_PREFIX)
                cls.PAST_3_DAYS_PREFIX = data_header_config.get('past_3_days_prefix', cls.PAST_3_DAYS_PREFIX)
                cls.PAST_7_DAYS_PREFIX = data_header_config.get('past_7_days_prefix', cls.PAST_7_DAYS_PREFIX)
                cls.PAST_14_DAYS_PREFIX = data_header_config.get('past_14_days_prefix', cls.PAST_14_DAYS_PREFIX)
                cls.COST_POSTFIX = data_header_config.get('cost_postfix', cls.COST_POSTFIX)
                cls.UV_POSTFIX = data_header_config.get('uv_postfix', cls.UV_POSTFIX)
                cls.STEP_1_STAT_NAME = data_header_config.get('step_1_stat_name', cls.STEP_1_STAT_NAME)
                cls.STEP_2_STAT_NAME = data_header_config.get('step_2_stat_name', cls.STEP_2_STAT_NAME)
                cls.STEP_3_STAT_NAME = data_header_config.get('step_3_stat_name', cls.STEP_3_STAT_NAME)
                cls.JOIN_CHAR = data_header_config.get('join_char', cls.JOIN_CHAR)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load data header config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for DataHeader")


class StringFormat:
    # Default values  
    UPDATE_DELIVERY_DATE = 'updateDeliveryDate'
    UPDATE_DELIVERY_TIME = 'updateDeliveryTime'
    START_AUTO_ACQUISITION = 'startAutoAcquisition'

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                string_format_config = config_data.get('string_format_config', {})

                cls.UPDATE_DELIVERY_DATE = string_format_config.get('update_delivery_date', cls.UPDATE_DELIVERY_DATE)
                cls.UPDATE_DELIVERY_TIME = string_format_config.get('update_delivery_time', cls.UPDATE_DELIVERY_TIME)
                cls.START_AUTO_ACQUISITION = string_format_config.get('start_auto_acquisition', cls.START_AUTO_ACQUISITION)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load string format config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for StringFormat")


class GeneralConfig:
    # Default values
    SCALE_RATIO = 1.0 # 回传
    ACQUISITION_COST_THRESHOLD = 100  # Minimum acquisition cost to be considered "up"
    SIGNATURE_COST_THRESHOLD = 1000  # Minimum cost for signature data
    PAGE_SIZE_DEFAULT = 100  # Default page size for API calls
    PAGE_SIZE_LARGE = 500  # Large page size for hourly reports
    HIST_RANGE = 7

    NO_AD_DAY_OF_WEEK = ['星期六', '星期日']
    WEEKDAY_DICT = {
        "0": "星期一",
        "1": "星期二",
        "2": "星期三",
        "3": "星期四",
        "4": "星期五",
        "5": "星期六",
        "6": "星期日"
    }

    @classmethod
    def load_from_json(cls):
 
        try:
            with open(CONFIG_JSON_PATH, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                general_config = config_data.get('general_config', {})

                cls.SCALE_RATIO = general_config.get('scale_ratio', cls.SCALE_RATIO)
                cls.HIST_RANGE = general_config.get('hist_range', cls.HIST_RANGE)
                # cls.MIN_COST_THRESHOLD = general_config.get('min_cost_threshold', cls.MIN_COST_THRESHOLD)
                # cls.LOW_COST_THRESHOLD = general_config.get('low_cost_threshold', cls.LOW_COST_THRESHOLD)
                # cls.DELETE_COST_THRESHOLD = general_config.get('delete_cost_threshold', cls.DELETE_COST_THRESHOLD)
                cls.ACQUISITION_COST_THRESHOLD = general_config.get('acquisition_cost_threshold', cls.ACQUISITION_COST_THRESHOLD)
                cls.SIGNATURE_COST_THRESHOLD = general_config.get('signature_cost_threshold', cls.SIGNATURE_COST_THRESHOLD)
                cls.PAGE_SIZE_DEFAULT = general_config.get('page_size_default', cls.PAGE_SIZE_DEFAULT)
                cls.PAGE_SIZE_LARGE = general_config.get('page_size_large', cls.PAGE_SIZE_LARGE)
                cls.WEEKDAY_DICT = general_config.get('weekday_dict', cls.WEEKDAY_DICT)
                cls.NO_AD_DAY_OF_WEEK = general_config.get('no_ad_day_of_week', cls.NO_AD_DAY_OF_WEEK)

        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load general config from {CONFIG_JSON_PATH}: {e}")
            print("Using default values for GeneralConfig")


def load_all_configs():
    print(f"Loading all configurations from {CONFIG_JSON_PATH}")
    TargetConfig.load_from_json()
    AccountConfig.load_from_json()
    APIConfig.load_from_json()
    PathConfig.load_from_json()
    # CreateConfig.load_from_json()
    # BudgetControlConfig.load_from_json()
    # AcquisitionControlConfig.load_from_json()
    # RestartControlConfig.load_from_json()
    AccountReportDataHeader.load_from_json()
    AdGroupReportDataHeader.load_from_json()
    StringFormat.load_from_json()
    GeneralConfig.load_from_json()
    print("All configurations loaded successfully")


# Auto-load configurations when module is imported
load_all_configs()
