{"target_config": {"year_target": 210, "step_1_target": 350, "step_2_target": 800, "step_3_target": 6000}, "budget_control_config_old": {"check_pre_threshold_ratio": 0.5, "step_1_cost_limit_scale": 2, "step_2_cost_limit_scale": 2, "step_3_cost_limit_scale": 1, "use_houxiao_stop": 0, "post_effect_step_3_num_limit": 10, "cost_rate_scale": 2, "morning_scale": 1.0, "afternoon_scale": 1.0, "night_scale": 1.0}, "budget_control_config_med": {"check_pre_threshold_ratio": 0.5, "step_1_cost_limit_scale": 2, "step_2_cost_limit_scale": 2, "step_3_cost_limit_scale": 1, "use_houxiao_stop": 0, "post_effect_step_3_num_limit": 10, "cost_rate_scale": 2, "morning_scale": 1.0, "afternoon_scale": 1.0, "night_scale": 1.0}, "budget_control_config_new": {"check_pre_threshold_ratio": 0.5, "step_1_cost_limit_scale": 2, "step_2_cost_limit_scale": 2, "step_3_cost_limit_scale": 1, "use_houxiao_stop": 0, "post_effect_step_3_num_limit": 10, "cost_rate_scale": 2, "morning_scale": 1.0, "afternoon_scale": 1.0, "night_scale": 1.0}, "acquisition_control_config_old": {"hist_range": 7, "up_all_num": 3, "morning_hours": [9, 10], "afternoon_hours": [14, 15], "no_up_day_of_week": ["星期六", "星期日"], "reup_hours": [12, 13, 14, 15], "cost_threshold": 700, "acquisition_budget": 2000000, "acquisition_budget_for_reup": 5000000, "rank_1_limit_for_morning": {"today_reservavtion_uv": 1, "today_biz_reg_uv": 1}, "rank_2_limit_for_morning": {"today_biz_follow_uv": 2, "today_biz_reg_uv": 1}, "rank_3_limit_for_morning": {"today_cost": 700, "up_trend": 1}, "rank_4_limit_for_morning": {"life_long_days": 3, "is_ever_up": 0}, "rank_1_limit_for_afternoon": {"today_reservation_uv": 1}, "rank_2_limit_for_afternoon": {"past_1day_reservation_uv": 1, "past_1day_reservation_cost": 1.2}, "rank_3_limit_for_afternoon": {"today_biz_reg_uv": 1}, "final_cost_limit_scale": 1.2, "step_3_cost_limit_scale": 2.1, "step_3_limit_for_reup": 2}, "acquisition_control_config_med": {"hist_range": 7, "up_all_num": 2, "morning_hours": [9, 10], "afternoon_hours": [14, 15], "no_up_day_of_week": ["星期六", "星期日"], "reup_hours": [12, 13, 14, 15], "cost_threshold": 700, "acquisition_budget": 5000000, "acquisition_budget_for_reup": 5000000, "rank_1_limit_for_morning": {"today_reservavtion_uv": 1, "today_biz_reg_uv": 1}, "rank_2_limit_for_morning": {"today_biz_follow_uv": 2, "today_biz_reg_uv": 1}, "rank_3_limit_for_morning": {"today_cost": 700, "up_trend": 1}, "rank_4_limit_for_morning": {"life_long_days": 3, "is_ever_up": 0}, "rank_1_limit_for_afternoon": {"today_reservation_uv": 1}, "rank_2_limit_for_afternoon": {"past_1day_reservation_uv": 1, "past_1day_reservation_cost": 1.2}, "rank_3_limit_for_afternoon": {"today_biz_reg_uv": 1}, "final_cost_limit_scale": 1.2, "step_3_cost_limit_scale": 2.1, "step_3_limit_for_reup": 2}, "acquisition_control_config_new": {"hist_range": 7, "up_all_num": 1, "morning_hours": [9, 10], "afternoon_hours": [14, 15], "no_up_day_of_week": ["星期六", "星期日"], "reup_hours": [12, 13, 14, 15], "cost_threshold": 700, "acquisition_budget": 3000000, "acquisition_budget_for_reup": 5000000, "rank_1_limit_for_morning": {"today_reservavtion_uv": 1, "today_biz_reg_uv": 1}, "rank_2_limit_for_morning": {"today_biz_follow_uv": 2, "today_biz_reg_uv": 1}, "rank_3_limit_for_morning": {"today_cost": 700, "up_trend": 1}, "rank_4_limit_for_morning": {"life_long_days": 3, "is_ever_up": 0}, "rank_1_limit_for_afternoon": {"today_reservation_uv": 1}, "rank_2_limit_for_afternoon": {"past_1day_reservation_uv": 1, "past_1day_reservation_cost": 1.2}, "rank_3_limit_for_afternoon": {"today_biz_reg_uv": 1}, "final_cost_limit_scale": 1.2, "step_3_cost_limit_scale": 2.1, "step_3_limit_for_reup": 2}, "create_config_old": {"no_create_week_of_day": ["星期二", "星期四", "星期六", "星期日"], "create_hour_for_special_day_of_week": [6, 7], "create_hour_for_weekday": [14, 15]}, "create_config_med": {"no_create_week_of_day": ["星期二", "星期四", "星期六", "星期日"], "create_hour_for_special_day_of_week": [6, 7], "create_hour_for_weekday": [14, 15]}, "create_config_new": {"no_create_week_of_day": ["星期二", "星期四", "星期六", "星期日"], "create_hour_for_special_day_of_week": [6, 7], "create_hour_for_weekday": [14, 15]}, "restart_control_config_old": {"check_restart_hours": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "check_restart_interval": 10, "step_1_cost_limit_scale": 2, "step_2_cost_limit_scale": 2, "step_3_cost_limit_scale": 1.3333}, "restart_control_config_med": {"check_restart_hours": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "check_restart_interval": 10, "step_1_cost_limit_scale": 2, "step_2_cost_limit_scale": 2, "step_3_cost_limit_scale": 1.3333}, "restart_control_config_new": {"check_restart_hours": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], "check_restart_interval": 10, "step_1_cost_limit_scale": 2, "step_2_cost_limit_scale": 2, "step_3_cost_limit_scale": 1.3333}, "low_cost_control_config_old": {"check_low_cost_hours": [18, 19, 20, 21, 22, 23], "check_low_cost_interval": 10}, "low_cost_control_config_med": {"check_low_cost_hours": [20, 21, 22, 23], "check_low_cost_interval": 10}, "low_cost_control_config_new": {"check_low_cost_hours": [20, 21, 22, 23], "check_low_cost_interval": 10}, "account_config": {"scale_ratio": 1.0, "old_accounts": [], "median_accounts": [], "new_accounts": ["********"], "one_hundred_account": [], "one_third_account": [], "two_small_up_account": [], "two_ups_account": [], "afternoon_accounts": [], "control_exclude_accounts": [], "create_exclude_accounts": [], "up_exclude_accounts": [], "over_budget_exclude_accounts": [], "low_cost_exclude_accounts": [], "youju_accounts": [], "stop_accounts": [], "active_status": ["ADGROUP_STATUS_PARTIAL_ACTIVE", "ADGROUP_STATUS_ACTIVE"], "not_in_delivery_status": "ADGROUP_STATUS_NOT_IN_DELIVERY", "auto_acquisition_pending_status": "AUTO_ACQUISITION_STATUS_PENDING"}, "general_config": {"scale_ratio": 1.0, "acquisition_cost_threshold": 100, "signature_cost_threshold": 1000, "page_size_default": 100, "page_size_large": 500, "weekday_dict": {"0": "星期一", "1": "星期二", "2": "星期三", "3": "星期四", "4": "星期五", "5": "星期六", "6": "星期日"}, "no_ad_day_of_week": ["星期六", "星期日"]}, "api_config": {"api_url": "https://api.e.qq.com/v3.0/", "adgroups_interface": "adgroups/get", "hourly_interface": "hourly_reports/get", "daily_interface": "daily_reports/get", "access_token": "66c1748bdca5e0016250f3cdd4cdaa4f", "server_port": 8866}, "path_config": {"adgroups_dir": "./adgroups", "adgroup_report_path": "adgroup_report_data.json", "stop_dates_path": "stop_dates.txt"}, "account_report_data_header": {"cost_rate": "aveD7CostRate", "assesss_cost": "assessCostD7"}, "adgroup_report_data_header": {"life_long_days": "life_long_days", "up_trend": "up_trend", "is_ever_up": "is_ever_up", "today_prefix": "today", "past_1_days_prefix": "past_1day", "past_3_days_prefix": "past_3day", "past_7_days_prefix": "past_7day", "past_14_days_prefix": "past_14day", "cost_postfix": "cost", "uv_postfix": "uv", "step_1_stat_name": "biz_follow", "step_2_stat_name": "biz_reg", "step_3_stat_name": "reservation", "join_char": "_"}, "string_format": {"update_delivery_date": "updateDeliveryDate-"}}