#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Feature Filtering Module

This module provides comprehensive feature filtering based on:
1. Correlation analysis (target correlation and feature intercorrelation)
2. Variance analysis (low variance filter)
3. Weight of Evidence (WoE) analysis for categorical features

Author: Assistant
Date: 2025-01-08
"""

import pandas as pd
import numpy as np
import warnings
from typing import List, Dict, Tuple, Optional, Union
from scipy.stats import chi2_contingency
import matplotlib.pyplot as plt
import seaborn as sns


def calculate_woe_iv(df: pd.DataFrame, feature: str, target: str, bins: int = 10) -> Tuple[Dict, float]:
    """
    Calculate Weight of Evidence (WoE) and Information Value (IV) for a feature.
    
    Args:
        df: Input dataframe
        feature: Feature column name
        target: Target column name (binary: 0/1)
        bins: Number of bins for continuous variables
        
    Returns:
        Tuple of (WoE dictionary, IV value)
    """
    # Handle missing values
    df_clean = df[[feature, target]].dropna()
    
    if df_clean.empty:
        return {}, 0.0
    
    # Bin continuous variables
    if df_clean[feature].dtype in ['float64', 'int64'] and df_clean[feature].nunique() > bins:
        try:
            df_clean[feature + '_binned'] = pd.cut(df_clean[feature], bins=bins, duplicates='drop')
            feature_col = feature + '_binned'
        except ValueError:
            # If binning fails, treat as categorical
            feature_col = feature
    else:
        feature_col = feature
    
    # Calculate WoE and IV
    crosstab = pd.crosstab(df_clean[feature_col], df_clean[target])
    
    if crosstab.shape[1] != 2:
        warnings.warn(f"Target variable should be binary for WoE calculation. Feature: {feature}")
        return {}, 0.0
    
    # Add small constant to avoid division by zero
    crosstab = crosstab + 1e-6
    
    # Calculate percentages
    total_good = crosstab[0].sum()
    total_bad = crosstab[1].sum()
    
    woe_dict = {}
    iv = 0.0
    
    for category in crosstab.index:
        good = crosstab.loc[category, 0]
        bad = crosstab.loc[category, 1]
        
        good_rate = good / total_good
        bad_rate = bad / total_bad
        
        woe = np.log(good_rate / bad_rate)
        iv += (good_rate - bad_rate) * woe
        
        woe_dict[category] = woe
    
    return woe_dict, iv


def filter_features_comprehensive(
    df: pd.DataFrame,
    target_col: str,
    correlation_threshold: float = 0.95,
    target_correlation_min: float = 0.01,
    variance_threshold: float = 0.01,
    iv_threshold: float = 0.02,
    missing_threshold: float = 0.5,
    categorical_threshold: int = 20,
    verbose: bool = True
) -> Dict[str, Union[pd.DataFrame, List[str], Dict]]:
    """
    Comprehensive feature filtering based on correlation, variance, and WoE/IV.
    
    Args:
        df: Input dataframe
        target_col: Target column name
        correlation_threshold: Threshold for removing highly correlated features (default: 0.95)
        target_correlation_min: Minimum correlation with target to keep feature (default: 0.01)
        variance_threshold: Minimum variance threshold (default: 0.01)
        iv_threshold: Minimum Information Value threshold for categorical features (default: 0.02)
        missing_threshold: Maximum missing value ratio allowed (default: 0.5)
        categorical_threshold: Max unique values to treat as categorical (default: 20)
        verbose: Whether to print filtering progress
        
    Returns:
        Dictionary containing:
        - 'filtered_df': Filtered dataframe
        - 'selected_features': List of selected feature names
        - 'removed_features': Dictionary with removal reasons
        - 'feature_stats': Dictionary with feature statistics
        - 'woe_analysis': WoE analysis for categorical features
    """
    
    if target_col not in df.columns:
        raise ValueError(f"Target column '{target_col}' not found in dataframe")
    
    # Initialize results
    removed_features = {
        'missing_values': [],
        'low_variance': [],
        'low_target_correlation': [],
        'high_intercorrelation': [],
        'low_information_value': []
    }
    
    feature_stats = {}
    woe_analysis = {}
    
    # Get feature columns (exclude target)
    feature_cols = [col for col in df.columns if col != target_col]
    current_features = feature_cols.copy()
    
    if verbose:
        print(f"Starting feature filtering with {len(current_features)} features")
        print(f"Target column: {target_col}")
        print("=" * 60)
    
    # 1. Filter by missing values
    if verbose:
        print("1. Filtering by missing values...")
    
    for col in current_features.copy():
        missing_ratio = df[col].isnull().sum() / len(df)
        feature_stats[col] = {'missing_ratio': missing_ratio}
        
        if missing_ratio > missing_threshold:
            removed_features['missing_values'].append(col)
            current_features.remove(col)
            if verbose:
                print(f"   Removed {col}: {missing_ratio:.2%} missing values")
    
    if verbose:
        print(f"   Remaining features: {len(current_features)}")
    
    # 2. Filter by variance
    if verbose:
        print("\n2. Filtering by variance...")
    
    numeric_features = df[current_features].select_dtypes(include=[np.number]).columns.tolist()
    
    for col in numeric_features:
        if col in current_features:
            variance = df[col].var()
            feature_stats[col]['variance'] = variance
            
            if variance < variance_threshold:
                removed_features['low_variance'].append(col)
                current_features.remove(col)
                if verbose:
                    print(f"   Removed {col}: variance = {variance:.6f}")
    
    if verbose:
        print(f"   Remaining features: {len(current_features)}")
    
    # 3. Filter by target correlation
    if verbose:
        print("\n3. Filtering by target correlation...")
    
    numeric_features = [col for col in current_features if col in numeric_features]
    
    for col in numeric_features:
        if col in current_features:
            try:
                correlation = abs(df[col].corr(df[target_col]))
                feature_stats[col]['target_correlation'] = correlation
                
                if pd.isna(correlation) or correlation < target_correlation_min:
                    removed_features['low_target_correlation'].append(col)
                    current_features.remove(col)
                    if verbose:
                        print(f"   Removed {col}: target correlation = {correlation:.4f}")
            except Exception as e:
                if verbose:
                    print(f"   Warning: Could not calculate correlation for {col}: {e}")
    
    if verbose:
        print(f"   Remaining features: {len(current_features)}")
    
    # 4. Filter by intercorrelation
    if verbose:
        print("\n4. Filtering by intercorrelation...")
    
    numeric_features = [col for col in current_features if col in numeric_features]
    
    if len(numeric_features) > 1:
        corr_matrix = df[numeric_features].corr().abs()
        
        # Find highly correlated pairs
        high_corr_pairs = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                if corr_matrix.iloc[i, j] > correlation_threshold:
                    col1, col2 = corr_matrix.columns[i], corr_matrix.columns[j]
                    
                    # Keep the feature with higher target correlation
                    corr1 = abs(df[col1].corr(df[target_col]))
                    corr2 = abs(df[col2].corr(df[target_col]))
                    
                    if corr1 >= corr2:
                        remove_col = col2
                    else:
                        remove_col = col1
                    
                    if remove_col in current_features:
                        high_corr_pairs.append((col1, col2, corr_matrix.iloc[i, j]))
                        removed_features['high_intercorrelation'].append(remove_col)
                        current_features.remove(remove_col)
                        if verbose:
                            print(f"   Removed {remove_col}: correlated with {col1 if remove_col == col2 else col2} (r={corr_matrix.iloc[i, j]:.3f})")
    
    if verbose:
        print(f"   Remaining features: {len(current_features)}")
    
    # 5. WoE/IV analysis for categorical features
    if verbose:
        print("\n5. Analyzing categorical features with WoE/IV...")
    
    # Check if target is binary for WoE analysis
    target_unique = df[target_col].nunique()
    if target_unique == 2:
        categorical_features = []
        for col in current_features:
            if col not in numeric_features or df[col].nunique() <= categorical_threshold:
                categorical_features.append(col)
        
        for col in categorical_features:
            if col in current_features:
                try:
                    woe_dict, iv_value = calculate_woe_iv(df, col, target_col)
                    feature_stats[col]['information_value'] = iv_value
                    woe_analysis[col] = {
                        'woe_mapping': woe_dict,
                        'iv': iv_value,
                        'interpretation': get_iv_interpretation(iv_value)
                    }
                    
                    if iv_value < iv_threshold:
                        removed_features['low_information_value'].append(col)
                        current_features.remove(col)
                        if verbose:
                            print(f"   Removed {col}: IV = {iv_value:.4f} ({get_iv_interpretation(iv_value)})")
                    elif verbose:
                        print(f"   Kept {col}: IV = {iv_value:.4f} ({get_iv_interpretation(iv_value)})")
                        
                except Exception as e:
                    if verbose:
                        print(f"   Warning: Could not calculate WoE/IV for {col}: {e}")
    else:
        if verbose:
            print(f"   Skipping WoE/IV analysis: target has {target_unique} unique values (need binary target)")
    
    if verbose:
        print(f"   Remaining features: {len(current_features)}")
    
    # Create filtered dataframe
    filtered_df = df[[target_col] + current_features].copy()
    
    # Summary
    if verbose:
        print("\n" + "=" * 60)
        print("FEATURE FILTERING SUMMARY")
        print("=" * 60)
        print(f"Original features: {len(feature_cols)}")
        print(f"Selected features: {len(current_features)}")
        print(f"Removed features: {len(feature_cols) - len(current_features)}")
        print()
        for reason, features in removed_features.items():
            if features:
                print(f"{reason.replace('_', ' ').title()}: {len(features)} features")
        print()
        print("Selected features:")
        for i, feature in enumerate(current_features, 1):
            stats = feature_stats.get(feature, {})
            stats_str = []
            if 'target_correlation' in stats:
                stats_str.append(f"r={stats['target_correlation']:.3f}")
            if 'information_value' in stats:
                stats_str.append(f"IV={stats['information_value']:.3f}")
            stats_display = f" ({', '.join(stats_str)})" if stats_str else ""
            print(f"  {i:2d}. {feature}{stats_display}")
    
    return {
        'filtered_df': filtered_df,
        'selected_features': current_features,
        'removed_features': removed_features,
        'feature_stats': feature_stats,
        'woe_analysis': woe_analysis
    }


def get_iv_interpretation(iv_value: float) -> str:
    """
    Interpret Information Value according to standard guidelines.
    
    Args:
        iv_value: Information Value
        
    Returns:
        Interpretation string
    """
    if iv_value < 0.02:
        return "Not useful for prediction"
    elif iv_value < 0.1:
        return "Weak predictive power"
    elif iv_value < 0.3:
        return "Medium predictive power"
    elif iv_value < 0.5:
        return "Strong predictive power"
    else:
        return "Suspicious (too good to be true)"


def plot_feature_analysis(results: Dict, figsize: Tuple[int, int] = (15, 10)) -> None:
    """
    Plot feature analysis results.
    
    Args:
        results: Results dictionary from filter_features_comprehensive
        figsize: Figure size tuple
    """
    fig, axes = plt.subplots(2, 2, figsize=figsize)
    fig.suptitle('Feature Filtering Analysis', fontsize=16, fontweight='bold')
    
    # 1. Removal reasons pie chart
    removed_counts = {k: len(v) for k, v in results['removed_features'].items() if v}
    if removed_counts:
        axes[0, 0].pie(removed_counts.values(), labels=removed_counts.keys(), autopct='%1.1f%%')
        axes[0, 0].set_title('Features Removed by Reason')
    else:
        axes[0, 0].text(0.5, 0.5, 'No features removed', ha='center', va='center')
        axes[0, 0].set_title('Features Removed by Reason')
    
    # 2. Target correlation distribution
    correlations = [stats.get('target_correlation', 0) for stats in results['feature_stats'].values() 
                   if 'target_correlation' in stats]
    if correlations:
        axes[0, 1].hist(correlations, bins=20, alpha=0.7, edgecolor='black')
        axes[0, 1].set_title('Target Correlation Distribution')
        axes[0, 1].set_xlabel('Absolute Correlation')
        axes[0, 1].set_ylabel('Frequency')
    else:
        axes[0, 1].text(0.5, 0.5, 'No correlation data', ha='center', va='center')
        axes[0, 1].set_title('Target Correlation Distribution')
    
    # 3. Information Value distribution
    iv_values = [woe['iv'] for woe in results['woe_analysis'].values()]
    if iv_values:
        axes[1, 0].hist(iv_values, bins=20, alpha=0.7, edgecolor='black')
        axes[1, 0].set_title('Information Value Distribution')
        axes[1, 0].set_xlabel('Information Value')
        axes[1, 0].set_ylabel('Frequency')
        
        # Add IV interpretation lines
        axes[1, 0].axvline(x=0.02, color='red', linestyle='--', alpha=0.7, label='Weak threshold')
        axes[1, 0].axvline(x=0.1, color='orange', linestyle='--', alpha=0.7, label='Medium threshold')
        axes[1, 0].axvline(x=0.3, color='green', linestyle='--', alpha=0.7, label='Strong threshold')
        axes[1, 0].legend()
    else:
        axes[1, 0].text(0.5, 0.5, 'No IV data', ha='center', va='center')
        axes[1, 0].set_title('Information Value Distribution')
    
    # 4. Feature selection summary
    total_features = len(results['selected_features']) + sum(len(v) for v in results['removed_features'].values())
    selected_features = len(results['selected_features'])
    
    categories = ['Selected', 'Removed']
    values = [selected_features, total_features - selected_features]
    colors = ['green', 'red']
    
    axes[1, 1].bar(categories, values, color=colors, alpha=0.7)
    axes[1, 1].set_title('Feature Selection Summary')
    axes[1, 1].set_ylabel('Number of Features')
    
    # Add value labels on bars
    for i, v in enumerate(values):
        axes[1, 1].text(i, v + 0.5, str(v), ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.show()


# Example usage
if __name__ == "__main__":
    # Create sample data
    np.random.seed(42)
    n_samples = 1000
    
    # Generate sample dataset
    data = {
        'target': np.random.binomial(1, 0.3, n_samples),
        'feature1': np.random.normal(0, 1, n_samples),
        'feature2': np.random.normal(0, 1, n_samples),
        'feature3': np.random.normal(0, 0.01, n_samples),  # Low variance
        'feature4': np.random.choice(['A', 'B', 'C'], n_samples),  # Categorical
        'feature5': np.random.normal(0, 1, n_samples),
    }
    
    # Make feature1 correlated with target
    data['feature1'] = data['feature1'] + 0.5 * data['target']
    
    # Make feature2 highly correlated with feature1
    data['feature2'] = data['feature1'] + np.random.normal(0, 0.1, n_samples)
    
    # Add some missing values
    missing_indices = np.random.choice(n_samples, size=int(0.1 * n_samples), replace=False)
    data['feature5'] = np.array(data['feature5'])
    data['feature5'][missing_indices] = np.nan
    
    df = pd.DataFrame(data)
    
    print("Sample Dataset:")
    print(df.head())
    print(f"\nDataset shape: {df.shape}")
    print(f"Target distribution: {df['target'].value_counts().to_dict()}")
    
    # Apply feature filtering
    results = filter_features_comprehensive(
        df=df,
        target_col='target',
        correlation_threshold=0.9,
        target_correlation_min=0.05,
        variance_threshold=0.01,
        iv_threshold=0.02,
        verbose=True
    )
    
    print(f"\nFiltered dataset shape: {results['filtered_df'].shape}")
    
    # Plot analysis
    plot_feature_analysis(results)
