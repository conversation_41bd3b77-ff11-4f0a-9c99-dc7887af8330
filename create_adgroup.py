import datetime 
import pandas as pd 
import numpy as np
import random

agency_accounts_dict = {
    '派瑞': [], 
    '亿科': [], 
    '开域': [], 
    '云锐': []
}

agency_accounts_path_dict = {
    '派瑞': 'get_data/pairei_accounts.txt', 
    '亿科': 'get_data/yike_accounts.txt', 
    '开域': 'get_data/kaiyu_accounts.txt', 
    '云锐': 'get_data/yunrui_accounts.txt'
}


def get_good_dapan_adcreative():
    dapan_video_data = pd.read_csv('get_data/video_data.csv')
    dapan_video_url = pd.read_csv('get_data/video_url.csv')
    dapan_video_is_used = pd.read_csv('get_data/video_data_is_used.csv')
    dapan_video_data_merge = pd.merge(dapan_video_data, dapan_video_url, on = 'signature', how = 'left')
    dapan_video_data_merge = pd.merge(dapan_video_data_merge, dapan_video_is_used,  on = ['material_id', 'account_id'], how = 'left')
    # if account_id in youju_accounts:
    #     temp = dapan_video_data_merge[dapan_video_data_merge['account_id'].isin(youju_accounts)]
    #     temp = temp[~temp['account_id'].isin(ai_material_accounts)]
    #     temp = temp.sort_values('cost')
    # elif account_id in zyz_accounts:
    #     temp = dapan_video_data_merge[dapan_video_data_merge['account_id'].isin(zyz_accounts)]
    #     temp = temp[~temp['account_id'].isin(ai_material_accounts)]
    #     temp = temp.sort_values('cost')

    past_30day = str(datetime.datetime.today() + datetime.timedelta(days = -30)).split(' ')[0] # 限制数据日期
    temp = dapan_video_data_merge[dapan_video_data_merge['date']>=past_30day]

    material_data = {'account_id': [], 'adgroup_id': [], 'material_id':[], 'signature':[], \
                        'cost':[], 'reservation_uv':[], 'reservation_cost':[], \
                            'used':[], 'agency':[], 'project':[], 'offer':[]}
    material_group = temp.groupby('material_id')
    for name, group in material_group:
        cost = np.sum(group['cost']) * 0.01
        reservation_uv = np.sum(group['reservation_uv'])
        if reservation_uv:
            reservation_cost = cost/reservation_uv
        else:
            reservation_cost = 0
        material_data['account_id'].append(group['account_id'].iloc[0])
        material_data['adgroup_id'].append(group['adgroup_id'].iloc[0])
        material_data['material_id'].append(group['material_id'].iloc[0])
        material_data['signature'].append(group['signature'].iloc[0])
        material_data['agency'].append(group['agency'].iloc[0])
        material_data['project'].append(group['project'].iloc[0])
        # material_data['offer'].append(group['offer'].iloc[0])
        material_data['cost'].append(cost)
        material_data['reservation_uv'].append(reservation_uv)
        material_data['reservation_cost'].append(reservation_cost)
        if np.sum(group['used']) > 0:
            is_used = 1
        else:
            is_used = 0
        material_data['used'].append(is_used)
        
    material_data = pd.DataFrame(material_data)
    # material_data = material_data[material_data['offer'] == 'NO_OFFER'] # 仅无Offer
    # material_data = material_data[material_data['project'] == 'JK'] # 仅无Offer
    material_data = material_data.sort_values('cost', ascending = False)
    material_data = material_data.drop_duplicates(subset=['signature','material_id','account_id'])
   

    f_pairui = open(agency_accounts_path_dict.get('派瑞'), 'w')
    f_yike = open(agency_accounts_path_dict.get('亿科'), 'w')
    f_kaiyu = open(agency_accounts_path_dict.get('开域'), 'w')
    f_yunrui = open(agency_accounts_path_dict.get('云锐'), 'w')

    temp = material_data[['account_id', 'agency']]
    temp = temp.drop_duplicates()
    
    for i in range(len(temp)):
        if str(temp['agency'].iloc[i]) == 'PR':
            f_pairui.write(str(temp['account_id'].iloc[i]) + '\n')
        elif str(temp['agency'].iloc[i]) == 'YK':
            f_yike.write(str(temp['account_id'].iloc[i]) + '\n')
        elif str(temp['agency'].iloc[i]) == 'KY':
            f_kaiyu.write(str(temp['account_id'].iloc[i]) + '\n')
        elif str(temp['agency'].iloc[i]) == 'YR':
            f_yunrui.write(str(temp['account_id'].iloc[i]) + '\n')
    f_pairui.close()
    f_yike.close()
    f_kaiyu.close()
    f_yunrui.close()

    return material_data

dapan_good_material = get_good_dapan_adcreative()


def get_good_dapan_material_for_account(account_id, ai_material_accounts = []):
    
    global dapan_good_material

    print ('--- get dapan good material ...')

    # use materials from yike only 
    # temp = dapan_good_material[dapan_good_material['account_id'].isin(agency_accounts_dict.get('亿科'))]
    lines = open('get_data/亿科-账号.txt', encoding='gbk').readlines() # 亿科 only
    yike_accounts = []
    for line in lines:
        L = line.strip().split(' ')
        if L[0] not in yike_accounts and L[1] == '非朋': # 非朋 only
            yike_accounts.append(L[0])
    temp = dapan_good_material[dapan_good_material['account_id'].isin(yike_accounts)]
    
    temp = temp[~temp['account_id'].isin(ai_material_accounts)] # 去掉AI素材账号
    temp = temp.sort_values('cost', ascending = False) # 消耗排序
    top = temp[temp['cost']>5000] # 取top500
    median = temp[(temp['cost']<=5000)&(temp['cost']>1000)] # 取top500
    bottom = temp[(temp['cost']<=1000)&(temp['cost']>200)] # 取top500
    top = top.sample(frac = 1) # 打乱
    median = median.sample(frac = 1) # 打乱
    bottom = bottom.sample(frac = 1) # 打乱
    print (len(top), len(median), len(bottom))
    res = []
    if len(top):
        res.append(str(top['account_id'].iloc[0]) + '_' + str(top['material_id'].iloc[0])) # 取1个top
    for i in range(min(2, len(median))):
        res.append(str(median['account_id'].iloc[i]) + '_' + str(median['material_id'].iloc[i])) # 取2个median
    for i in range(min(5, len(bottom))):
        res.append(str(bottom['account_id'].iloc[i]) + '_' + str(bottom['material_id'].iloc[i])) # 取5个median
    print (res)
    return res


lines = open('get_data/全量非朋账号.txt', encoding='utf8').readlines()
feipeng_accounts = []
for line in lines:
    L = line.strip().split()
    if L[0] not in feipeng_accounts:
        feipeng_accounts.append(int(L[0]))

def get_good_account_adcreative():    
    global feipeng_accounts
    merchant_candidates = {}
    for merchant in ['亿科']:
        """lines = open('get_data/wyd_accounts.txt', encoding='utf8').readlines()
        good_accounts = []
        for line in lines:
            L = line.strip().split(' ')
            # if L[0] not in youju_no_offer_accounts and L[0] not in zyz_no_offer_accounts: # 只保留no-offer账户
            #     continue
            # if L[0] in ai_material_accounts:
            #    continue
            if L[1] == merchant:
                good_accounts.append(L[0])"""
        lines = open('get_data/亿科-账号.txt', encoding='gbk').readlines()
        good_accounts = []
        for line in lines:
            L = line.strip().split(' ')
            if L[0] not in good_accounts and L[1] == '非朋':
                good_accounts.append(L[0])
        good_accounts = feipeng_accounts

        past_7day = str(datetime.datetime.today() + datetime.timedelta(days = -7)).split(' ')[0]

        video_data = pd.read_csv('get_data/video_data.csv', encoding = 'gbk')
        material_data = {'account_id': [], 'adgroup_id': [], 'material_id':[], 'cost':[], 'reservation_uv':[], 'reservation_cost':[]}
        for account_id in good_accounts:
            account_data = video_data[video_data['account_id'] == int(account_id)]
            account_data = account_data[account_data['date'] >= past_7day]
            material_group = account_data.groupby('material_id')
            for name, group in material_group:
                cost = np.sum(group['cost']) * 0.01
                reservation_uv = np.sum(group['reservation_uv'])
                if reservation_uv:
                    reservation_cost = cost/reservation_uv
                else:
                    reservation_cost = 0
                material_data['account_id'].append(account_id)
                material_data['adgroup_id'].append(group['adgroup_id'].iloc[0])
                material_data['material_id'].append(group['material_id'].iloc[0])
                material_data['cost'].append(cost)
                material_data['reservation_uv'].append(reservation_uv)
                material_data['reservation_cost'].append(reservation_cost)
        material_data = pd.DataFrame(material_data)

        adgroup_group = material_data.groupby('adgroup_id')
        adgroup_data = {'account_id': [], 'adgroup_id': [], 'cost':[]}
        for name, group in adgroup_group:
            adgroup_data['account_id'].append(group['account_id'].iloc[0])
            adgroup_data['adgroup_id'].append(group['adgroup_id'].iloc[0])
            adgroup_data['cost'].append(np.sum(group['cost']))
        adgroup_data = pd.DataFrame(adgroup_data)

        adgroup_data = adgroup_data.sort_values('cost',ascending=False)
        adgroup_data = adgroup_data[adgroup_data['cost']>10000]
        candidates = []
        for i in range(len(adgroup_data)):
            account_id = adgroup_data['account_id'].iloc[i]
            adgroup_id = adgroup_data['adgroup_id'].iloc[i]
            materials = material_data[material_data['adgroup_id'] == adgroup_id]
            materials = materials.sort_values('cost',ascending=False)
            # materials = materials[materials['cost']>100]
            print ('--------------------')
            print (account_id, adgroup_id, adgroup_data['cost'].iloc[i], len(materials))
            item_list = []
            for j in range(len(materials)):
                item = str(account_id) + '_' + str(materials['material_id'].iloc[j])
                item_list.append(item)
                if len(item_list) == 8:
                    break
            if len(item_list) < 1:
                continue
            print (item_list)
            # break
            candidates.append(item_list)
        merchant_candidates[merchant] = candidates
    return merchant_candidates


good_candidates = get_good_account_adcreative()
print ('--- good candidates: ', good_candidates)
    
def copy_good_account_adcreative(account_id):
    global good_candidates
    for merchant in ['亿科']:
        good_candidates_temp = good_candidates.get(merchant, [])
    print ('--- candidates num: ', len(good_candidates_temp))
    if len(good_candidates_temp):
        idx = int(random.random() * len(good_candidates_temp))
        print (len(good_candidates_temp[idx]))
        return good_candidates_temp[idx]
    else:
        print ('--- no candidates ...')
        return []

account_video_data_merge = pd.read_csv('get_data/account_video_data_merge.csv')

def get_account_good_unused_adcreative(account_id):
    print ('---', account_id)
    print ('--- check own good candidate ...')
    account_data = account_video_data_merge[account_video_data_merge['account_id'] == int(account_id)]
    print ('account_data len: ', len(account_data))
    account_data = account_data[account_data['used'] == 0]
    account_data = account_data[account_data['reservation_uv'] > 0]
    account_data = account_data[account_data['reservation_cost']*0.01 < 500]
    if len(account_data) == 0:
        print ('--- no good candidates')
        return []
    else:
        res = []
        for material_id in list(account_data['material_id']):
            try:
                res.append(account_id + '_' + str(material_id))
            except:
                print (account_data)
        print (res)
        ind = int(random.random() * len(res))
        return [res[ind]]
